'use client'

import { useState } from "react";
import { ProjectSidebar } from "@/components/project-sidebar";
import { PromptWorkspace } from "@/components/prompt-workspace";
import { ContextSidebar } from "@/components/context-sidebar";
import { AuthGuard } from "@/components/auth-guard";
import { Button } from "@/components/ui/button";
import { Menu, Settings } from "lucide-react";
import { useAppStore } from "@/store/app-store";

export default function Dashboard() {
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const [isMobileContextOpen, setIsMobileContextOpen] = useState(false);
  const [isContextGalleryOpen, setIsContextGalleryOpen] = useState(false);

  // Zustand store for sidebar collapse states
  const {
    isProjectSidebarCollapsed,
    setIsProjectSidebarCollapsed,
    isContextSidebarCollapsed,
    setIsContextSidebarCollapsed
  } = useAppStore();

  return (
    <AuthGuard>
      <div className="flex full-height-mobile bg-gray-50 relative">
        {/* Mobile Overlay */}
        {(isMobileSidebarOpen || isMobileContextOpen) && (
          <div 
            className="fixed inset-0 bg-black/50 z-40 lg:hidden mobile-transition"
            onClick={() => {
              setIsMobileSidebarOpen(false);
              setIsMobileContextOpen(false);
            }}
          />
        )}

        {/* Sol Sütun - Proje Listesi */}
        <div className={`
          fixed lg:relative inset-y-0 left-0 z-50
          w-[85vw] sm:w-80
          ${isProjectSidebarCollapsed ? 'lg:w-16' : 'lg:w-80'}
          transform transition-all duration-300 ease-in-out
          lg:transform-none lg:translate-x-0
          border-r border-gray-200 bg-white
          ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
          ${isMobileSidebarOpen ? 'block' : 'hidden lg:block'}
        `}>
          <ProjectSidebar
            onClose={() => setIsMobileSidebarOpen(false)}
            isCollapsed={isProjectSidebarCollapsed}
            onToggleCollapse={() => setIsProjectSidebarCollapsed(!isProjectSidebarCollapsed)}
          />
        </div>

        {/* Orta Sütun - Prompt Workspace */}
        <div className="flex-1 flex flex-col min-w-0">
          {/* Mobile Header */}
          <div className="lg:hidden flex items-center justify-between p-4 bg-white border-b border-gray-200 safe-area-top">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileSidebarOpen(true)}
              className="flex items-center gap-2 touch-target focus-visible-enhanced"
            >
              <Menu className="h-5 w-5" />
              <span className="font-medium mobile-text-base">Projeler</span>
            </Button>
            
            <h1 className="text-lg font-semibold text-gray-900 mobile-text-base">Promptbir</h1>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileContextOpen(true)}
              className="flex items-center gap-2 touch-target focus-visible-enhanced"
            >
              <Settings className="h-5 w-5" />
              <span className="font-medium mobile-text-base">Ayarlar</span>
            </Button>
          </div>

          <PromptWorkspace
            isContextGalleryOpen={isContextGalleryOpen}
            onToggleContextGallery={() => setIsContextGalleryOpen(!isContextGalleryOpen)}
          />
        </div>

        {/* Sağ Sütun - Context Sidebar */}
        <div className={`
          fixed xl:relative inset-y-0 right-0 z-50
          w-[90vw] sm:w-96
          ${isContextSidebarCollapsed ? 'xl:w-16' : 'xl:w-96'}
          transform transition-all duration-300 ease-in-out
          xl:transform-none xl:translate-x-0
          border-l border-gray-200 bg-white
          ${isMobileContextOpen ? 'translate-x-0' : 'translate-x-full xl:translate-x-0'}
          ${isMobileContextOpen ? 'block' : 'hidden xl:block'}
        `}>
          <ContextSidebar
            onClose={() => setIsMobileContextOpen(false)}
            isCollapsed={isContextSidebarCollapsed}
            onToggleCollapse={() => setIsContextSidebarCollapsed(!isContextSidebarCollapsed)}
            isContextGalleryOpen={isContextGalleryOpen}
            onToggleContextGallery={() => setIsContextGalleryOpen(!isContextGalleryOpen)}
          />
        </div>
      </div>
    </AuthGuard>
  );
}
