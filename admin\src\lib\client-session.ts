/**
 * Client-Side Session Persistence Utilities
 * Ensures session is properly maintained during navigation and page transitions
 */

import { supabase } from './supabase';

/**
 * Force session persistence before navigation
 */
export async function ensureSessionPersistence(): Promise<boolean> {
  console.log('🔄 Ensuring session persistence...');
  
  try {
    // Get current session
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('❌ Session persistence check failed:', error);
      return false;
    }
    
    if (!session) {
      console.log('❌ No session to persist');
      return false;
    }
    
    // Verify session is in localStorage
    const supabaseProject = process.env.NEXT_PUBLIC_SUPABASE_URL?.split('//')[1].split('.')[0];
    const storageKey = `sb-${supabaseProject}-auth-token`;
    const storedSession = localStorage.getItem(storageKey);
    
    console.log('📦 Session persistence check:', {
      hasSession: !!session,
      hasStoredSession: !!storedSession,
      storageKey,
      userId: session.user?.id
    });
    
    if (!storedSession) {
      console.warn('⚠️ Session not in localStorage, forcing storage...');
      
      // Force session refresh to trigger storage
      const { data: refreshedSession, error: refreshError } = await supabase.auth.refreshSession();
      
      if (refreshError) {
        console.error('❌ Session refresh failed:', refreshError);
        return false;
      }
      
      if (refreshedSession.session) {
        console.log('✅ Session refreshed and stored');
        // Wait a bit for storage to complete
        await new Promise(resolve => setTimeout(resolve, 200));
        return true;
      }
    }
    
    console.log('✅ Session persistence verified');
    return true;
    
  } catch (error) {
    console.error('❌ Session persistence error:', error);
    return false;
  }
}

/**
 * Safe navigation with session persistence
 */
export async function navigateWithSession(url: string): Promise<void> {
  console.log('🚀 Navigating with session persistence to:', url);
  
  const sessionPersisted = await ensureSessionPersistence();
  
  if (!sessionPersisted) {
    console.warn('⚠️ Session persistence failed, but proceeding with navigation');
  }
  
  // Add a small delay to ensure session is fully persisted
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // Use window.location for full page navigation to ensure cookies are set
  window.location.href = url;
}

/**
 * Monitor session state during app lifecycle
 */
export function setupSessionMonitoring() {
  console.log('🔍 Setting up session monitoring...');
  
  // Monitor visibility changes
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      console.log('👁️ Page became visible, checking session...');
      checkSessionHealth();
    }
  });
  
  // Monitor page focus
  window.addEventListener('focus', () => {
    console.log('🎯 Window focused, checking session...');
    checkSessionHealth();
  });
  
  // Monitor before unload
  window.addEventListener('beforeunload', () => {
    console.log('🚪 Page unloading, session state preserved');
  });
}

/**
 * Check session health
 */
async function checkSessionHealth() {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    console.log('💓 Session health check:', {
      hasSession: !!session,
      error: error?.message,
      userId: session?.user?.id,
      expiresAt: session?.expires_at
    });
    
    if (session && session.expires_at) {
      const expiresAt = new Date(session.expires_at * 1000);
      const now = new Date();
      const timeUntilExpiry = expiresAt.getTime() - now.getTime();
      
      console.log('⏰ Session expires in:', Math.round(timeUntilExpiry / 1000 / 60), 'minutes');
      
      // If session expires in less than 5 minutes, refresh it
      if (timeUntilExpiry < 5 * 60 * 1000) {
        console.log('🔄 Session expiring soon, refreshing...');
        await supabase.auth.refreshSession();
      }
    }
    
  } catch (error) {
    console.error('❌ Session health check failed:', error);
  }
}

/**
 * Initialize session persistence system
 */
export function initializeSessionPersistence() {
  console.log('🚀 Initializing session persistence system...');
  
  // Only run in browser
  if (typeof window === 'undefined') {
    return;
  }
  
  // Setup monitoring
  setupSessionMonitoring();
  
  // Initial session check
  setTimeout(() => {
    checkSessionHealth();
  }, 1000);
  
  console.log('✅ Session persistence system initialized');
}

/**
 * Debug session storage state
 */
export function debugSessionStorage() {
  if (typeof window === 'undefined') {
    console.log('❌ Not in browser environment');
    return;
  }
  
  const supabaseProject = process.env.NEXT_PUBLIC_SUPABASE_URL?.split('//')[1].split('.')[0];
  const storageKey = `sb-${supabaseProject}-auth-token`;
  
  console.log('🔍 Session storage debug:');
  console.log('Storage key:', storageKey);
  
  try {
    const storedSession = localStorage.getItem(storageKey);
    console.log('Stored session:', storedSession ? 'present' : 'missing');
    
    if (storedSession) {
      try {
        const parsed = JSON.parse(storedSession);
        console.log('Session data:', {
          hasAccessToken: !!parsed.access_token,
          hasRefreshToken: !!parsed.refresh_token,
          hasUser: !!parsed.user,
          expiresAt: parsed.expires_at
        });
      } catch (parseError) {
        console.log('❌ Failed to parse stored session:', parseError);
      }
    }
    
    // Check all localStorage keys
    const allKeys = Object.keys(localStorage);
    const supabaseKeys = allKeys.filter(key => key.includes('supabase') || key.includes('sb-'));
    console.log('All Supabase keys in localStorage:', supabaseKeys);
    
  } catch (error) {
    console.error('❌ Session storage debug failed:', error);
  }
}
