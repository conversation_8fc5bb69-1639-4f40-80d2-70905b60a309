import { createClient } from '@supabase/supabase-js';
import { createBrowserClient } from '@supabase/ssr';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl) {
  throw new Error('Missing environment variable: NEXT_PUBLIC_SUPABASE_URL');
}

if (!supabaseAnonKey) {
  throw new Error('Missing environment variable: NEXT_PUBLIC_SUPABASE_ANON_KEY');
}

// Browser client for client-side operations with proper SSR handling
export const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    // Use default storage to ensure cookie/localStorage sync
    storage: typeof window !== 'undefined' ? window.localStorage : undefined,
    debug: process.env.NODE_ENV === 'development'
  },
  global: {
    headers: {
      'X-Client-Info': 'admin-panel@1.0.0'
    }
  }
});

// Enhanced auth state change handler for better error handling and debugging
if (typeof window !== 'undefined') {
  supabase.auth.onAuthStateChange((event, session) => {
    console.log('🔐 Admin auth state change:', event, {
      userId: session?.user?.id,
      hasSession: !!session,
      accessToken: session?.access_token ? 'present' : 'missing',
      refreshToken: session?.refresh_token ? 'present' : 'missing'
    });

    // Debug session storage
    const supabaseProject = supabaseUrl.split('//')[1].split('.')[0];
    const storageKey = `sb-${supabaseProject}-auth-token`;
    const storedSession = localStorage.getItem(storageKey);
    console.log('📦 Session storage status:', {
      storageKey,
      hasStoredSession: !!storedSession,
      storedSessionLength: storedSession?.length || 0
    });

    if (event === 'SIGNED_OUT') {
      console.log('🚪 Signing out - clearing storage');
      // Clear all Supabase storage keys
      localStorage.removeItem(storageKey);
      localStorage.removeItem('sb-admin-auth-token'); // Legacy cleanup
      // Clear any cached admin data
      if (typeof window !== 'undefined' && window.location.pathname !== '/') {
        window.location.href = '/';
      }
    }

    if (event === 'TOKEN_REFRESHED') {
      console.log('🔄 Admin token refreshed successfully');
    }

    if (event === 'SIGNED_IN') {
      console.log('✅ Admin signed in:', session?.user?.email);
      // Verify session is properly stored
      setTimeout(() => {
        const verifySession = localStorage.getItem(storageKey);
        console.log('🔍 Post-signin session verification:', {
          hasStoredSession: !!verifySession,
          sessionLength: verifySession?.length || 0
        });
      }, 100);
    }

    if (event === 'INITIAL_SESSION') {
      console.log('🎯 Initial session loaded:', {
        hasSession: !!session,
        userId: session?.user?.id
      });
    }
  });
}

// Admin client with Service Role Key (server-side only)
export const supabaseAdmin = supabaseServiceRoleKey 
  ? createClient(supabaseUrl, supabaseServiceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      },
      global: {
        headers: {
          'X-Client-Info': 'admin-panel-server@1.0.0'
        }
      }
    })
  : null;

// Auth helper functions
export const getUser = async () => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) {
      console.error('Auth error:', error);
      return null;
    }
    return user;
  } catch (error) {
    console.error('Failed to get user:', error);
    return null;
  }
};

// Session verification helper
export const waitForSessionEstablishment = async (maxAttempts = 10, delayMs = 500): Promise<boolean> => {
  console.log('🔄 Waiting for session establishment...');

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();

      if (session && !error) {
        console.log(`✅ Session established on attempt ${attempt}`);

        // Verify session is also in storage
        const supabaseProject = supabaseUrl.split('//')[1].split('.')[0];
        const storageKey = `sb-${supabaseProject}-auth-token`;
        const storedSession = localStorage.getItem(storageKey);

        if (storedSession) {
          console.log('✅ Session confirmed in localStorage');
          return true;
        } else {
          console.log(`⏳ Session in memory but not in storage, attempt ${attempt}/${maxAttempts}`);
        }
      } else {
        console.log(`⏳ No session found, attempt ${attempt}/${maxAttempts}`, error?.message);
      }
    } catch (error) {
      console.log(`❌ Session check failed, attempt ${attempt}/${maxAttempts}:`, error);
    }

    if (attempt < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  }

  console.log('❌ Session establishment timeout');
  return false;
};

export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error('Sign out error:', error);
      return { error };
    }
    return { error: null };
  } catch (error) {
    console.error('Failed to sign out:', error);
    return { error };
  }
};

// Admin user check - uses regular client, not admin API
export const isAdmin = async (userId: string) => {
  try {
    if (!userId) return false;
    
    // Check admin_users table instead of using Admin API
    const { data, error } = await supabase
      .from('admin_users')
      .select('user_id, is_active')
      .eq('user_id', userId)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Admin check error:', error);
      return false;
    }

    return !!data;
  } catch (error) {
    console.error('Failed to check admin status:', error);
    return false;
  }
};

// Get user profile with admin status
export const getUserProfile = async (userId: string) => {
  try {
    if (!userId) return null;

    // Get user info from auth.users via RPC or direct query
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('User profile error:', userError);
      return null;
    }

    // Check admin status
    const adminStatus = await isAdmin(userId);

    return {
      ...userData,
      is_admin: adminStatus
    };
  } catch (error) {
    console.error('Failed to get user profile:', error);
    return null;
  }
};

// Database types
export interface Database {
  public: {
    Tables: {
      projects: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          context_text: string;
          description: string | null;
          status: 'active' | 'inactive' | 'archived';
          tags: string[];
          is_public: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name: string;
          context_text?: string;
          description?: string;
          status?: 'active' | 'inactive' | 'archived';
          tags?: any[];
          is_public?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          context_text?: string;
          description?: string;
          status?: 'active' | 'inactive' | 'archived';
          tags?: any[];
          is_public?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      prompts: {
        Row: {
          id: string;
          project_id: string;
          user_id: string;
          prompt_text: string;
          title: string | null;
          description: string | null;
          category: string | null;
          tags: any[];
          order_index: number;
          is_used: boolean;
          is_favorite: boolean;
          usage_count: number;
          last_used_at: string | null;
          task_code: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          project_id: string;
          user_id: string;
          prompt_text: string;
          title?: string;
          description?: string;
          category?: string;
          tags?: any[];
          order_index: number;
          is_used?: boolean;
          is_favorite?: boolean;
          usage_count?: number;
          last_used_at?: string;
          task_code?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          project_id?: string;
          user_id?: string;
          prompt_text?: string;
          title?: string;
          description?: string;
          category?: string;
          tags?: any[];
          order_index?: number;
          is_used?: boolean;
          is_favorite?: boolean;
          usage_count?: number;
          last_used_at?: string;
          task_code?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      admin_users: {
        Row: {
          id: string;
          user_id: string;
          role: 'admin' | 'super_admin';
          permissions: any;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          role?: 'admin' | 'super_admin';
          permissions?: any;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          role?: 'admin' | 'super_admin';
          permissions?: any;
          created_at?: string;
          updated_at?: string;
        };
      };
      admin_logs: {
        Row: {
          id: string;
          admin_id: string;
          action: string;
          resource_type: string;
          resource_id: string | null;
          old_values: any | null;
          new_values: any | null;
          ip_address: string | null;
          user_agent: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          admin_id: string;
          action: string;
          resource_type: string;
          resource_id?: string;
          old_values?: any;
          new_values?: any;
          ip_address?: string;
          user_agent?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          admin_id?: string;
          action?: string;
          resource_type?: string;
          resource_id?: string;
          old_values?: any;
          new_values?: any;
          ip_address?: string;
          user_agent?: string;
          created_at?: string;
        };
      };
      system_settings: {
        Row: {
          id: string;
          key: string;
          value: any;
          description: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          key: string;
          value: any;
          description?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          key?: string;
          value?: any;
          description?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      user_sessions: {
        Row: {
          id: string;
          user_id: string;
          session_token: string;
          ip_address: string | null;
          user_agent: string | null;
          location_country: string | null;
          location_city: string | null;
          is_active: boolean;
          last_activity_at: string;
          created_at: string;
          expires_at: string | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          session_token: string;
          ip_address?: string;
          user_agent?: string;
          location_country?: string;
          location_city?: string;
          is_active?: boolean;
          last_activity_at?: string;
          created_at?: string;
          expires_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          session_token?: string;
          ip_address?: string;
          user_agent?: string;
          location_country?: string;
          location_city?: string;
          is_active?: boolean;
          last_activity_at?: string;
          created_at?: string;
          expires_at?: string;
        };
      };
      analytics: {
        Row: {
          id: string;
          user_id: string | null;
          event_type: string;
          event_data: any;
          session_id: string | null;
          ip_address: string | null;
          user_agent: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id?: string;
          event_type: string;
          event_data?: any;
          session_id?: string;
          ip_address?: string;
          user_agent?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          event_type?: string;
          event_data?: any;
          session_id?: string;
          ip_address?: string;
          user_agent?: string;
          created_at?: string;
        };
      };
    };
  };
} 