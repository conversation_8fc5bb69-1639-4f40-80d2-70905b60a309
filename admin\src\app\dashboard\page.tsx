'use client';

import { memo, useMemo } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Card,
  CardBody,
  VStack,
  HStack,
  Icon,
  useColorModeValue,
  SimpleGrid,
  Button,
} from '@chakra-ui/react';
import { useDashboardStats } from '@/hooks/use-dashboard';
import { AdminLayout } from '@/components/admin-layout';
import { StatsCard } from '@/components/optimized/stats-card';
import {
  Users,
  FileText,
  Activity,
  BarChart3,
} from 'lucide-react';



const AdminDashboard = memo(function AdminDashboard() {
  // Use optimized React Query hooks instead of manual state management
  const { data: stats, isLoading: statsLoading, error } = useDashboardStats();

  // Move hooks to top level - fix React hooks violation
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');

  // Memoize color values to prevent unnecessary recalculations
  const colors = useMemo(() => ({
    bgColor,
    cardBg,
  }), [bgColor, cardBg]);

  const isLoading = statsLoading;

  // Error state
  if (error) {
    const errorMessage = error instanceof Error ? error.message : 'Bilinmeyen hata';
    const isAuthError = errorMessage.includes('Admin access required') ||
                       errorMessage.includes('Unauthorized') ||
                       errorMessage.includes('401') ||
                       errorMessage.includes('403');

    return (
      <AdminLayout>
        <Box minH="100vh" bg={colors.bgColor} display="flex" alignItems="center" justifyContent="center">
          <VStack spacing={4} textAlign="center">
            <Text color="red.500" fontSize="lg" fontWeight="bold">
              {isAuthError ? 'Yetkilendirme Hatası' : 'Veri Yükleme Hatası'}
            </Text>
            <Text color="gray.600" maxW="md">
              {isAuthError
                ? 'Admin paneline erişim yetkiniz bulunmuyor. Lütfen tekrar giriş yapın.'
                : `Veriler yüklenirken hata oluştu: ${errorMessage}`
              }
            </Text>
            {isAuthError && (
              <Button colorScheme="blue" onClick={() => window.location.href = '/'}>
                Giriş Sayfasına Dön
              </Button>
            )}
          </VStack>
        </Box>
      </AdminLayout>
    );
  }



  if (isLoading) {
    return (
      <AdminLayout>
        <Box minH="100vh" bg={colors.bgColor} display="flex" alignItems="center" justifyContent="center">
          <Text>Yükleniyor...</Text>
        </Box>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Container maxW="7xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Welcome Section */}
          <Box>
            <Heading size="xl" mb={2}>
              Hoş Geldiniz! 👋
            </Heading>
            <Text color="gray.600" fontSize="lg">
              PromptFlow yönetim paneline genel bakış
            </Text>
          </Box>

          {/* Stats Cards */}
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
            <StatsCard
              title="Toplam Kullanıcı"
              value={stats?.totalUsers || 0}
              icon={Users}
              color="blue.600"
              growth={stats?.userGrowth}
              cardBg={colors.cardBg}
            />

            <StatsCard
              title="Toplam Proje"
              value={stats?.totalProjects || 0}
              icon={FileText}
              color="green.600"
              growth={stats?.projectGrowth}
              cardBg={colors.cardBg}
            />

            <StatsCard
              title="Toplam Prompt"
              value={stats?.totalPrompts || 0}
              icon={BarChart3}
              color="purple.600"
              growth={15.2}
              cardBg={colors.cardBg}
            />

            <StatsCard
              title="Aktif Kullanıcı"
              value={stats?.activeUsers || 0}
              icon={Activity}
              color="orange.600"
              helpText="Son 24 saat"
              cardBg={colors.cardBg}
            />
          </SimpleGrid>
        </VStack>
      </Container>
    </AdminLayout>
  );
});

export default AdminDashboard;