'use client'

import { useState, useEffect, useCallback } from 'react'

interface DynamicHeightConfig {
  /** Minimum height in pixels */
  minHeight?: number
  /** Maximum height as fraction of viewport height (0.33 = 1/3) */
  maxHeightFraction?: number
  /** Base height in pixels when content is minimal */
  baseHeight?: number
  /** Enable smooth transitions */
  enableTransitions?: boolean
}

interface DynamicHeightReturn {
  /** Current maximum height in pixels */
  maxHeight: number
  /** Current minimum height in pixels */
  minHeight: number
  /** CSS style object for textarea */
  heightStyle: React.CSSProperties
  /** Function to manually trigger height recalculation */
  recalculateHeight: () => void
  /** Current viewport height */
  viewportHeight: number
}

/**
 * Custom hook for dynamic textarea height management
 * Calculates optimal height based on viewport size and content
 */
export function useDynamicHeight(config: DynamicHeightConfig = {}): DynamicHeightReturn {
  const {
    minHeight = 44,
    maxHeightFraction = 0.33, // 1/3 of viewport height
    baseHeight: _baseHeight = 56, // Unused for now
    enableTransitions = true
  } = config

  const [viewportHeight, setViewportHeight] = useState(0)
  const [maxHeight, setMaxHeight] = useState(200) // Default fallback

  // Calculate heights based on viewport
  const calculateHeights = useCallback(() => {
    if (typeof window === 'undefined') return

    const vh = window.innerHeight
    const calculatedMaxHeight = Math.floor(vh * maxHeightFraction)
    
    // Ensure minimum constraints
    const finalMaxHeight = Math.max(calculatedMaxHeight, minHeight + 50)
    
    setViewportHeight(vh)
    setMaxHeight(finalMaxHeight)
  }, [maxHeightFraction, minHeight])

  // Handle viewport resize with debouncing
  useEffect(() => {
    if (typeof window === 'undefined') return

    let timeoutId: NodeJS.Timeout

    const handleResize = () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(calculateHeights, 150) // Debounce for performance
    }

    // Initial calculation
    calculateHeights()

    // Listen for resize events
    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleResize)

    // Cleanup
    return () => {
      clearTimeout(timeoutId)
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleResize)
    }
  }, [calculateHeights])

  // Handle dynamic viewport height changes (mobile browsers)
  useEffect(() => {
    if (typeof window === 'undefined') return

    const handleVisualViewportChange = () => {
      if (window.visualViewport) {
        const vh = window.visualViewport.height
        const calculatedMaxHeight = Math.floor(vh * maxHeightFraction)
        const finalMaxHeight = Math.max(calculatedMaxHeight, minHeight + 50)
        
        setViewportHeight(vh)
        setMaxHeight(finalMaxHeight)
      }
    }

    // Support for Visual Viewport API (better mobile support)
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleVisualViewportChange)
      return () => {
        window.visualViewport?.removeEventListener('resize', handleVisualViewportChange)
      }
    }
  }, [maxHeightFraction, minHeight])

  // Generate CSS style object
  const heightStyle: React.CSSProperties = {
    minHeight: `${minHeight}px`,
    maxHeight: `${maxHeight}px`,
    height: 'auto',
    resize: 'none' as const,
    ...(enableTransitions && {
      transition: 'max-height 0.2s ease-out, min-height 0.2s ease-out'
    })
  }

  return {
    maxHeight,
    minHeight,
    heightStyle,
    recalculateHeight: calculateHeights,
    viewportHeight
  }
}

/**
 * Responsive breakpoint-aware dynamic height hook
 * Provides different height configurations for different screen sizes
 */
export function useResponsiveDynamicHeight(): DynamicHeightReturn {
  const [breakpoint, setBreakpoint] = useState<'mobile' | 'tablet' | 'desktop'>('desktop')

  // Detect current breakpoint
  useEffect(() => {
    if (typeof window === 'undefined') return

    const updateBreakpoint = () => {
      const width = window.innerWidth
      if (width < 640) {
        setBreakpoint('mobile')
      } else if (width < 1024) {
        setBreakpoint('tablet')
      } else {
        setBreakpoint('desktop')
      }
    }

    updateBreakpoint()
    window.addEventListener('resize', updateBreakpoint)
    
    return () => window.removeEventListener('resize', updateBreakpoint)
  }, [])

  // Configure height based on breakpoint with optimized values
  const config: DynamicHeightConfig = {
    minHeight: breakpoint === 'mobile' ? 48 : breakpoint === 'tablet' ? 44 : 56,
    maxHeightFraction: breakpoint === 'mobile' ? 0.25 : 0.33, // 1/4 on mobile, 1/3 on tablet/desktop
    baseHeight: breakpoint === 'mobile' ? 48 : breakpoint === 'tablet' ? 44 : 56,
    enableTransitions: true
  }

  return useDynamicHeight(config)
}

/**
 * Auto-expanding textarea hook with dynamic height constraints
 * Automatically adjusts height based on content while respecting viewport limits
 */
export function useAutoExpandingHeight(
  textareaRef: React.RefObject<HTMLTextAreaElement | null>,
  content: string,
  config?: DynamicHeightConfig
) {
  const { heightStyle, maxHeight, minHeight } = useDynamicHeight(config)
  const [isExpanding, setIsExpanding] = useState(false)

  // Auto-expand based on content with smooth animations
  useEffect(() => {
    const textarea = textareaRef.current
    if (!textarea) return

    // Get current height before changes
    const currentHeight = textarea.offsetHeight

    // Reset height to calculate scroll height
    textarea.style.height = 'auto'

    // Calculate optimal height
    const scrollHeight = textarea.scrollHeight
    const optimalHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight)

    // Check if height is changing significantly
    const heightDifference = Math.abs(optimalHeight - currentHeight)

    if (heightDifference > 10) {
      setIsExpanding(true)

      // Add expanding class for animation
      textarea.classList.add('expanding')

      // Remove animation class after animation completes
      setTimeout(() => {
        textarea.classList.remove('expanding')
        setIsExpanding(false)
      }, 200)
    }

    // Apply calculated height
    textarea.style.height = `${optimalHeight}px`
  }, [content, maxHeight, minHeight, textareaRef])

  return {
    heightStyle: {
      ...heightStyle,
      overflow: 'hidden' as const
    },
    maxHeight,
    minHeight,
    isExpanding
  }
}
