/**
 * Environment Variables Security Checker for Admin Panel
 * Ensures sensitive data is not exposed to client-side
 */

// Copy of the security checker for admin panel
const SENSITIVE_ENV_VARS = [
  'SUPABASE_SERVICE_ROLE_KEY',
  'SUPABASE_JWT_SECRET',
  'DATABASE_URL',
  'NEXTAUTH_SECRET',
  'OPENAI_API_KEY',
  'STRIPE_SECRET_KEY',
  'WEBHOOK_SECRET',
  'PRIVATE_KEY',
  'SECRET_KEY',
  'API_SECRET',
  'ADMIN_PASSWORD',
  'DB_PASSWORD',
  'ENCRYPTION_KEY'
] as const

const ALLOWED_CLIENT_ENV_VARS = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'NEXT_PUBLIC_APP_URL',
  'NEXT_PUBLIC_ENVIRONMENT',
  'NEXT_PUBLIC_ANALYTICS_ID',
  'NEXT_PUBLIC_SENTRY_DSN'
] as const

interface SecurityCheckResult {
  isSecure: boolean
  violations: string[]
  warnings: string[]
  recommendations: string[]
}

export function checkEnvironmentSecurity(): SecurityCheckResult {
  const violations: string[] = []
  const warnings: string[] = []
  const recommendations: string[] = []

  const isClientSide = typeof window !== 'undefined'

  if (isClientSide) {
    console.log('🔍 [ADMIN_ENV_SECURITY] Running client-side environment security check...')

    SENSITIVE_ENV_VARS.forEach(varName => {
      if (process.env[varName]) {
        violations.push(`CRITICAL: ${varName} is exposed to admin client-side code`)
      }
    })

    Object.keys(process.env).forEach(key => {
      if (!key.startsWith('NEXT_PUBLIC_') && 
          !key.startsWith('NODE_') && 
          !key.startsWith('npm_') &&
          key !== '__NEXT_PRIVATE_PREBUNDLED_REACT') {
        warnings.push(`Admin variable ${key} is available on client but doesn't follow NEXT_PUBLIC_ convention`)
      }
    })

    const requiredClientVars = ['NEXT_PUBLIC_SUPABASE_URL', 'NEXT_PUBLIC_SUPABASE_ANON_KEY']
    requiredClientVars.forEach(varName => {
      if (!process.env[varName]) {
        violations.push(`Required admin client variable ${varName} is missing`)
      }
    })

  } else {
    console.log('🔍 [ADMIN_ENV_SECURITY] Running server-side environment security check...')

    const requiredServerVars = ['NEXT_PUBLIC_SUPABASE_URL', 'NEXT_PUBLIC_SUPABASE_ANON_KEY']
    requiredServerVars.forEach(varName => {
      if (!process.env[varName]) {
        violations.push(`Required admin server variable ${varName} is missing`)
      }
    })

    if (process.env.NEXT_PUBLIC_SUPABASE_URL?.includes('localhost') && 
        process.env.NODE_ENV === 'production') {
      warnings.push('Admin panel using localhost Supabase URL in production environment')
    }

    if (process.env.NODE_ENV === 'production') {
      if (process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.includes('test') ||
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.includes('dev')) {
        warnings.push('Admin panel potentially using development keys in production')
      }
    }
  }

  if (process.env.NODE_ENV !== 'production') {
    recommendations.push('Use .env.local for admin panel local development secrets')
    recommendations.push('Never commit admin .env files to version control')
    recommendations.push('Use different admin keys for development and production')
  }

  recommendations.push('Regularly rotate admin API keys and secrets')
  recommendations.push('Use environment-specific admin configurations')
  recommendations.push('Monitor for exposed admin secrets in client bundles')

  const isSecure = violations.length === 0

  return {
    isSecure,
    violations,
    warnings,
    recommendations
  }
}

export function performRuntimeSecurityCheck(): void {
  const result = checkEnvironmentSecurity()

  if (!result.isSecure) {
    console.error('🚨 [ADMIN_ENV_SECURITY] CRITICAL SECURITY VIOLATIONS DETECTED:')
    result.violations.forEach(violation => {
      console.error(`  ❌ ${violation}`)
    })
    
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Critical admin environment security violations detected in production')
    }
  }

  if (result.warnings.length > 0) {
    console.warn('⚠️ [ADMIN_ENV_SECURITY] Security warnings:')
    result.warnings.forEach(warning => {
      console.warn(`  ⚠️ ${warning}`)
    })
  }

  if (result.recommendations.length > 0 && process.env.NODE_ENV === 'development') {
    console.info('💡 [ADMIN_ENV_SECURITY] Security recommendations:')
    result.recommendations.forEach(rec => {
      console.info(`  💡 ${rec}`)
    })
  }

  console.log('✅ [ADMIN_ENV_SECURITY] Admin environment security check completed')
}

// Auto-run security check in development
if (process.env.NODE_ENV === 'development' && typeof window === 'undefined') {
  performRuntimeSecurityCheck()
}
