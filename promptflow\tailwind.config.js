import tailwindcssAnimate from 'tailwindcss-animate'

/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [
    tailwindcssAnimate,
    function({ addUtilities }) {
      const newUtilities = {
        // Mobile-friendly touch targets
        '.touch-target': {
          minHeight: '44px',
          minWidth: '44px',
        },
        
        // Improved scroll behavior for mobile
        '.mobile-scroll': {
          '-webkit-overflow-scrolling': 'touch',
          'overscroll-behavior': 'contain',
        },
        
        // Better viewport handling
        '.full-height-mobile': {
          height: '100vh',
          height: '100dvh', /* Dynamic viewport height for mobile browsers */
        },
        
        // Mobile-optimized buttons
        '.mobile-btn': {
          minHeight: '44px',
          paddingLeft: '1rem',
          paddingRight: '1rem',
          paddingTop: '0.5rem',
          paddingBottom: '0.5rem',
        },
        
        // Better drag handle for mobile
        '.mobile-drag-handle': {
          width: '2rem',
          height: '2rem',
          minHeight: '44px',
          minWidth: '44px',
        },
        
        // Smooth transitions for mobile interactions
        '.mobile-transition': {
          transition: 'all 200ms ease-in-out',
        },
        
        // Better shadow for mobile cards
        '.mobile-card-shadow': {
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        },
        '.mobile-card-shadow:hover': {
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        },
        
        // Improved focus styles for accessibility
        '.focus-visible-enhanced:focus-visible': {
          outline: '2px solid hsl(var(--ring))',
          outlineOffset: '2px',
        },
      }

      // Mobile-specific utilities
      const mobileUtilities = {
        '@media (max-width: 768px)': {
          '.mobile-text-base': {
            fontSize: '16px', /* Prevents zoom on iOS */
          },
          '.mobile-card-spacing': {
            padding: '0.75rem',
            gap: '0.75rem',
          },
          'input, textarea': {
            fontSize: '16px !important', /* Prevents zoom on iOS */
          },
        }
      }

      // Tablet optimizations
      const tabletUtilities = {
        '@media (min-width: 768px) and (max-width: 1024px)': {
          '.tablet-spacing': {
            padding: '1rem',
            gap: '1rem',
          },
        }
      }

      // Safe area handling for mobile
      const safeAreaUtilities = {
        '@supports (padding: max(0px))': {
          '.safe-area-bottom': {
            paddingBottom: 'max(1rem, env(safe-area-inset-bottom))',
          },
          '.safe-area-top': {
            paddingTop: 'max(1rem, env(safe-area-inset-top))',
          },
        }
      }

      addUtilities(newUtilities)
      addUtilities(mobileUtilities)
      addUtilities(tabletUtilities)
      addUtilities(safeAreaUtilities)
    }
  ],
} 