// Test script to verify the authentication fix
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://iqehopwgrczylqliajww.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlxZWhvcHdncmN6eWxxbGlhancyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjEyMzE5NzQsImV4cCI6MjAzNjgwNzk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Test the isAdmin function that was causing the TypeError
async function testIsAdminFunction() {
  console.log('🧪 Testing isAdmin function...');
  
  try {
    // Test with a known admin user ID
    const adminUserId = 'fd7c0a50-3d77-42f2-9d06-11d5467d8080';
    
    // Simulate the isAdmin function from lib/supabase.ts
    const isAdmin = async (userId) => {
      try {
        if (!userId) return false;
        
        const { data, error } = await supabase
          .from('admin_users')
          .select('user_id, is_active')
          .eq('user_id', userId)
          .eq('is_active', true)
          .single();

        if (error) {
          console.error('Admin check error:', error);
          return false;
        }

        return !!data;
      } catch (error) {
        console.error('Failed to check admin status:', error);
        return false;
      }
    };
    
    // Test the function
    console.log('Testing isAdmin function with admin user ID:', adminUserId);
    const result = await isAdmin(adminUserId);
    console.log('✅ isAdmin function result:', result);
    
    // Test with invalid user ID
    console.log('Testing isAdmin function with invalid user ID...');
    const invalidResult = await isAdmin('invalid-user-id');
    console.log('✅ isAdmin function result for invalid ID:', invalidResult);
    
    // Test with null/undefined
    console.log('Testing isAdmin function with null...');
    const nullResult = await isAdmin(null);
    console.log('✅ isAdmin function result for null:', nullResult);
    
    return true;
  } catch (error) {
    console.error('❌ isAdmin function test failed:', error);
    return false;
  }
}

// Test the admin_users table structure
async function testAdminUsersTable() {
  console.log('\n🧪 Testing admin_users table...');
  
  try {
    const { data, error } = await supabase
      .from('admin_users')
      .select('*')
      .limit(5);
    
    if (error) {
      console.error('❌ Admin users query failed:', error);
      return false;
    }
    
    console.log('✅ Admin users found:', data.length);
    console.log('Admin users data:', data);
    return true;
  } catch (error) {
    console.error('❌ Admin users table test failed:', error);
    return false;
  }
}

// Test authentication flow
async function testAuthFlow() {
  console.log('\n🧪 Testing authentication flow...');
  
  try {
    // Test getting current user (should be null initially)
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.log('✅ No current user (expected):', error.message);
    } else if (!user) {
      console.log('✅ No current user (expected)');
    } else {
      console.log('✅ Current user found:', user.id);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Auth flow test failed:', error);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting authentication fix tests...\n');
  
  const tests = [
    { name: 'isAdmin Function', test: testIsAdminFunction },
    { name: 'Admin Users Table', test: testAdminUsersTable },
    { name: 'Authentication Flow', test: testAuthFlow }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const { name, test } of tests) {
    try {
      const result = await test();
      if (result) {
        console.log(`✅ ${name}: PASSED`);
        passed++;
      } else {
        console.log(`❌ ${name}: FAILED`);
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${name}: ERROR -`, error.message);
      failed++;
    }
  }
  
  console.log('\n📊 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! The authentication fix should work correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the errors above.');
  }
}

// Run the tests
runTests().catch(console.error);
