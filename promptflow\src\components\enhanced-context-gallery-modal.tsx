"use client";

import React, { useEffect, useRef } from 'react';
import { X } from 'lucide-react';
import { Button } from './ui/button';

import ContextGallery, { Context } from './context-gallery';

/**
 * Enhanced Context Gallery Modal Component
 *
 * Features:
 * - Smooth upward animation from "Add New Prompt" area
 * - Responsive positioning that adapts to screen sizes
 * - Fills available space except for "Add New Prompt" area
 * - Proper accessibility with ARIA attributes and keyboard navigation
 * - Touch-friendly scrolling on mobile devices
 * - Focus management and screen reader support
 */
interface EnhancedContextGalleryModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectContext: (context: Context) => void;
}

export function EnhancedContextGalleryModal({
  open,
  onOpenChange,
  onSelectContext
}: EnhancedContextGalleryModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const backdropRef = useRef<HTMLDivElement>(null);

  // Handle keyboard navigation and accessibility
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!open) return;

      switch (e.key) {
        case 'Escape':
          onOpenChange(false);
          break;
        case 'Tab':
          // Trap focus within modal
          if (modalRef.current) {
            const focusableElements = modalRef.current.querySelectorAll(
              'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            const firstElement = focusableElements[0] as HTMLElement;
            const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

            if (e.shiftKey) {
              if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement?.focus();
              }
            } else {
              if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement?.focus();
              }
            }
          }
          break;
      }
    };

    if (open) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
      // Announce modal opening to screen readers
      const announcement = document.createElement('div');
      announcement.setAttribute('aria-live', 'polite');
      announcement.setAttribute('aria-atomic', 'true');
      announcement.className = 'sr-only';
      announcement.textContent = 'Context Gallery açıldı. Escape tuşu ile kapatabilirsiniz.';
      document.body.appendChild(announcement);

      // Clean up announcement after screen reader has time to read it
      setTimeout(() => {
        document.body.removeChild(announcement);
      }, 1000);
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [open, onOpenChange]);

  // Focus management
  useEffect(() => {
    if (open && modalRef.current) {
      // Focus the modal container
      modalRef.current.focus();
    }
  }, [open]);

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === backdropRef.current) {
      onOpenChange(false);
    }
  };

  if (!open) return null;

  return (
    <>
      {/* CSS for responsive heights and smooth animations */}
      <style jsx>{`
        .enhanced-modal {
          height: calc(100vh - 200px);
          max-height: calc(100vh - 200px);
        }
        @media (min-width: 768px) {
          .enhanced-modal {
            height: calc(100vh - 180px);
            max-height: calc(100vh - 180px);
          }
        }
        @media (min-width: 1024px) {
          .enhanced-modal {
            height: calc(100vh - 160px);
            max-height: calc(100vh - 160px);
          }
        }
        .enhanced-modal-content {
          height: calc(100vh - 200px - 80px);
          max-height: calc(100vh - 200px - 80px);
        }
        @media (min-width: 768px) {
          .enhanced-modal-content {
            height: calc(100vh - 180px - 80px);
            max-height: calc(100vh - 180px - 80px);
          }
        }
        @media (min-width: 1024px) {
          .enhanced-modal-content {
            height: calc(100vh - 160px - 80px);
            max-height: calc(100vh - 160px - 80px);
          }
        }

        /* Smooth scrolling for better UX */
        .enhanced-modal-content {
          scroll-behavior: smooth;
          -webkit-overflow-scrolling: touch;
        }

        /* Focus styles for accessibility */
        .enhanced-modal:focus {
          outline: 2px solid #3b82f6;
          outline-offset: -2px;
        }
      `}</style>

      {/* Backdrop */}
      <div
        ref={backdropRef}
        className={`
          fixed inset-0 backdrop-blur-sm z-50 transition-all duration-300
          ${open ? 'bg-black/50 opacity-100' : 'bg-black/0 opacity-0'}
        `}
        onClick={handleBackdropClick}
        aria-hidden="true"
        role="presentation"
      />

      {/* Modal Container */}
      <div
        ref={modalRef}
        role="dialog"
        aria-modal="true"
        aria-labelledby="context-gallery-title"
        tabIndex={-1}
        className={`
          enhanced-modal
          fixed left-0 right-0 z-[55]
          bg-white border-t border-gray-200 shadow-2xl
          transition-all duration-300 ease-in-out
          ${open
            ? 'bottom-[200px] md:bottom-[180px] lg:bottom-[160px] opacity-100 translate-y-0'
            : 'bottom-0 opacity-0 translate-y-full pointer-events-none'
          }
        `}
      >
        {/* Modal Header */}
        <header className="flex-shrink-0 p-4 lg:p-6 border-b border-gray-200 bg-white">
          <div className="flex items-center justify-between">
            <h2
              id="context-gallery-title"
              className="text-xl lg:text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent"
            >
              Context Galerisi
            </h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
              className="h-8 w-8 p-0 hover:bg-gray-100 rounded-full transition-colors duration-200"
              aria-label="Context Gallery'yi kapat (Escape tuşu ile de kapatabilirsiniz)"
              title="Kapat"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <p className="sr-only">
            Context Gallery modal açık. Tab tuşu ile navigasyon yapabilir, Escape tuşu ile kapatabilirsiniz.
          </p>
        </header>

        {/* Scrollable Content Area */}
        <main
          className="flex-1 overflow-hidden"
          role="main"
          aria-label="Context Gallery içeriği"
        >
          <div
            className="enhanced-modal-content p-4 lg:p-6 overflow-y-auto focus:outline-none"
            tabIndex={-1}
            style={{
              scrollBehavior: 'smooth',
              WebkitOverflowScrolling: 'touch'
            }}
          >
            {/* Use existing ContextGallery with contentOnly prop */}
            <ContextGallery
              onSelectContext={onSelectContext}
              contentOnly={true}
            />
          </div>
        </main>
      </div>
    </>
  );
}


