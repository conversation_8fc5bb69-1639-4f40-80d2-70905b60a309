'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabaseBrowser as supabase } from '@/lib/supabase-browser'
import { Database } from '@/lib/supabase'

type PlanType = Database['public']['Tables']['plan_types']['Row']
type UserPlan = Database['public']['Tables']['user_plans']['Row']
type UsageStats = Database['public']['Tables']['usage_stats']['Row']

// Plan türlerini getir
export function usePlanTypes() {
  return useQuery({
    queryKey: ['plan-types'],
    queryFn: async (): Promise<PlanType[]> => {
      console.log(`📋 [USE_PLANS] Fetching plan types...`)
      
      const { data, error } = await supabase
        .from('plan_types')
        .select('*')
        .eq('is_active', true)
        .order('sort_order')

      if (error) {
        console.error(`❌ [USE_PLANS] Plan types fetch failed:`, error)
        throw new Error(error.message)
      }

      console.log(`✅ [USE_PLANS] Plan types fetched:`, data?.length)
      return data || []
    },
    staleTime: 5 * 60 * 1000, // 5 dakika
  })
}

// Kullanıcının aktif planını getir
export function useUserActivePlan() {
  return useQuery({
    queryKey: ['user-active-plan'],
    queryFn: async () => {
      console.log(`👤 [USE_PLANS] Fetching user active plan...`)
      
      const { data, error } = await supabase.rpc('get_user_active_plan', {
        user_uuid: (await supabase.auth.getUser()).data.user?.id
      })

      if (error) {
        console.error(`❌ [USE_PLANS] User active plan fetch failed:`, error)
        throw new Error(error.message)
      }

      console.log(`✅ [USE_PLANS] User active plan fetched:`, data?.[0])
      return data?.[0] || null
    },
    staleTime: 2 * 60 * 1000, // 2 dakika
  })
}

// Kullanıcının limitlerini kontrol et
export function useUserLimits() {
  return useQuery({
    queryKey: ['user-limits'],
    queryFn: async () => {
      console.log(`🔍 [USE_PLANS] Checking user limits...`)
      
      const { data, error } = await supabase.rpc('check_user_limits', {
        user_uuid: (await supabase.auth.getUser()).data.user?.id
      })

      if (error) {
        console.error(`❌ [USE_PLANS] User limits check failed:`, error)
        throw new Error(error.message)
      }

      console.log(`✅ [USE_PLANS] User limits checked:`, data?.[0])
      return data?.[0] || null
    },
    staleTime: 30 * 1000, // 30 saniye
  })
}

// Kullanıcının plan geçmişini getir
export function useUserPlanHistory() {
  return useQuery({
    queryKey: ['user-plan-history'],
    queryFn: async (): Promise<UserPlan[]> => {
      console.log(`📜 [USE_PLANS] Fetching user plan history...`)
      
      const { data, error } = await supabase
        .from('user_plans')
        .select(`
          *,
          plan_types (
            name,
            display_name,
            price_monthly,
            price_yearly
          )
        `)
        .order('created_at', { ascending: false })

      if (error) {
        console.error(`❌ [USE_PLANS] User plan history fetch failed:`, error)
        throw new Error(error.message)
      }

      console.log(`✅ [USE_PLANS] User plan history fetched:`, data?.length)
      return data || []
    },
    staleTime: 5 * 60 * 1000, // 5 dakika
  })
}

// Kullanıcının kullanım istatistiklerini getir
export function useUsageStats(days: number = 30) {
  return useQuery({
    queryKey: ['usage-stats', days],
    queryFn: async (): Promise<UsageStats[]> => {
      console.log(`📊 [USE_PLANS] Fetching usage stats for ${days} days...`)
      
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)
      
      const { data, error } = await supabase
        .from('usage_stats')
        .select('*')
        .gte('stat_date', startDate.toISOString().split('T')[0])
        .order('stat_date', { ascending: false })

      if (error) {
        console.error(`❌ [USE_PLANS] Usage stats fetch failed:`, error)
        throw new Error(error.message)
      }

      console.log(`✅ [USE_PLANS] Usage stats fetched:`, data?.length)
      return data || []
    },
    staleTime: 5 * 60 * 1000, // 5 dakika
  })
}

// Plan değiştir
export function useChangePlan() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ 
      planName, 
      billingCycle = 'monthly',
      paymentReference 
    }: { 
      planName: string
      billingCycle?: string
      paymentReference?: string 
    }) => {
      console.log(`🔄 [USE_PLANS] Changing plan to: ${planName}`)
      
      const { data, error } = await supabase.rpc('change_user_plan', {
        user_uuid: (await supabase.auth.getUser()).data.user?.id,
        new_plan_name: planName,
        billing_cycle_param: billingCycle,
        payment_reference_param: paymentReference
      })

      if (error) {
        console.error(`❌ [USE_PLANS] Plan change failed:`, error)
        throw new Error(error.message)
      }

      console.log(`✅ [USE_PLANS] Plan changed successfully:`, data)
      return data
    },
    onSuccess: () => {
      console.log(`🎉 [USE_PLANS] Plan change successful, invalidating queries`)
      queryClient.invalidateQueries({ queryKey: ['user-active-plan'] })
      queryClient.invalidateQueries({ queryKey: ['user-limits'] })
      queryClient.invalidateQueries({ queryKey: ['user-plan-history'] })
      queryClient.invalidateQueries({ queryKey: ['usage-stats'] })
    },
    onError: (error) => {
      console.error(`💥 [USE_PLANS] Plan change error:`, error)
    }
  })
}

// Kullanım istatistiklerini güncelle
export function useUpdateUsageStats() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async () => {
      console.log(`📈 [USE_PLANS] Updating usage stats...`)
      
      const { error } = await supabase.rpc('update_usage_stats', {
        user_uuid: (await supabase.auth.getUser()).data.user?.id
      })

      if (error) {
        console.error(`❌ [USE_PLANS] Usage stats update failed:`, error)
        throw new Error(error.message)
      }

      console.log(`✅ [USE_PLANS] Usage stats updated successfully`)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['usage-stats'] })
      queryClient.invalidateQueries({ queryKey: ['user-limits'] })
    }
  })
}

// Plan limiti kontrol fonksiyonu
export async function checkPlanLimit(action: 'create_project' | 'create_prompt'): Promise<boolean> {
  try {
    const { data, error } = await supabase.rpc('check_user_limits', {
      user_uuid: (await supabase.auth.getUser()).data.user?.id
    })

    if (error) {
      console.error(`❌ [USE_PLANS] Limit check failed:`, error)
      return false
    }

    const limits = data?.[0]
    if (!limits) return false

    switch (action) {
      case 'create_project':
        return limits.can_create_project
      case 'create_prompt':
        return limits.can_create_prompt
      default:
        return false
    }
  } catch (error) {
    console.error(`❌ [USE_PLANS] Limit check error:`, error)
    return false
  }
}

// Plan özelliği kontrol fonksiyonu
export function usePlanFeature(featureName: string) {
  const { data: activePlan } = useUserActivePlan()
  
  return {
    hasFeature: activePlan?.features?.[featureName] === true,
    featureValue: activePlan?.features?.[featureName],
    planName: activePlan?.plan_name,
    displayName: activePlan?.display_name
  }
}
