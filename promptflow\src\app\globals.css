@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;

  /* Promptbir Brand Colors - Updated to use modern blue/purple gradient */
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);

  /* Primary: Modern Blue (#3B82F6) */
  --primary: oklch(0.6 0.2 250);
  --primary-foreground: oklch(0.985 0 0);

  /* Secondary: Purple accent (#8B5CF6) */
  --secondary: oklch(0.65 0.25 280);
  --secondary-foreground: oklch(0.985 0 0);

  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.6 0.2 250);

  /* Chart colors with Promptbir theme */
  --chart-1: oklch(0.6 0.2 250);
  --chart-2: oklch(0.65 0.25 280);
  --chart-3: oklch(0.55 0.15 200);
  --chart-4: oklch(0.7 0.2 320);
  --chart-5: oklch(0.5 0.18 180);

  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.6 0.2 250);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.6 0.2 250);

  /* Promptbir Custom Properties */
  --promptbir-gradient-from: oklch(0.6 0.2 250);
  --promptbir-gradient-to: oklch(0.65 0.25 280);
  --promptbir-success: oklch(0.5 0.15 140);
  --promptbir-warning: oklch(0.7 0.2 60);
  --promptbir-info: oklch(0.55 0.15 200);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Import Inter font for better typography */
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

  html {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }
}

@layer components {
  /* Modern Glass Effect */
  .promptbir-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Enhanced Shadows */
  .promptbir-shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
  }

  .promptbir-shadow-purple-glow {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.15);
  }

  /* Hero Section Animations */
  .promptbir-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
  }

  .promptbir-fade-in-up-delay {
    animation: fadeInUp 0.8s ease-out 0.2s forwards;
    opacity: 0;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Floating Animation */
  .promptbir-float {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  /* Touch-friendly targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Mobile-specific styles */
  @media (max-width: 640px) {
    .touch-target {
      min-height: 48px;
      min-width: 48px;
    }
  }

  /* Focus improvements */
  .focus-visible-enhanced:focus-visible {
    outline: 2px solid theme(colors.blue.500);
    outline-offset: 2px;
  }

  /* Mobile text sizing */
  .mobile-text-base {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Dynamic height utilities with enhanced animations */
  .dynamic-textarea {
    transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                border-color 0.2s ease-out,
                box-shadow 0.2s ease-out;
    overflow: hidden;
    resize: none;
    transform: translateZ(0); /* Enable hardware acceleration */
    will-change: height, max-height;
  }

  /* Enhanced focus states with smooth transitions */
  .dynamic-textarea:focus {
    transition: height 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                max-height 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                border-color 0.15s ease-out,
                box-shadow 0.15s ease-out;
  }

  /* Smooth expansion animation */
  @keyframes textareaExpand {
    from {
      transform: scaleY(0.95);
      opacity: 0.8;
    }
    to {
      transform: scaleY(1);
      opacity: 1;
    }
  }

  .dynamic-textarea.expanding {
    animation: textareaExpand 0.2s ease-out;
  }

  /* Responsive height constraints */
  @media (max-width: 640px) {
    .dynamic-textarea {
      max-height: 25vh; /* 1/4 viewport height on mobile */
      min-height: 48px;
    }
  }

  @media (min-width: 641px) and (max-width: 1024px) {
    .dynamic-textarea {
      max-height: 33vh; /* 1/3 viewport height on tablet */
      min-height: 44px;
    }
  }

  @media (min-width: 1025px) {
    .dynamic-textarea {
      max-height: 33vh; /* 1/3 viewport height on desktop */
      min-height: 56px;
    }
  }

  /* Safe area handling for mobile devices */
  @supports (height: 100dvh) {
    @media (max-width: 640px) {
      .dynamic-textarea {
        max-height: 25dvh; /* Dynamic viewport height */
      }
    }

    @media (min-width: 641px) and (max-width: 1024px) {
      .dynamic-textarea {
        max-height: 33dvh;
      }
    }

    @media (min-width: 1025px) {
      .dynamic-textarea {
        max-height: 33dvh;
      }
    }
  }

  /* Performance optimizations for dynamic height */
  .dynamic-textarea {
    contain: layout style;
    will-change: height;
  }

  /* Smooth scrolling for overflow content */
  .dynamic-textarea::-webkit-scrollbar {
    width: 6px;
  }

  .dynamic-textarea::-webkit-scrollbar-track {
    background: transparent;
  }

  .dynamic-textarea::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
  }

  .dynamic-textarea::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.8);
  }
}
