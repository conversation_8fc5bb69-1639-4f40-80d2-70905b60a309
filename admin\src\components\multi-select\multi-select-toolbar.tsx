'use client';

import {
  <PERSON>,
  <PERSON>Stack,
  <PERSON>,
  <PERSON><PERSON>,
  IconButton,
  Badge,
  useColorModeValue,
  Tooltip,
} from '@chakra-ui/react';
import { X, Trash2, Check, Copy, Download } from 'lucide-react';
import {
  useSelectedItems,
  useIsMultiSelectMode,
  useMultiSelectActions,
} from '@/store/admin-store';

interface MultiSelectToolbarProps {
  onBulkDelete?: (ids: string[]) => void;
  onBulkMarkAsUsed?: (ids: string[]) => void;
  onBulkCopy?: (ids: string[]) => void;
  onBulkExport?: (ids: string[]) => void;
  itemType?: 'prompts' | 'projects' | 'users';
}

export function MultiSelectToolbar({
  onBulkDelete,
  onBulkMarkAsUsed,
  onBulkCopy,
  onBulkExport,
  itemType = 'prompts',
}: MultiSelectToolbarProps) {
  const selectedItems = useSelectedItems();
  const isMultiSelectMode = useIsMultiSelectMode();
  const { clearSelectedItems, toggleMultiSelectMode } = useMultiSelectActions();

  const bgColor = useColorModeValue('blue.50', 'blue.900');
  const borderColor = useColorModeValue('blue.200', 'blue.700');

  const selectedCount = selectedItems.size;

  if (!isMultiSelectMode || selectedCount === 0) {
    return null;
  }

  const handleBulkAction = (action: (ids: string[]) => void) => {
    const selectedIds = Array.from(selectedItems);
    action(selectedIds);
    clearSelectedItems();
  };

  const getItemTypeText = () => {
    switch (itemType) {
      case 'prompts':
        return selectedCount === 1 ? 'prompt' : 'prompt';
      case 'projects':
        return selectedCount === 1 ? 'proje' : 'proje';
      case 'users':
        return selectedCount === 1 ? 'kullanıcı' : 'kullanıcı';
      default:
        return 'öğe';
    }
  };

  return (
    <Box
      position="fixed"
      bottom={4}
      left="50%"
      transform="translateX(-50%)"
      zIndex={1000}
      bg={bgColor}
      border="1px solid"
      borderColor={borderColor}
      borderRadius="lg"
      shadow="lg"
      p={4}
      minW="400px"
    >
      <HStack justify="space-between" align="center">
        {/* Selection info */}
        <HStack spacing={3}>
          <Badge colorScheme="blue" fontSize="sm" px={2} py={1}>
            {selectedCount}
          </Badge>
          <Text fontSize="sm" fontWeight="medium">
            {selectedCount} {getItemTypeText()} seçildi
          </Text>
        </HStack>

        {/* Action buttons */}
        <HStack spacing={2}>
          {/* Copy button */}
          {onBulkCopy && (
            <Tooltip label="Seçilenleri kopyala">
              <IconButton
                aria-label="Kopyala"
                icon={<Copy size={16} />}
                size="sm"
                variant="ghost"
                onClick={() => handleBulkAction(onBulkCopy)}
              />
            </Tooltip>
          )}

          {/* Mark as used button (for prompts) */}
          {onBulkMarkAsUsed && itemType === 'prompts' && (
            <Tooltip label="Kullanıldı olarak işaretle">
              <IconButton
                aria-label="Kullanıldı işaretle"
                icon={<Check size={16} />}
                size="sm"
                variant="ghost"
                colorScheme="green"
                onClick={() => handleBulkAction(onBulkMarkAsUsed)}
              />
            </Tooltip>
          )}

          {/* Export button */}
          {onBulkExport && (
            <Tooltip label="Seçilenleri dışa aktar">
              <IconButton
                aria-label="Dışa aktar"
                icon={<Download size={16} />}
                size="sm"
                variant="ghost"
                onClick={() => handleBulkAction(onBulkExport)}
              />
            </Tooltip>
          )}

          {/* Delete button */}
          {onBulkDelete && (
            <Tooltip label="Seçilenleri sil">
              <IconButton
                aria-label="Sil"
                icon={<Trash2 size={16} />}
                size="sm"
                variant="ghost"
                colorScheme="red"
                onClick={() => handleBulkAction(onBulkDelete)}
              />
            </Tooltip>
          )}

          {/* Clear selection */}
          <Tooltip label="Seçimi temizle">
            <IconButton
              aria-label="Seçimi temizle"
              icon={<X size={16} />}
              size="sm"
              variant="ghost"
              onClick={clearSelectedItems}
            />
          </Tooltip>

          {/* Exit multi-select mode */}
          <Button
            size="sm"
            variant="outline"
            onClick={toggleMultiSelectMode}
          >
            Çıkış
          </Button>
        </HStack>
      </HStack>
    </Box>
  );
}
