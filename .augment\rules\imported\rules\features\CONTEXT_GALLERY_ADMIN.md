---
type: "admin_feature"
role: "admin_guide"
parent_feature: "features/CONTEXT_GALLERY.md"
dependencies:
  - "core/PROJECT_CORE.md"
  - "features/CONTEXT_GALLERY.md"
  - "frontend/ADMIN_PANEL.md"
  - "backend/SUPABASE.md"
fixes_reference:
  - "fixes/CONTEXT_GALLERY_ISSUES.md"
auto_update_from:
  - "features/CONTEXT_GALLERY.md"
development_history:
  - date: "2025-01-29"
    change: "Admin panel integration documented"
    admin_features: ["Template approval workflow", "Bulk operations", "Analytics dashboard"]
    integration_points: ["/admin/contexts page", "Admin permission checks", "RLS policies"]
last_updated: "2025-01-29"
---

# Context Gallery Admin Panel Development Guide

## Admin Panel Structure (/admin/contexts)

### Page Layout
```typescript
// /admin/src/app/contexts/page.tsx
export default function ContextsAdminPage() {
  return (
    <AdminLayout>
      <ContextsHeader />
      <ContextsStats />
      <ContextsFilters />
      <ContextsTable />
      <BulkActions />
    </AdminLayout>
  )
}

// Key components for admin management
<ContextsHeader>
  <h1>Context Gallery Yönetimi</h1>
  <CreateCategoryButton />
  <ExportButton />
</ContextsHeader>

<ContextsStats>
  <StatCard title="Toplam Template" value={totalTemplates} />
  <StatCard title="Onay Bekleyen" value={pendingCount} />
  <StatCard title="Öne Çıkan" value={featuredCount} />
  <StatCard title="Günlük Kullanım" value={dailyUsage} />
</ContextsStats>
```

### Template Management Interface
```typescript
// Template approval interface
interface TemplateApprovalProps {
  template: ContextTemplate
  onApprove: (id: string, notes?: string) => void
  onReject: (id: string, reason: string) => void
  onFeature: (id: string, featured: boolean) => void
}

function TemplateApprovalCard({ template, onApprove, onReject, onFeature }: TemplateApprovalProps) {
  return (
    <Card className="p-4">
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <h3 className="font-semibold">{template.title}</h3>
          <p className="text-sm text-gray-600">{template.description}</p>
          <div className="flex gap-2 mt-2">
            {template.tags.map(tag => (
              <Badge key={tag} variant="secondary">{tag}</Badge>
            ))}
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button 
            size="sm" 
            variant="outline"
            onClick={() => onFeature(template.id, !template.is_featured)}
          >
            {template.is_featured ? 'Öne Çıkarmayı Kaldır' : 'Öne Çıkar'}
          </Button>
          
          <Button 
            size="sm" 
            variant="default"
            onClick={() => onApprove(template.id)}
          >
            Onayla
          </Button>
          
          <Button 
            size="sm" 
            variant="destructive"
            onClick={() => onReject(template.id, 'Quality issues')}
          >
            Reddet
          </Button>
        </div>
      </div>
      
      <div className="mt-4 p-3 bg-gray-50 rounded">
        <p className="text-sm font-medium">İçerik Önizleme:</p>
        <p className="text-sm mt-1 line-clamp-3">{template.content}</p>
      </div>
      
      <div className="mt-2 text-xs text-gray-500">
        Yazar: {template.author.email} | 
        Oluşturulma: {formatDate(template.created_at)} |
        Kullanım: {template.usage_count}
      </div>
    </Card>
  )
}
```

## Admin Operations

### Bulk Operations
```typescript
// Bulk approval system
const useBulkTemplateOperations = () => {
  const [selectedTemplates, setSelectedTemplates] = useState<string[]>([])
  
  const bulkApprove = async (templateIds: string[]) => {
    try {
      const { error } = await supabase
        .from('context_templates')
        .update({ 
          status: 'approved',
          is_public: true,
          approved_by: adminUserId,
          approved_at: new Date().toISOString()
        })
        .in('id', templateIds)
      
      if (error) throw error
      
      toast.success(`${templateIds.length} template onaylandı`)
      setSelectedTemplates([])
      
    } catch (error) {
      toast.error('Toplu onaylama hatası')
      console.error(error)
    }
  }
  
  const bulkReject = async (templateIds: string[], reason: string) => {
    try {
      const { error } = await supabase
        .from('context_templates')
        .update({ 
          status: 'rejected',
          rejection_reason: reason,
          rejected_by: adminUserId,
          rejected_at: new Date().toISOString()
        })
        .in('id', templateIds)
      
      if (error) throw error
      
      toast.success(`${templateIds.length} template reddedildi`)
      setSelectedTemplates([])
      
    } catch (error) {
      toast.error('Toplu reddetme hatası')
      console.error(error)
    }
  }
  
  return {
    selectedTemplates,
    setSelectedTemplates,
    bulkApprove,
    bulkReject
  }
}
```

### Category Management
```typescript
// Category CRUD operations for admin
const useCategoryManagement = () => {
  const createCategory = async (category: {
    name: string
    description: string
    icon: string
    color: string
  }) => {
    try {
      const { data, error } = await supabase
        .from('context_categories')
        .insert({
          ...category,
          sort_order: await getNextSortOrder()
        })
        .select()
        .single()
      
      if (error) throw error
      
      toast.success('Kategori oluşturuldu')
      return data
      
    } catch (error) {
      toast.error('Kategori oluşturma hatası')
      throw error
    }
  }
  
  const updateCategory = async (id: string, updates: Partial<ContextCategory>) => {
    try {
      const { error } = await supabase
        .from('context_categories')
        .update(updates)
        .eq('id', id)
      
      if (error) throw error
      
      toast.success('Kategori güncellendi')
      
    } catch (error) {
      toast.error('Kategori güncelleme hatası')
      throw error
    }
  }
  
  const deleteCategory = async (id: string) => {
    try {
      // Check if category has templates
      const { count } = await supabase
        .from('context_templates')
        .select('*', { count: 'exact', head: true })
        .eq('category_id', id)
      
      if (count && count > 0) {
        throw new Error('Bu kategoride template\'ler var, önce onları taşıyın')
      }
      
      const { error } = await supabase
        .from('context_categories')
        .delete()
        .eq('id', id)
      
      if (error) throw error
      
      toast.success('Kategori silindi')
      
    } catch (error) {
      toast.error(error.message || 'Kategori silme hatası')
      throw error
    }
  }
  
  return {
    createCategory,
    updateCategory,
    deleteCategory
  }
}
```

## Analytics Dashboard

### Admin Analytics Components
```typescript
// Context analytics for admin dashboard
function ContextAnalyticsDashboard() {
  const { data: analytics } = useContextAnalytics()
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <AnalyticsCard
        title="Toplam Template"
        value={analytics?.total_templates || 0}
        change="+12%"
        trend="up"
      />
      
      <AnalyticsCard
        title="Onay Bekleyen"
        value={analytics?.pending_approvals || 0}
        change="-5%"
        trend="down"
      />
      
      <AnalyticsCard
        title="Günlük Kullanım"
        value={analytics?.daily_usage || 0}
        change="+23%"
        trend="up"
      />
      
      <AnalyticsCard
        title="Ortalama Rating"
        value={analytics?.average_rating || 0}
        change="+0.2"
        trend="up"
      />
    </div>
  )
}

// Popular templates chart
function PopularTemplatesChart() {
  const { data: popularTemplates } = usePopularTemplates()
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>En Popüler Template'ler</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {popularTemplates?.map((template, index) => (
            <div key={template.id} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">#{index + 1}</span>
                <span className="text-sm">{template.title}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs text-gray-500">{template.usage_count} kullanım</span>
                <span className="text-xs text-gray-500">{template.like_count} beğeni</span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
```

## Admin Hooks & API

### Admin-specific Hooks
```typescript
// Admin context management hooks
export const useAdminContexts = (filters?: {
  status?: 'pending' | 'approved' | 'rejected' | 'all'
  category?: string
  featured?: boolean
}) => {
  return useQuery({
    queryKey: ['admin-contexts', filters],
    queryFn: async () => {
      let query = supabase
        .from('context_templates')
        .select(`
          *,
          category:context_categories(*),
          author:auth.users(email, raw_user_meta_data)
        `)
        .order('created_at', { ascending: false })
      
      if (filters?.status && filters.status !== 'all') {
        query = query.eq('status', filters.status)
      }
      
      if (filters?.category) {
        query = query.eq('category_id', filters.category)
      }
      
      if (filters?.featured !== undefined) {
        query = query.eq('is_featured', filters.featured)
      }
      
      const { data, error } = await query
      
      if (error) throw error
      return data
    },
    enabled: !!useAdminCheck().data // Only run if user is admin
  })
}

export const useContextAnalytics = () => {
  return useQuery({
    queryKey: ['context-analytics'],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_context_admin_analytics')
      if (error) throw error
      return data
    },
    enabled: !!useAdminCheck().data
  })
}
```

## Security & Permissions

### Admin Permission Checks
```typescript
// Ensure only admins can access context management
export const useAdminContextGuard = () => {
  const { data: isAdmin, isLoading } = useAdminCheck()
  const router = useRouter()
  
  useEffect(() => {
    if (!isLoading && !isAdmin) {
      router.push('/admin/dashboard')
      toast.error('Bu sayfaya erişim yetkiniz yok')
    }
  }, [isAdmin, isLoading, router])
  
  return { isAdmin, isLoading }
}

// RLS policies for admin operations
-- Admin can see all templates regardless of status
CREATE POLICY "Admins see all templates" ON context_templates
  FOR SELECT USING (
    EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
  );

-- Admin can update any template
CREATE POLICY "Admins update all templates" ON context_templates
  FOR UPDATE USING (
    EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
  );
```

## Development Workflow

### Adding New Admin Features
1. **Database Changes**: Add necessary columns/tables
2. **RLS Policies**: Ensure admin-only access
3. **Backend Functions**: Create admin-specific functions
4. **Admin Hooks**: Create React Query hooks
5. **UI Components**: Build admin interface
6. **Permission Guards**: Add security checks
7. **Testing**: Test admin workflows

### Context Gallery Admin Checklist
- [ ] Admin permission verification
- [ ] Template approval workflow
- [ ] Bulk operations functionality
- [ ] Category management
- [ ] Analytics dashboard
- [ ] Search and filtering
- [ ] Export functionality
- [ ] Audit logging
- [ ] Error handling
- [ ] Mobile responsiveness

## Update Requirements
- Document new admin features as they're added
- Keep permission checks current
- Update analytics when new metrics are added
- Maintain security documentation
- Document new bulk operations
- Keep workflow documentation current
