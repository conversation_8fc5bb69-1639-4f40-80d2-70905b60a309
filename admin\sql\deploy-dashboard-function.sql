-- Deploy get_admin_dashboard_stats function to Supabase
-- Copy and paste this entire content into Supabase SQL Editor

-- Create RPC function for consolidated dashboard stats
CREATE OR REPLACE FUNCTION get_admin_dashboard_stats()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
  total_users_count INTEGER := 0;
  total_projects_count INTEGER := 0;
  total_prompts_count INTEGER := 0;
  total_contexts_count INTEGER := 0;
  active_users_count INTEGER := 0;
  user_growth_rate NUMERIC := 0;
  project_growth_rate NUMERIC := 0;
  avg_prompts_per_project NUMERIC := 0;
  avg_context_usage NUMERIC := 0;
BEGIN
  -- Get total users (fallback to auth.users if admin_users doesn't exist)
  BEGIN
    SELECT COUNT(*) INTO total_users_count FROM admin_users WHERE is_active = true;
  EXCEPTION WHEN undefined_table THEN
    SELECT COUNT(*) INTO total_users_count FROM auth.users;
  END;

  -- Get total projects
  BEGIN
    SELECT COUNT(*) INTO total_projects_count FROM projects;
  EXCEPTION WHEN undefined_table THEN
    total_projects_count := 0;
  END;

  -- Get total prompts
  BEGIN
    SELECT COUNT(*) INTO total_prompts_count FROM prompts;
  EXCEPTION WHEN undefined_table THEN
    total_prompts_count := 0;
  END;

  -- Get total contexts
  BEGIN
    SELECT COUNT(*) INTO total_contexts_count FROM contexts WHERE status = 'active';
  EXCEPTION WHEN undefined_table THEN
    total_contexts_count := 0;
  END;

  -- Get active users (fallback if user_sessions doesn't exist)
  BEGIN
    SELECT COUNT(DISTINCT user_id) INTO active_users_count
    FROM user_sessions
    WHERE is_active = true
    AND last_activity_at > NOW() - INTERVAL '24 hours';
  EXCEPTION WHEN undefined_table THEN
    -- Fallback to recent auth users
    SELECT COUNT(*) INTO active_users_count
    FROM auth.users
    WHERE last_sign_in_at > NOW() - INTERVAL '24 hours';
  END;

  -- Calculate user growth rate with proper type casting
  BEGIN
    WITH user_counts AS (
      SELECT 
        COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '30 days') as new_users,
        COUNT(*) FILTER (WHERE created_at <= NOW() - INTERVAL '30 days') as old_users
      FROM admin_users
      WHERE is_active = true
    )
    SELECT 
      CASE 
        WHEN old_users > 0 THEN ROUND((new_users::NUMERIC / old_users::NUMERIC * 100), 2)
        ELSE 0
      END
    INTO user_growth_rate
    FROM user_counts;
  EXCEPTION WHEN undefined_table THEN
    WITH user_counts AS (
      SELECT 
        COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '30 days') as new_users,
        COUNT(*) FILTER (WHERE created_at <= NOW() - INTERVAL '30 days') as old_users
      FROM auth.users
    )
    SELECT 
      CASE 
        WHEN old_users > 0 THEN ROUND((new_users::NUMERIC / old_users::NUMERIC * 100), 2)
        ELSE 0
      END
    INTO user_growth_rate
    FROM user_counts;
  END;

  -- Calculate project growth rate with proper type casting
  BEGIN
    WITH project_counts AS (
      SELECT 
        COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '30 days') as new_projects,
        COUNT(*) FILTER (WHERE created_at <= NOW() - INTERVAL '30 days') as old_projects
      FROM projects
    )
    SELECT 
      CASE 
        WHEN old_projects > 0 THEN ROUND((new_projects::NUMERIC / old_projects::NUMERIC * 100), 2)
        ELSE 0
      END
    INTO project_growth_rate
    FROM project_counts;
  EXCEPTION WHEN undefined_table THEN
    project_growth_rate := 0;
  END;

  -- Calculate average prompts per project with proper type casting
  BEGIN
    WITH prompt_counts AS (
      SELECT COUNT(*) as prompt_count
      FROM prompts
      GROUP BY project_id
    )
    SELECT COALESCE(ROUND(AVG(prompt_count::NUMERIC), 2), 0)
    INTO avg_prompts_per_project
    FROM prompt_counts;
  EXCEPTION WHEN undefined_table THEN
    avg_prompts_per_project := 0;
  END;

  -- Calculate average context usage per project with proper type casting
  BEGIN
    SELECT COALESCE(
      ROUND(
        AVG(
          CASE 
            WHEN context_text IS NOT NULL AND context_text != '' THEN 1::NUMERIC 
            ELSE 0::NUMERIC 
          END
        ) * 100, 
        2
      ), 
      0
    )
    INTO avg_context_usage
    FROM projects;
  EXCEPTION WHEN undefined_table THEN
    avg_context_usage := 0;
  END;

  -- Build final result
  SELECT json_build_object(
    'totalUsers', total_users_count,
    'totalProjects', total_projects_count,
    'totalPrompts', total_prompts_count,
    'totalContexts', total_contexts_count,
    'activeUsers', active_users_count,
    'userGrowth', user_growth_rate,
    'projectGrowth', project_growth_rate,
    'avgPromptsPerProject', avg_prompts_per_project,
    'avgContextUsage', avg_context_usage
  ) INTO result;

  RETURN result;
END;
$$;

-- Test the function (optional)
-- SELECT get_admin_dashboard_stats();
