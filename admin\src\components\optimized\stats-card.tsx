'use client';

import { memo } from 'react';
import {
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  HStack,
  Text,
  Icon,
} from '@chakra-ui/react';
import { LucideIcon } from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: number | string;
  icon: LucideIcon;
  color: string;
  growth?: number;
  helpText?: string;
  cardBg?: string;
}

// Memoized stats card component to prevent unnecessary re-renders
export const StatsCard = memo(function StatsCard({
  title,
  value,
  icon,
  color,
  growth,
  helpText,
  cardBg = 'white',
}: StatsCardProps) {
  return (
    <Card bg={cardBg} shadow="md">
      <CardBody>
        <Stat>
          <StatLabel color="gray.600">
            <HStack>
              <Icon as={icon} w={4} h={4} />
              <Text>{title}</Text>
            </HStack>
          </StatLabel>
          <StatNumber fontSize="3xl" color={color}>
            {value}
          </StatNumber>
          {(growth !== undefined || helpText) && (
            <StatHelpText>
              {growth !== undefined && (
                <>
                  <StatArrow type={growth >= 0 ? 'increase' : 'decrease'} />
                  {Math.abs(growth)}%
                </>
              )}
              {helpText && <Text>{helpText}</Text>}
            </StatHelpText>
          )}
        </Stat>
      </CardBody>
    </Card>
  );
});

// Props comparison function for better memoization
StatsCard.displayName = 'StatsCard';
