/**
 * Database Query Security System
 * SQL Injection prevention and query validation
 */

import { supabaseBrowser as supabase } from './supabase-browser'

// SQL injection patterns to detect and block
const SQL_INJECTION_PATTERNS = [
  // Union-based injection
  /(\bunion\b.*\bselect\b)/gi,
  /(\bselect\b.*\bunion\b)/gi,
  
  // Boolean-based injection
  /(\bor\b\s+\d+\s*=\s*\d+)/gi,
  /(\band\b\s+\d+\s*=\s*\d+)/gi,
  /(\bor\b\s+true\b)/gi,
  /(\band\b\s+false\b)/gi,
  
  // Time-based injection
  /(\bwaitfor\b.*\bdelay\b)/gi,
  /(\bsleep\b\s*\()/gi,
  /(\bbenchmark\b\s*\()/gi,
  
  // Error-based injection
  /(\bcast\b.*\bas\b.*\bint\b)/gi,
  /(\bconvert\b.*\bint\b)/gi,
  
  // Stacked queries
  /(;\s*drop\b)/gi,
  /(;\s*delete\b)/gi,
  /(;\s*insert\b)/gi,
  /(;\s*update\b)/gi,
  /(;\s*create\b)/gi,
  /(;\s*alter\b)/gi,
  
  // Comment-based injection
  /(--[^\r\n]*)/g,
  /(\/\*[\s\S]*?\*\/)/g,
  
  // Quote manipulation
  /('.*'.*')/g,
  /(".*".*")/g,
  
  // Function calls that could be dangerous
  /(\bexec\b|\bexecute\b|\beval\b)/gi,
  /(\bsp_\w+)/gi,
  /(\bxp_\w+)/gi,
  
  // Information schema access
  /(\binformation_schema\b)/gi,
  /(\bsys\.\w+)/gi,
  /(\bmaster\.\w+)/gi,
  
  // Hex encoding attempts
  /(0x[0-9a-f]+)/gi
] as const

// Safe query patterns for Supabase (for future use)
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const SAFE_QUERY_PATTERNS = {
  // Basic CRUD operations
  select: /^select\s+[\w\s,.*]+\s+from\s+\w+(\s+where\s+[\w\s=<>!]+)?(\s+order\s+by\s+\w+)?(\s+limit\s+\d+)?$/gi,
  insert: /^insert\s+into\s+\w+\s*\([\w\s,]+\)\s*values\s*\([\w\s,'?]+\)$/gi,
  update: /^update\s+\w+\s+set\s+[\w\s=,'?]+(\s+where\s+[\w\s=<>!]+)?$/gi,
  delete: /^delete\s+from\s+\w+(\s+where\s+[\w\s=<>!]+)?$/gi
} as const

interface QuerySecurityResult {
  isSecure: boolean
  sanitizedQuery?: string
  errors: string[]
  warnings: string[]
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
}

/**
 * Database Query Security Validator
 */
export class QuerySecurityValidator {
  /**
   * Validate query for SQL injection attempts
   */
  static validateQuery(query: string): QuerySecurityResult {
    const errors: string[] = []
    const warnings: string[] = []
    let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low'

    // Normalize query for analysis
    const normalizedQuery = query.trim().toLowerCase()

    // Check for SQL injection patterns
    for (const pattern of SQL_INJECTION_PATTERNS) {
      if (pattern.test(query)) {
        errors.push('Potansiyel SQL injection tespit edildi')
        riskLevel = 'critical'
        break
      }
    }

    // Check for suspicious keywords
    const suspiciousKeywords = [
      'drop', 'truncate', 'alter', 'create', 'grant', 'revoke',
      'exec', 'execute', 'sp_', 'xp_', 'cmdshell'
    ]

    for (const keyword of suspiciousKeywords) {
      if (normalizedQuery.includes(keyword)) {
        errors.push(`Güvenlik nedeniyle engellenmiş anahtar kelime: ${keyword}`)
        riskLevel = 'critical'
      }
    }

    // Check for multiple statements
    if (query.includes(';') && query.split(';').filter(s => s.trim()).length > 1) {
      errors.push('Çoklu SQL ifadeleri güvenlik nedeniyle engellenmiştir')
      riskLevel = 'high'
    }

    // Check for comment attempts
    if (query.includes('--') || query.includes('/*')) {
      warnings.push('SQL yorumları potansiyel güvenlik riski')
      if (riskLevel === 'low') riskLevel = 'medium'
    }

    // Check for quote manipulation
    const singleQuotes = (query.match(/'/g) || []).length
    const doubleQuotes = (query.match(/"/g) || []).length
    
    if (singleQuotes % 2 !== 0 || doubleQuotes % 2 !== 0) {
      warnings.push('Dengesiz tırnak işaretleri tespit edildi')
      if (riskLevel === 'low') riskLevel = 'medium'
    }

    // Sanitize query if possible
    let sanitizedQuery = query
    if (errors.length === 0) {
      sanitizedQuery = this.sanitizeQuery(query)
    }

    return {
      isSecure: errors.length === 0,
      sanitizedQuery: errors.length === 0 ? sanitizedQuery : undefined,
      errors,
      warnings,
      riskLevel
    }
  }

  /**
   * Sanitize query by removing dangerous elements
   */
  private static sanitizeQuery(query: string): string {
    let sanitized = query

    // Remove SQL comments
    sanitized = sanitized.replace(/(--[^\r\n]*)/g, '')
    sanitized = sanitized.replace(/(\/\*[\s\S]*?\*\/)/g, '')

    // Normalize whitespace
    sanitized = sanitized.replace(/\s+/g, ' ').trim()

    return sanitized
  }

  /**
   * Validate Supabase query parameters
   */
  static validateSupabaseParams(params: Record<string, unknown>): {
    isValid: boolean
    sanitizedParams: Record<string, unknown>
    errors: string[]
  } {
    const errors: string[] = []
    const sanitizedParams: Record<string, unknown> = {}

    for (const [key, value] of Object.entries(params)) {
      // Validate parameter key
      if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(key)) {
        errors.push(`Geçersiz parametre adı: ${key}`)
        continue
      }

      // Sanitize parameter value
      if (typeof value === 'string') {
        const validation = this.validateQuery(value)
        if (!validation.isSecure) {
          errors.push(`Güvenli olmayan parametre değeri: ${key}`)
          continue
        }
        sanitizedParams[key] = validation.sanitizedQuery || value
      } else {
        sanitizedParams[key] = value
      }
    }

    return {
      isValid: errors.length === 0,
      sanitizedParams,
      errors
    }
  }

  /**
   * Safe database query wrapper
   */
  static async safeQuery<T = unknown>(
    query: string,
    params: Record<string, unknown> = {}
  ): Promise<{
    data: T[] | null
    error: string | null
    securityInfo: QuerySecurityResult
  }> {
    // Validate query security
    const securityResult = this.validateQuery(query)
    
    if (!securityResult.isSecure) {
      return {
        data: null,
        error: `Güvenlik nedeniyle sorgu engellenmiştir: ${securityResult.errors.join(', ')}`,
        securityInfo: securityResult
      }
    }

    // Validate parameters
    const paramValidation = this.validateSupabaseParams(params)
    if (!paramValidation.isValid) {
      return {
        data: null,
        error: `Güvenli olmayan parametreler: ${paramValidation.errors.join(', ')}`,
        securityInfo: securityResult
      }
    }

    try {
      // Execute query with Supabase
      const { data, error } = await supabase.rpc('safe_query_executor', {
        query_text: securityResult.sanitizedQuery,
        query_params: paramValidation.sanitizedParams
      })

      if (error) {
        console.error('Database query error:', error)
        return {
          data: null,
          error: 'Veritabanı sorgu hatası',
          securityInfo: securityResult
        }
      }

      return {
        data,
        error: null,
        securityInfo: securityResult
      }
    } catch (error) {
      console.error('Safe query execution failed:', error)
      return {
        data: null,
        error: 'Sorgu çalıştırma hatası',
        securityInfo: securityResult
      }
    }
  }

  /**
   * Validate filter parameters for Supabase queries
   */
  static validateFilters(filters: Record<string, unknown>): {
    isValid: boolean
    sanitizedFilters: Record<string, unknown>
    errors: string[]
  } {
    const errors: string[] = []
    const sanitizedFilters: Record<string, unknown> = {}

    for (const [column, value] of Object.entries(filters)) {
      // Validate column name (prevent injection through column names)
      if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(column)) {
        errors.push(`Geçersiz sütun adı: ${column}`)
        continue
      }

      // Validate and sanitize value
      if (typeof value === 'string') {
        // Check for SQL injection in filter values
        const queryValidation = this.validateQuery(value)
        if (queryValidation.riskLevel === 'critical' || queryValidation.riskLevel === 'high') {
          errors.push(`Güvenli olmayan filtre değeri: ${column}`)
          continue
        }
        
        // Sanitize string values
        sanitizedFilters[column] = value.replace(/['"]/g, '').trim()
      } else if (typeof value === 'number' || typeof value === 'boolean') {
        sanitizedFilters[column] = value
      } else if (Array.isArray(value)) {
        // Validate array values
        const sanitizedArray = value.filter(item => {
          if (typeof item === 'string') {
            const validation = this.validateQuery(item)
            return validation.riskLevel !== 'critical' && validation.riskLevel !== 'high'
          }
          return typeof item === 'number' || typeof item === 'boolean'
        })
        sanitizedFilters[column] = sanitizedArray
      } else {
        errors.push(`Desteklenmeyen filtre türü: ${column}`)
      }
    }

    return {
      isValid: errors.length === 0,
      sanitizedFilters,
      errors
    }
  }
}

/**
 * Secure Supabase query builder wrapper
 */
export class SecureSupabaseQuery {
  private tableName: string
  private filters: Record<string, unknown> = {}
  private selectFields: string[] = ['*']
  private orderBy: { column: string; ascending: boolean }[] = []
  private limitCount?: number

  constructor(tableName: string) {
    // Validate table name
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(tableName)) {
      throw new Error(`Geçersiz tablo adı: ${tableName}`)
    }
    this.tableName = tableName
  }

  /**
   * Add secure filter
   */
  filter(column: string, operator: string, value: unknown): this {
    // Validate column name
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(column)) {
      throw new Error(`Geçersiz sütun adı: ${column}`)
    }

    // Validate operator
    const allowedOperators = ['eq', 'neq', 'gt', 'gte', 'lt', 'lte', 'like', 'ilike', 'in', 'is']
    if (!allowedOperators.includes(operator)) {
      throw new Error(`Geçersiz operatör: ${operator}`)
    }

    this.filters[`${column}.${operator}`] = value
    return this
  }

  /**
   * Set select fields
   */
  select(fields: string): this {
    // Validate field names
    const fieldList = fields.split(',').map(f => f.trim())
    for (const field of fieldList) {
      if (field !== '*' && !/^[a-zA-Z_][a-zA-Z0-9_]*(\([^)]*\))?$/.test(field)) {
        throw new Error(`Geçersiz alan adı: ${field}`)
      }
    }
    this.selectFields = fieldList
    return this
  }

  /**
   * Add order by
   */
  order(column: string, ascending: boolean = true): this {
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(column)) {
      throw new Error(`Geçersiz sütun adı: ${column}`)
    }
    this.orderBy.push({ column, ascending })
    return this
  }

  /**
   * Set limit
   */
  limit(count: number): this {
    if (!Number.isInteger(count) || count < 0 || count > 1000) {
      throw new Error('Geçersiz limit değeri (0-1000 arası olmalı)')
    }
    this.limitCount = count
    return this
  }

  /**
   * Execute query securely
   */
  async execute<T = unknown>(): Promise<{
    data: T[] | null
    error: string | null
    count?: number
  }> {
    try {
      // Validate filters
      const filterValidation = QuerySecurityValidator.validateFilters(this.filters)
      if (!filterValidation.isValid) {
        return {
          data: null,
          error: `Güvenli olmayan filtreler: ${filterValidation.errors.join(', ')}`
        }
      }

      // Build and execute query
      let query = supabase
        .from(this.tableName)
        .select(this.selectFields.join(','))

      // Apply filters
      for (const [key, value] of Object.entries(filterValidation.sanitizedFilters)) {
        const [column, operator] = key.split('.')
        // Use type assertion for Supabase query methods
        const queryWithOperator = query as unknown as Record<string, (col: string, val: unknown) => unknown>
        queryWithOperator[operator](column, value)
      }

      // Apply ordering
      for (const { column, ascending } of this.orderBy) {
        query = query.order(column, { ascending })
      }

      // Apply limit
      if (this.limitCount) {
        query = query.limit(this.limitCount)
      }

      const { data, error, count } = await query

      if (error) {
        console.error('Secure query execution failed:', error)
        return {
          data: null,
          error: 'Veritabanı sorgu hatası'
        }
      }

      return { data: data as T[] | null, error: null, count: count || undefined }
    } catch (error) {
      console.error('Secure query builder error:', error)
      return {
        data: null,
        error: 'Sorgu oluşturma hatası'
      }
    }
  }
}

/**
 * React hook for secure database queries
 */
export function useSecureQuery() {
  return {
    validateQuery: QuerySecurityValidator.validateQuery,
    safeQuery: QuerySecurityValidator.safeQuery,
    validateFilters: QuerySecurityValidator.validateFilters,
    createSecureQuery: (tableName: string) => new SecureSupabaseQuery(tableName)
  }
}
