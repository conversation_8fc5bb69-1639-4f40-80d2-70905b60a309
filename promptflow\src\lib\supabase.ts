import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl) {
  throw new Error('Missing environment variable: NEXT_PUBLIC_SUPABASE_URL')
}

if (!supabaseAnonKey) {
  throw new Error('Missing environment variable: NEXT_PUBLIC_SUPABASE_ANON_KEY')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    storage: typeof window !== 'undefined' ? window.localStorage : undefined,
    flowType: 'pkce',
    debug: true, // Debug modunu açalım
    storageKey: 'sb-iqehopwgrczylqliajww-auth-token', // Explicit storage key
  },
  global: {
    headers: {
      'X-Client-Info': 'promptflow-web'
    }
  }
})

// Session debug için
if (typeof window !== 'undefined') {
  supabase.auth.onAuthStateChange((event, session) => {
    console.log(`🔐 [SUPABASE_CLIENT] Auth state change: ${event}`, {
      hasSession: !!session,
      userId: session?.user?.id,
      email: session?.user?.email,
      expiresAt: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : null
    })
  })
}

// Global auth error handler
if (typeof window !== 'undefined') {
  supabase.auth.onAuthStateChange((event) => {
    if (event === 'SIGNED_OUT') {
      // Oturum sonlandığında localStorage'ı temizle
      localStorage.removeItem('sb-' + supabaseUrl.split('//')[1].split('.')[0] + '-auth-token')
    }
  })
}

// Database types
export interface Database {
  public: {
    Tables: {
      projects: {
        Row: {
          id: string
          user_id: string
          name: string
          context_text: string
          description?: string
          status?: string
          tags?: string[]
          is_public?: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          context_text?: string
          description?: string
          status?: string
          tags?: string[]
          is_public?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          context_text?: string
          description?: string
          status?: string
          tags?: string[]
          is_public?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      prompts: {
        Row: {
          id: string
          project_id: string
          user_id: string
          prompt_text: string
          title: string | null
          description: string | null
          category: string | null
          tags: string[]
          order_index: number
          is_used: boolean
          is_favorite: boolean
          usage_count: number
          last_used_at: string | null
          task_code: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          user_id: string
          prompt_text: string
          title?: string
          description?: string
          category?: string
          tags?: string[]
          order_index: number
          is_used?: boolean
          is_favorite?: boolean
          usage_count?: number
          last_used_at?: string
          task_code?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          user_id?: string
          prompt_text?: string
          title?: string
          description?: string
          category?: string
          tags?: string[]
          order_index?: number
          is_used?: boolean
          is_favorite?: boolean
          usage_count?: number
          last_used_at?: string
          task_code?: string
          created_at?: string
          updated_at?: string
        }
      }
      plan_types: {
        Row: {
          id: string
          name: string
          display_name: string
          description: string | null
          price_monthly: number
          price_yearly: number
          max_projects: number
          max_prompts_per_project: number
          features: Record<string, boolean | string | number>
          is_active: boolean
          sort_order: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          display_name: string
          description?: string
          price_monthly?: number
          price_yearly?: number
          max_projects?: number
          max_prompts_per_project?: number
          features?: Record<string, boolean | string | number>
          is_active?: boolean
          sort_order?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          display_name?: string
          description?: string
          price_monthly?: number
          price_yearly?: number
          max_projects?: number
          max_prompts_per_project?: number
          features?: Record<string, boolean | string | number>
          is_active?: boolean
          sort_order?: number
          created_at?: string
          updated_at?: string
        }
      }
      user_plans: {
        Row: {
          id: string
          user_id: string
          plan_type_id: string
          status: string
          billing_cycle: string
          started_at: string
          expires_at: string | null
          cancelled_at: string | null
          auto_renew: boolean
          payment_method: string | null
          subscription_id: string | null
          metadata: Record<string, boolean | string | number>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          plan_type_id: string
          status?: string
          billing_cycle?: string
          started_at?: string
          expires_at?: string | null
          cancelled_at?: string | null
          auto_renew?: boolean
          payment_method?: string | null
          subscription_id?: string | null
          metadata?: Record<string, boolean | string | number>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          plan_type_id?: string
          status?: string
          billing_cycle?: string
          started_at?: string
          expires_at?: string | null
          cancelled_at?: string | null
          auto_renew?: boolean
          payment_method?: string | null
          subscription_id?: string | null
          metadata?: Record<string, boolean | string | number>
          created_at?: string
          updated_at?: string
        }
      }
      usage_stats: {
        Row: {
          id: string
          user_id: string
          stat_date: string
          projects_count: number
          prompts_count: number
          api_calls_count: number
          storage_used_mb: number
          last_activity_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          stat_date?: string
          projects_count?: number
          prompts_count?: number
          api_calls_count?: number
          storage_used_mb?: number
          last_activity_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          stat_date?: string
          projects_count?: number
          prompts_count?: number
          api_calls_count?: number
          storage_used_mb?: number
          last_activity_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      plan_transactions: {
        Row: {
          id: string
          user_id: string
          from_plan_id: string | null
          to_plan_id: string
          transaction_type: string
          amount: number
          currency: string
          payment_status: string
          payment_provider: string | null
          payment_reference: string | null
          notes: string | null
          processed_at: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          from_plan_id?: string | null
          to_plan_id: string
          transaction_type: string
          amount?: number
          currency?: string
          payment_status?: string
          payment_provider?: string | null
          payment_reference?: string | null
          notes?: string | null
          processed_at?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          from_plan_id?: string | null
          to_plan_id?: string
          transaction_type?: string
          amount?: number
          currency?: string
          payment_status?: string
          payment_provider?: string | null
          payment_reference?: string | null
          notes?: string | null
          processed_at?: string | null
          created_at?: string
        }
      }
    }
    Functions: {
      get_user_active_plan: {
        Args: { user_uuid: string }
        Returns: {
          plan_id: string
          plan_name: string
          display_name: string
          max_projects: number
          max_prompts_per_project: number
          features: Record<string, boolean | string | number>
          status: string
          expires_at: string | null
        }[]
      }
      check_user_limits: {
        Args: { user_uuid: string }
        Returns: {
          current_projects: number
          current_prompts: number
          max_projects: number
          max_prompts_per_project: number
          can_create_project: boolean
          can_create_prompt: boolean
        }[]
      }
      change_user_plan: {
        Args: {
          user_uuid: string
          new_plan_name: string
          billing_cycle_param?: string
          payment_reference_param?: string
        }
        Returns: string
      }
    }
  }
}