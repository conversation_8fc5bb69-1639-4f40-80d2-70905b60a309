'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { Hash, Folder } from 'lucide-react'
import { useAutoExpandingHeight } from '@/hooks/use-dynamic-height'

interface AutocompleteItem {
  id: string
  value: string
  type: 'hashtag' | 'folder'
  usage_count?: number
}

interface SmartAutocompleteProps {
  value: string
  onChange: (value: string) => void
  onKeyDown?: (e: React.KeyboardEvent) => void
  placeholder?: string
  className?: string
  suggestions: {
    hashtags: string[]
    folders: string[]
  }
  disabled?: boolean
  /** Enable dynamic height based on viewport */
  enableDynamicHeight?: boolean
  /** Custom height configuration */
  heightConfig?: {
    minHeight?: number
    maxHeightFraction?: number
    baseHeight?: number
  }
}

export function SmartAutocomplete({
  value,
  onChange,
  onKeyDown,
  placeholder,
  className,
  suggestions = { hashtags: [], folders: [] },
  disabled = false,
  enableDynamicHeight = true,
  heightConfig = {}
}: SmartAutocompleteProps) {
  const [showDropdown, setShowDropdown] = useState(false)
  const [filteredItems, setFilteredItems] = useState<AutocompleteItem[]>([])
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const [triggerChar, setTriggerChar] = useState<'#' | '/' | null>(null)
  const [triggerPosition, setTriggerPosition] = useState(-1)
  const [, setSearchTerm] = useState('')

  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Dynamic height management
  const { heightStyle } = useAutoExpandingHeight(
    textareaRef,
    value,
    enableDynamicHeight ? heightConfig : undefined
  )

  // Get cursor position and detect trigger characters
  const detectTrigger = useCallback((text: string, cursorPos: number) => {
    if (cursorPos === 0) return null

    // Look backwards from cursor to find trigger character
    for (let i = cursorPos - 1; i >= 0; i--) {
      const char = text[i]
      
      if (char === '#' || char === '/') {
        // Check if this is a valid trigger (not escaped or in middle of word)
        const prevChar = i > 0 ? text[i - 1] : ' '
        if (prevChar === ' ' || prevChar === '\n' || i === 0) {
          const searchText = text.slice(i + 1, cursorPos)
          // Only trigger if search term doesn't contain spaces
          if (!searchText.includes(' ') && !searchText.includes('\n')) {
            return {
              char: char as '#' | '/',
              position: i,
              searchTerm: searchText
            }
          }
        }
      }
      
      // Stop if we hit a space or newline
      if (char === ' ' || char === '\n') {
        break
      }
    }
    
    return null
  }, [])

  // Filter suggestions based on search term
  const filterSuggestions = useCallback((searchTerm: string, type: 'hashtag' | 'folder') => {
    const items = type === 'hashtag' ? (suggestions?.hashtags || []) : (suggestions?.folders || [])

    if (!items || items.length === 0) {
      return []
    }

    return items
      .filter(item => {
        if (!item || typeof item !== 'string') return false
        const cleanItem = item.replace(/^[#/]/, '') // Remove prefix for matching
        return cleanItem.toLowerCase().includes(searchTerm.toLowerCase())
      })
      .slice(0, 8) // Limit to 8 suggestions for performance
      .map((item, index) => ({
        id: `${type}-${index}`,
        value: item,
        type,
        usage_count: Math.floor(Math.random() * 100) // Mock usage count
      }))
      .sort((a, b) => (b.usage_count || 0) - (a.usage_count || 0)) // Sort by usage
  }, [suggestions])

  // Insert selected suggestion
  const insertSuggestion = useCallback((item: AutocompleteItem) => {
    if (triggerPosition === -1) return

    const beforeTrigger = value.slice(0, triggerPosition)
    const afterCursor = value.slice(textareaRef.current?.selectionStart || 0)

    // Ensure proper formatting
    const prefix = item.type === 'hashtag' ? '#' : '/'
    const suggestion = item.value.startsWith(prefix) ? item.value : `${prefix}${item.value}`

    const newValue = beforeTrigger + suggestion + ' ' + afterCursor
    onChange(newValue)

    // Set cursor position after the inserted suggestion
    setTimeout(() => {
      if (textareaRef.current) {
        const newCursorPos = beforeTrigger.length + suggestion.length + 1
        textareaRef.current.setSelectionRange(newCursorPos, newCursorPos)
        textareaRef.current.focus()
      }
    }, 0)

    setShowDropdown(false)
    setSelectedIndex(-1)
  }, [value, triggerPosition, onChange])

  // Handle text change and trigger detection
  const handleTextChange = useCallback((newValue: string) => {
    try {
      onChange(newValue)

      if (!textareaRef.current) return

      const cursorPos = textareaRef.current.selectionStart || 0
      const trigger = detectTrigger(newValue, cursorPos)

      if (trigger) {
        setTriggerChar(trigger.char)
        setTriggerPosition(trigger.position)
        setSearchTerm(trigger.searchTerm)

        const type = trigger.char === '#' ? 'hashtag' : 'folder'
        const filtered = filterSuggestions(trigger.searchTerm, type)
        setFilteredItems(filtered || [])
        setShowDropdown(filtered && filtered.length > 0)
        setSelectedIndex(-1)
      } else {
        setShowDropdown(false)
        setTriggerChar(null)
        setTriggerPosition(-1)
        setSearchTerm('')
        setFilteredItems([])
        setSelectedIndex(-1)
      }
    } catch (error) {
      console.error('Error in handleTextChange:', error)
      // Reset state on error
      setShowDropdown(false)
      setTriggerChar(null)
      setTriggerPosition(-1)
      setSearchTerm('')
      setFilteredItems([])
      setSelectedIndex(-1)
    }
  }, [onChange, detectTrigger, filterSuggestions])

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (showDropdown && filteredItems.length > 0) {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault()
          setSelectedIndex(prev =>
            prev < filteredItems.length - 1 ? prev + 1 : 0
          )
          break
        case 'ArrowUp':
          e.preventDefault()
          setSelectedIndex(prev =>
            prev > 0 ? prev - 1 : filteredItems.length - 1
          )
          break
        case 'Enter':
        case 'Tab':
          if (selectedIndex >= 0) {
            e.preventDefault()
            insertSuggestion(filteredItems[selectedIndex])
          }
          break
        case 'Escape':
          e.preventDefault()
          setShowDropdown(false)
          setSelectedIndex(-1)
          break
      }
    }

    onKeyDown?.(e)
  }, [showDropdown, filteredItems, selectedIndex, onKeyDown, insertSuggestion])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div className="relative">
      <textarea
        ref={textareaRef}
        value={value || ''}
        onChange={(e) => handleTextChange(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className={`${className} ${enableDynamicHeight ? 'dynamic-textarea' : ''}`}
        disabled={disabled}
        style={enableDynamicHeight ? heightStyle : { height: 'auto' }}
      />

      {showDropdown && filteredItems && filteredItems.length > 0 && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto"
        >
          <div className="p-2 text-xs text-gray-500 border-b border-gray-100">
            {triggerChar === '#' ? 'Hashtags' : 'Folders'} - Use ↑↓ to navigate, Enter to select
          </div>
          {filteredItems.map((item, index) => {
            if (!item || !item.id || !item.value) return null

            return (
              <div
                key={item.id}
                className={`flex items-center gap-2 px-3 py-2 cursor-pointer transition-colors ${
                  index === selectedIndex
                    ? 'bg-blue-50 text-blue-700'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => insertSuggestion(item)}
              >
                {item.type === 'hashtag' ? (
                  <Hash className="h-4 w-4 text-blue-500" />
                ) : (
                  <Folder className="h-4 w-4 text-orange-500" />
                )}
                <span className="flex-1 text-sm">
                  {item.value}
                </span>
                {item.usage_count && (
                  <span className="text-xs text-gray-400">
                    {item.usage_count} uses
                  </span>
                )}
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}

export default SmartAutocomplete
