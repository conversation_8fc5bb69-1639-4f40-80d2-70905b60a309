/* Optimized CSS for admin panel performance */

/* Critical CSS - Above the fold styles */
.critical-layout {
  display: flex;
  min-height: 100vh;
  background-color: #f9fafb;
}

.critical-sidebar {
  width: 280px;
  background-color: white;
  border-right: 1px solid #e5e7eb;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000;
}

.critical-main {
  flex: 1;
  margin-left: 280px;
  padding: 2rem;
}

/* Performance optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Reduce layout shifts */
.aspect-ratio-container {
  position: relative;
  width: 100%;
  height: 0;
}

.aspect-ratio-16-9 {
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.aspect-ratio-4-3 {
  padding-bottom: 75%; /* 4:3 aspect ratio */
}

.aspect-ratio-1-1 {
  padding-bottom: 100%; /* 1:1 aspect ratio */
}

.aspect-ratio-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Optimized animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Skeleton loading styles */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-text {
  height: 1rem;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
}

.skeleton-text:last-child {
  margin-bottom: 0;
  width: 60%;
}

.skeleton-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
}

.skeleton-button {
  height: 2.5rem;
  border-radius: 0.375rem;
  width: 6rem;
}

/* Performance-optimized table styles */
.optimized-table {
  contain: layout style paint;
  table-layout: fixed;
  width: 100%;
}

.optimized-table th,
.optimized-table td {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Virtual scrolling container */
.virtual-scroll-container {
  height: 400px;
  overflow-y: auto;
  contain: strict;
}

.virtual-scroll-item {
  contain: layout style paint;
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 1rem;
  border-bottom: 1px solid #e5e7eb;
}

/* Optimized focus styles */
.focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border: 2px solid;
  }
  
  .button {
    border: 2px solid;
  }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .critical-layout {
    background-color: #111827;
  }
  
  .critical-sidebar {
    background-color: #1f2937;
    border-right-color: #374151;
  }
  
  .skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
}

/* Container queries for responsive components */
@container (min-width: 400px) {
  .responsive-card {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 1rem;
  }
}

/* Utility classes for performance */
.contain-layout {
  contain: layout;
}

.contain-style {
  contain: style;
}

.contain-paint {
  contain: paint;
}

.contain-strict {
  contain: strict;
}

.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-scroll {
  will-change: scroll-position;
}
