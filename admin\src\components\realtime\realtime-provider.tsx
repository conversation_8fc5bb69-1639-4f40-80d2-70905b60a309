'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { queryKeys } from '@/lib/query-client';
import { useToast } from '@chakra-ui/react';

interface RealtimeContextType {
  isConnected: boolean;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  subscriptions: Map<string, any>;
  subscribe: (channel: string, table: string, filter?: string) => void;
  unsubscribe: (channel: string) => void;
}

const RealtimeContext = createContext<RealtimeContextType | null>(null);

interface RealtimeProviderProps {
  children: ReactNode;
}

export function RealtimeProvider({ children }: RealtimeProviderProps) {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<RealtimeContextType['connectionStatus']>('connecting');
  const [subscriptions, setSubscriptions] = useState(new Map());
  
  const queryClient = useQueryClient();
  const toast = useToast();

  useEffect(() => {
    // Monitor connection status
    const handleConnectionChange = () => {
      const status = supabase.realtime.isConnected() ? 'connected' : 'disconnected';
      setConnectionStatus(status);
      setIsConnected(status === 'connected');
      
      if (status === 'connected') {
        console.log('✅ Supabase Realtime connected');
      } else {
        console.log('❌ Supabase Realtime disconnected');
      }
    };

    // Initial connection check
    handleConnectionChange();

    // Listen for connection changes
    const interval = setInterval(handleConnectionChange, 5000);

    return () => {
      clearInterval(interval);
      // Clean up all subscriptions
      subscriptions.forEach((subscription) => {
        subscription.unsubscribe();
      });
    };
  }, [subscriptions]);

  const subscribe = (channel: string, table: string, filter?: string) => {
    // Prevent duplicate subscriptions
    if (subscriptions.has(channel)) {
      console.log(`Already subscribed to channel: ${channel}`);
      return;
    }

    console.log(`🔔 Subscribing to ${channel} for table ${table}`);

    const subscription = supabase
      .channel(channel)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: table,
          filter: filter,
        },
        (payload) => {
          console.log(`📡 Realtime update on ${table}:`, payload);
          handleRealtimeUpdate(table, payload);
        }
      )
      .subscribe((status) => {
        console.log(`Subscription status for ${channel}:`, status);
        
        if (status === 'SUBSCRIBED') {
          setSubscriptions(prev => new Map(prev.set(channel, subscription)));
        } else if (status === 'CHANNEL_ERROR') {
          setConnectionStatus('error');
          toast({
            title: 'Realtime bağlantı hatası',
            description: `${channel} kanalına bağlanılamadı`,
            status: 'error',
            duration: 5000,
          });
        }
      });
  };

  const unsubscribe = (channel: string) => {
    const subscription = subscriptions.get(channel);
    if (subscription) {
      subscription.unsubscribe();
      setSubscriptions(prev => {
        const newMap = new Map(prev);
        newMap.delete(channel);
        return newMap;
      });
      console.log(`🔕 Unsubscribed from ${channel}`);
    }
  };

  const handleRealtimeUpdate = (table: string, payload: any) => {
    const { eventType, new: newRecord, old: oldRecord } = payload;

    // Invalidate relevant queries based on table and event type
    switch (table) {
      case 'prompts':
        handlePromptsUpdate(eventType, newRecord, oldRecord);
        break;
      case 'projects':
        handleProjectsUpdate(eventType, newRecord, oldRecord);
        break;
      case 'admin_users':
        handleUsersUpdate(eventType, newRecord, oldRecord);
        break;
      default:
        console.log(`Unhandled table update: ${table}`);
    }
  };

  const handlePromptsUpdate = (eventType: string, newRecord: any, oldRecord: any) => {
    // Invalidate prompts queries
    queryClient.invalidateQueries({ queryKey: queryKeys.prompts.all });
    
    // Show toast notification for significant changes
    if (eventType === 'INSERT') {
      toast({
        title: 'Yeni prompt eklendi',
        description: 'Prompt listesi güncellendi',
        status: 'info',
        duration: 3000,
      });
    } else if (eventType === 'DELETE') {
      toast({
        title: 'Prompt silindi',
        description: 'Prompt listesi güncellendi',
        status: 'info',
        duration: 3000,
      });
    }
  };

  const handleProjectsUpdate = (eventType: string, newRecord: any, oldRecord: any) => {
    // Invalidate projects queries
    queryClient.invalidateQueries({ queryKey: queryKeys.projects.all });
    
    if (eventType === 'INSERT') {
      toast({
        title: 'Yeni proje oluşturuldu',
        description: 'Proje listesi güncellendi',
        status: 'info',
        duration: 3000,
      });
    }
  };

  const handleUsersUpdate = (eventType: string, newRecord: any, oldRecord: any) => {
    // Invalidate users queries
    queryClient.invalidateQueries({ queryKey: queryKeys.users.all });
    queryClient.invalidateQueries({ queryKey: queryKeys.users.stats });
    
    if (eventType === 'INSERT') {
      toast({
        title: 'Yeni kullanıcı kaydı',
        description: 'Kullanıcı listesi güncellendi',
        status: 'info',
        duration: 3000,
      });
    }
  };

  const value: RealtimeContextType = {
    isConnected,
    connectionStatus,
    subscriptions,
    subscribe,
    unsubscribe,
  };

  return (
    <RealtimeContext.Provider value={value}>
      {children}
    </RealtimeContext.Provider>
  );
}

// Hook to use realtime context
export function useRealtime() {
  const context = useContext(RealtimeContext);
  if (!context) {
    throw new Error('useRealtime must be used within a RealtimeProvider');
  }
  return context;
}

// Hook to subscribe to specific table changes
export function useRealtimeSubscription(table: string, filter?: string) {
  const context = useContext(RealtimeContext);
  const channel = `${table}_changes${filter ? `_${filter}` : ''}`;

  useEffect(() => {
    // Only subscribe if context is available (not during SSR or before provider initialization)
    if (!context) {
      return;
    }

    const { subscribe, unsubscribe } = context;
    subscribe(channel, table, filter);

    return () => {
      unsubscribe(channel);
    };
  }, [channel, table, filter, context]);

  return { channel };
}

// Hook for connection status indicator
export function useConnectionStatus() {
  const context = useContext(RealtimeContext);

  // Return default values if context is not available (during SSR or before provider initialization)
  if (!context) {
    return {
      isConnected: false,
      connectionStatus: 'connecting' as const
    };
  }

  const { isConnected, connectionStatus } = context;
  return { isConnected, connectionStatus };
}
