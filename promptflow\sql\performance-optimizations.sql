-- PromptFlow V2 Backend Performance Optimizations
-- This file contains SQL optimizations for Supabase backend

-- 1. Create composite index for smart sorting
-- This index optimizes the common query pattern: ORDER BY is_used ASC, order_index ASC
-- filtered by project_id
CREATE INDEX IF NOT EXISTS idx_prompts_project_smart_sort 
ON prompts(project_id, is_used, order_index);

-- 2. Additional performance indexes
-- Index for user-specific queries
CREATE INDEX IF NOT EXISTS idx_prompts_user_project 
ON prompts(user_id, project_id);

-- Index for task code lookups
CREATE INDEX IF NOT EXISTS idx_prompts_task_code 
ON prompts(task_code) WHERE task_code IS NOT NULL;

-- Index for hashtag searches (using GIN for array operations)
CREATE INDEX IF NOT EXISTS idx_prompts_tags_gin 
ON prompts USING GIN(tags);

-- Index for category filtering
CREATE INDEX IF NOT EXISTS idx_prompts_category 
ON prompts(category) WHERE category IS NOT NULL;

-- Index for usage tracking
CREATE INDEX IF NOT EXISTS idx_prompts_usage_tracking 
ON prompts(is_used, last_used_at);

-- 3. Bulk update RPC function for atomic operations
CREATE OR REPLACE FUNCTION bulk_update_prompts_order(
  prompt_updates jsonb
)
RETURNS TABLE(
  id uuid,
  order_index integer,
  task_code text,
  updated_at timestamptz
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  update_record jsonb;
  prompt_id uuid;
  new_order_index integer;
  new_task_code text;
  current_user_id uuid;
BEGIN
  -- Get current user ID
  current_user_id := auth.uid();
  
  -- Validate user is authenticated
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'Authentication required';
  END IF;

  -- Process each update in the batch
  FOR update_record IN SELECT * FROM jsonb_array_elements(prompt_updates)
  LOOP
    -- Extract values from JSON
    prompt_id := (update_record->>'id')::uuid;
    new_order_index := (update_record->>'order_index')::integer;
    new_task_code := update_record->>'task_code';
    
    -- Validate the user owns this prompt (RLS check)
    IF NOT EXISTS (
      SELECT 1 FROM prompts 
      WHERE prompts.id = prompt_id 
      AND prompts.user_id = current_user_id
    ) THEN
      RAISE EXCEPTION 'Access denied for prompt %', prompt_id;
    END IF;
    
    -- Update the prompt
    UPDATE prompts 
    SET 
      order_index = new_order_index,
      task_code = new_task_code,
      updated_at = now()
    WHERE prompts.id = prompt_id
    AND prompts.user_id = current_user_id;
    
    -- Return the updated record
    RETURN QUERY
    SELECT 
      prompts.id,
      prompts.order_index,
      prompts.task_code,
      prompts.updated_at
    FROM prompts
    WHERE prompts.id = prompt_id;
  END LOOP;
END;
$$;

-- 4. Optimized RLS policies
-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view own prompts" ON prompts;
DROP POLICY IF EXISTS "Users can insert own prompts" ON prompts;
DROP POLICY IF EXISTS "Users can update own prompts" ON prompts;
DROP POLICY IF EXISTS "Users can delete own prompts" ON prompts;

-- Create optimized RLS policies with better indexing support
CREATE POLICY "prompts_select_policy" ON prompts
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "prompts_insert_policy" ON prompts
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "prompts_update_policy" ON prompts
  FOR UPDATE 
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "prompts_delete_policy" ON prompts
  FOR DELETE 
  USING (auth.uid() = user_id);

-- 5. Performance monitoring views
-- Create a view for monitoring prompt performance
CREATE OR REPLACE VIEW prompt_performance_stats AS
SELECT 
  p.project_id,
  COUNT(*) as total_prompts,
  COUNT(*) FILTER (WHERE p.is_used = true) as used_prompts,
  COUNT(*) FILTER (WHERE p.is_used = false) as unused_prompts,
  AVG(p.usage_count) as avg_usage_count,
  MAX(p.updated_at) as last_activity
FROM prompts p
GROUP BY p.project_id;

-- 6. Cleanup and maintenance functions
-- Function to reorder prompts after deletions
CREATE OR REPLACE FUNCTION reorder_prompts_after_deletion(
  target_project_id uuid
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  prompt_record record;
  new_index integer := 1;
BEGIN
  -- Validate user owns the project
  IF NOT EXISTS (
    SELECT 1 FROM projects 
    WHERE id = target_project_id 
    AND user_id = auth.uid()
  ) THEN
    RAISE EXCEPTION 'Access denied for project %', target_project_id;
  END IF;

  -- Reorder prompts sequentially
  FOR prompt_record IN 
    SELECT id FROM prompts 
    WHERE project_id = target_project_id 
    ORDER BY order_index ASC
  LOOP
    UPDATE prompts 
    SET 
      order_index = new_index,
      task_code = 'task-' || new_index,
      updated_at = now()
    WHERE id = prompt_record.id;
    
    new_index := new_index + 1;
  END LOOP;
END;
$$;

-- 7. Grant necessary permissions
-- Grant execute permission on RPC functions to authenticated users
GRANT EXECUTE ON FUNCTION bulk_update_prompts_order(jsonb) TO authenticated;
GRANT EXECUTE ON FUNCTION reorder_prompts_after_deletion(uuid) TO authenticated;

-- Grant select permission on performance view
GRANT SELECT ON prompt_performance_stats TO authenticated;

-- 8. Create triggers for automatic maintenance
-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to prompts table
DROP TRIGGER IF EXISTS update_prompts_updated_at ON prompts;
CREATE TRIGGER update_prompts_updated_at 
  BEFORE UPDATE ON prompts 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- 9. Analyze tables for query planner optimization
ANALYZE prompts;
ANALYZE projects;

-- Performance optimization complete
-- These optimizations provide:
-- 1. Faster sorting with composite indexes
-- 2. Atomic bulk updates with RPC functions
-- 3. Optimized RLS policies
-- 4. Performance monitoring capabilities
-- 5. Automatic maintenance functions
