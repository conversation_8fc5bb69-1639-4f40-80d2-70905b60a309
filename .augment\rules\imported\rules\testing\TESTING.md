---
type: "testing_guide"
role: "testing_implementation_patterns"
dependencies:
  - "core/PROJECT_CORE.md"
  - "frontend/MAIN_APP.md"
  - "security/SECURITY.md"
  - "performance/PERFORMANCE.md"
related_testing:
  - "frontend/COMPONENT_TESTING.md"
  - "backend/API_TESTING.md"
  - "security/SECURITY_TESTING.md"
auto_update_from:
  - "frontend/MAIN_APP.md"
  - "security/SECURITY.md"
testing_implementations:
  - date: "2025-01-29"
    feature: "Project Name Editing"
    test_types: "Unit tests, security tests, accessibility tests, integration tests"
    coverage: "Component behavior, validation logic, error handling, performance"
    status: "implemented"
last_updated: "2025-01-29"
---

# PromptFlow Testing Implementation Guide

## Testing Strategy & Principles

### 1. Comprehensive Test Coverage

#### Component Testing Pattern
```typescript
// ✅ Complete component test suite
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ProjectNameEditor } from '../project-name-editor'

// Test wrapper with all required providers
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })
  
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('ProjectNameEditor', () => {
  const defaultProps = {
    projectId: 'project-1',
    currentName: 'Test Project 1',
    onNameUpdated: jest.fn()
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Display Mode', () => {
    it('renders project name correctly', () => {
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )
      expect(screen.getByText('Test Project 1')).toBeInTheDocument()
    })

    it('enters edit mode when edit button is clicked', async () => {
      const user = userEvent.setup()
      render(<TestWrapper><ProjectNameEditor {...defaultProps} /></TestWrapper>)

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      expect(screen.getByRole('textbox', { name: /proje adını düzenle/i })).toBeInTheDocument()
    })
  })

  describe('Edit Mode', () => {
    it('shows input with current name as value', async () => {
      const user = userEvent.setup()
      render(<TestWrapper><ProjectNameEditor {...defaultProps} /></TestWrapper>)

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      const input = screen.getByRole('textbox', { name: /proje adını düzenle/i })
      expect(input).toHaveValue('Test Project 1')
    })

    it('shows character counter', async () => {
      const user = userEvent.setup()
      render(<TestWrapper><ProjectNameEditor {...defaultProps} /></TestWrapper>)

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      expect(screen.getByText('16/50')).toBeInTheDocument()
    })
  })
})
```

### 2. Security Testing

#### Input Validation Security Tests
```typescript
// ✅ Comprehensive security test suite
describe('Security Tests', () => {
  describe('XSS Prevention', () => {
    it('prevents script injection attempts', () => {
      const injectionAttempts = [
        '<script>alert("xss")</script>',
        'javascript:alert(1)',
        '${alert(1)}',
        '{{alert(1)}}',
        '#{alert(1)}'
      ]

      injectionAttempts.forEach(attempt => {
        const result = validateProjectName(attempt)
        expect(result.isValid).toBe(false)
        expect(result.error).toContain('geçersiz karakterler')
      })
    })

    it('escapes HTML entities correctly', () => {
      expect(escapeHtml('<script>alert("xss")</script>'))
        .toBe('&lt;script&gt;alert("xss")&lt;/script&gt;')
      expect(escapeHtml('Test & Company'))
        .toBe('Test &amp; Company')
    })
  })

  describe('SQL Injection Prevention', () => {
    it('prevents SQL injection attempts', () => {
      const sqlInjections = [
        "'; DROP TABLE projects; --",
        '"; DELETE FROM projects; --',
        "' OR '1'='1",
        '" OR "1"="1',
        'UNION SELECT * FROM users'
      ]

      sqlInjections.forEach(injection => {
        const result = validateProjectName(injection)
        expect(result.isValid).toBe(false)
      })
    })
  })

  describe('Rate Limiting', () => {
    beforeEach(() => {
      resetClientRateLimit()
    })

    it('allows requests within limit', () => {
      for (let i = 0; i < PROJECT_NAME_RULES.rateLimit.maxRequests; i++) {
        expect(checkClientRateLimit()).toBe(true)
      }
    })

    it('blocks requests exceeding limit', () => {
      // Exhaust the limit
      for (let i = 0; i < PROJECT_NAME_RULES.rateLimit.maxRequests; i++) {
        checkClientRateLimit()
      }
      
      // Next request should be blocked
      expect(checkClientRateLimit()).toBe(false)
    })

    it('resets after time window', () => {
      // Mock time progression
      jest.useFakeTimers()
      
      // Exhaust limit
      for (let i = 0; i < PROJECT_NAME_RULES.rateLimit.maxRequests; i++) {
        checkClientRateLimit()
      }
      expect(checkClientRateLimit()).toBe(false)
      
      // Advance time past window
      jest.advanceTimersByTime(PROJECT_NAME_RULES.rateLimit.windowMinutes * 60 * 1000 + 1000)
      
      // Should allow requests again
      expect(checkClientRateLimit()).toBe(true)
      
      jest.useRealTimers()
    })
  })

  describe('Input Sanitization', () => {
    it('handles malicious unicode characters', () => {
      const maliciousInputs = [
        'Test\u0000Project', // Null byte
        'Test\u200BProject', // Zero-width space
        'Test\uFEFFProject', // Byte order mark
        'Test\u202EProject' // Right-to-left override
      ]

      maliciousInputs.forEach(input => {
        const result = validateProjectName(input)
        expect(result.isValid).toBe(false)
      })
    })

    it('handles very long input gracefully', () => {
      const veryLongString = 'A'.repeat(10000)
      const result = validateProjectName(veryLongString)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('en fazla 50 karakter')
    })
  })
})
```

### 3. Accessibility Testing

#### A11y Test Patterns
```typescript
// ✅ Accessibility testing
describe('Accessibility Tests', () => {
  it('has proper ARIA labels and roles', async () => {
    const user = userEvent.setup()
    render(<TestWrapper><ProjectNameEditor {...defaultProps} /></TestWrapper>)

    const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
    await user.click(editButton)

    const input = screen.getByRole('textbox', { name: /proje adını düzenle/i })
    expect(input).toHaveAttribute('aria-label', 'Proje adını düzenle')
    expect(input).toHaveAttribute('aria-describedby', 'project-name-feedback-project-1')
    expect(input).toHaveAttribute('aria-invalid', 'false')
  })

  it('has live region for validation feedback', async () => {
    const user = userEvent.setup()
    render(<TestWrapper><ProjectNameEditor {...defaultProps} /></TestWrapper>)

    const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
    await user.click(editButton)

    const input = screen.getByRole('textbox')
    await user.clear(input)

    await waitFor(() => {
      const feedback = screen.getByRole('status')
      expect(feedback).toHaveAttribute('aria-live', 'polite')
      expect(feedback).toHaveTextContent(/proje adı boş olamaz/i)
    })
  })

  it('supports keyboard navigation', async () => {
    const user = userEvent.setup()
    render(<TestWrapper><ProjectNameEditor {...defaultProps} /></TestWrapper>)

    // Tab to edit button
    await user.tab()
    expect(screen.getByRole('button', { name: /proje adını düzenle/i })).toHaveFocus()

    // Enter to start editing
    await user.keyboard('{Enter}')
    expect(screen.getByRole('textbox')).toHaveFocus()

    // Escape to cancel
    await user.keyboard('{Escape}')
    expect(screen.queryByRole('textbox')).not.toBeInTheDocument()
  })

  it('has sufficient color contrast', () => {
    render(<TestWrapper><ProjectNameEditor {...defaultProps} /></TestWrapper>)
    
    // Test color contrast ratios meet WCAG AA standards
    const projectName = screen.getByText('Test Project 1')
    const styles = window.getComputedStyle(projectName)
    
    // This would typically use a color contrast testing library
    expect(styles.color).toBeDefined()
    expect(styles.backgroundColor).toBeDefined()
  })
})
```

### 4. Performance Testing

#### Performance Test Patterns
```typescript
// ✅ Performance testing
describe('Performance Tests', () => {
  it('debounces validation calls efficiently', async () => {
    const mockValidator = jest.fn()
    const debouncedValidator = createDebouncedValidator(mockValidator, 100)
    
    // Rapid successive calls
    debouncedValidator('test1')
    debouncedValidator('test2')
    debouncedValidator('test3')
    
    // Should only call once after debounce period
    await new Promise(resolve => setTimeout(resolve, 150))
    expect(mockValidator).toHaveBeenCalledTimes(1)
    expect(mockValidator).toHaveBeenCalledWith('test3')
  })

  it('prevents memory leaks during rapid mount/unmount', () => {
    const initialMemory = performance.memory?.usedJSHeapSize || 0
    
    // Simulate rapid component lifecycle
    for (let i = 0; i < 100; i++) {
      const { unmount } = render(<TestWrapper><ProjectNameEditor {...defaultProps} /></TestWrapper>)
      unmount()
    }
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc()
    }
    
    const finalMemory = performance.memory?.usedJSHeapSize || 0
    const memoryIncrease = finalMemory - initialMemory
    
    // Memory increase should be minimal (less than 5MB)
    expect(memoryIncrease).toBeLessThan(5 * 1024 * 1024)
  })

  it('renders within performance budget', () => {
    const startTime = performance.now()
    
    render(<TestWrapper><ProjectNameEditor {...defaultProps} /></TestWrapper>)
    
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    // Should render within 16ms (60fps budget)
    expect(renderTime).toBeLessThan(16)
  })

  it('handles large project lists efficiently', () => {
    const largeProjectList = Array.from({ length: 1000 }, (_, i) => ({
      id: `project-${i}`,
      name: `Project ${i}`
    }))

    const startTime = performance.now()
    
    const result = validateProjectNameUnique('New Project', 'new-id', largeProjectList)
    
    const endTime = performance.now()
    const validationTime = endTime - startTime
    
    // Validation should complete quickly even with large datasets
    expect(validationTime).toBeLessThan(10) // 10ms
  })
})
```

### 5. Integration Testing

#### API Integration Tests
```typescript
// ✅ Integration testing with mocked API
describe('Integration Tests', () => {
  it('handles successful project name update', async () => {
    const user = userEvent.setup()
    const mockMutation = {
      mutateAsync: jest.fn().mockResolvedValue({
        id: 'project-1',
        name: 'Updated Name',
        updated_at: new Date().toISOString()
      }),
      isPending: false
    }

    // Mock the hook
    jest.mocked(useUpdateProjectNameSecure).mockReturnValue(mockMutation as any)

    const onNameUpdated = jest.fn()
    render(
      <TestWrapper>
        <ProjectNameEditor {...defaultProps} onNameUpdated={onNameUpdated} />
      </TestWrapper>
    )

    // Start editing
    const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
    await user.click(editButton)

    // Change name
    const input = screen.getByRole('textbox')
    await user.clear(input)
    await user.type(input, 'Updated Name')

    // Save
    const saveButton = screen.getByRole('button', { name: /proje adını kaydet/i })
    await user.click(saveButton)

    // Verify API call
    expect(mockMutation.mutateAsync).toHaveBeenCalledWith({
      projectId: 'project-1',
      newName: 'Updated Name'
    })

    // Verify callback
    await waitFor(() => {
      expect(onNameUpdated).toHaveBeenCalledWith('Updated Name')
    })
  })

  it('handles API errors gracefully', async () => {
    const user = userEvent.setup()
    const mockMutation = {
      mutateAsync: jest.fn().mockRejectedValue(new Error('Bu isimde bir proje zaten mevcut')),
      isPending: false
    }

    jest.mocked(useUpdateProjectNameSecure).mockReturnValue(mockMutation as any)

    render(<TestWrapper><ProjectNameEditor {...defaultProps} /></TestWrapper>)

    // Start editing and attempt save
    const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
    await user.click(editButton)

    const input = screen.getByRole('textbox')
    await user.clear(input)
    await user.type(input, 'Duplicate Name')

    const saveButton = screen.getByRole('button', { name: /proje adını kaydet/i })
    await user.click(saveButton)

    // Should remain in edit mode and show error
    await waitFor(() => {
      expect(screen.getByRole('textbox')).toBeInTheDocument()
    })
  })
})
```

### 6. Edge Case Testing

#### Edge Case Test Patterns
```typescript
// ✅ Edge case testing
describe('Edge Cases', () => {
  it('handles null and undefined inputs', () => {
    expect(sanitizeProjectName(null as any)).toBe('')
    expect(sanitizeProjectName(undefined as any)).toBe('')
    expect(validateProjectName(null as any).isValid).toBe(false)
    expect(validateProjectName(undefined as any).isValid).toBe(false)
  })

  it('handles empty project lists', async () => {
    const result = await validateProjectNameUnique('Test Project', 'new-id', [])
    expect(result.isValid).toBe(true)
  })

  it('handles network failures gracefully', async () => {
    // Mock network failure
    global.fetch = jest.fn().mockRejectedValue(new Error('Network error'))

    const result = await validateProjectNameUnique('Test', 'id', [])
    
    // Should handle gracefully without crashing
    expect(result).toBeDefined()
  })

  it('handles rapid state changes', async () => {
    const user = userEvent.setup()
    render(<TestWrapper><ProjectNameEditor {...defaultProps} /></TestWrapper>)

    const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
    
    // Rapid click sequence
    await user.click(editButton)
    await user.keyboard('{Escape}')
    await user.click(editButton)
    await user.keyboard('{Escape}')

    // Should handle gracefully without errors
    expect(screen.getByText('Test Project 1')).toBeInTheDocument()
  })
})
```

## Test Configuration

### Jest Setup
```typescript
// ✅ Jest configuration for PromptFlow
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/test-setup.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/test-setup.ts'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
}

// test-setup.ts
import '@testing-library/jest-dom'
import { TextEncoder, TextDecoder } from 'util'

global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}))
```

### Testing Scripts
```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:security": "jest --testPathPattern=security",
    "test:a11y": "jest --testPathPattern=accessibility",
    "test:performance": "jest --testPathPattern=performance"
  }
}
```

## Testing Checklist

### Before Deployment
- [ ] All unit tests passing
- [ ] Security tests covering XSS, SQL injection, rate limiting
- [ ] Accessibility tests for ARIA labels, keyboard navigation
- [ ] Performance tests for debouncing, memory usage
- [ ] Integration tests for API interactions
- [ ] Edge case tests for error scenarios
- [ ] Coverage threshold met (80%+)

### Ongoing Testing
- [ ] Add tests for new features
- [ ] Update tests when requirements change
- [ ] Regular security test reviews
- [ ] Performance regression testing
- [ ] Accessibility compliance testing

## Update Requirements
- Add test patterns for new features
- Update security tests for new attack vectors
- Maintain accessibility test coverage
- Document performance test baselines
- Update integration tests for API changes
- Regular test suite maintenance and optimization
