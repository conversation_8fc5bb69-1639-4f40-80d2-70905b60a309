---
type: "feature"
role: "categorization_guide"
dependencies:
  - "core/PROJECT_CORE.md"
  - "backend/SUPABASE.md"
  - "frontend/MAIN_APP.md"
related_features:
  - "features/CONTEXT_GALLERY.md"
  - "features/USER_PLANS.md"
auto_update_targets:
  - "features/CONTEXT_GALLERY.md"
development_history:
  - date: "2025-01-29"
    change: "Hashtag system integration with Context Gallery documented"
    integration_points: ["Prompt categorization", "Context templates", "Search functionality"]
last_updated: "2025-01-29"
---

# PromptFlow Hashtag & Categorization System

## Overview
Comprehensive hashtag and folder-based categorization system for organizing prompts with autocomplete, analytics, and filtering capabilities.

## Implementation
### Parsing System
```typescript
// Parse hashtags and folders from text
const { hashtags, category, cleanText } = parsePromptCategories(
  "Create a React component #frontend #react /components/ui"
)
// Result: hashtags: ['frontend', 'react'], category: '/components/ui'

// Preserve original text option
const result = parsePromptCategoriesPreserveText(promptText)

// Merge hashtags utility
const mergedTags = mergeHashtags(existingTags, newTags)
```

### Database Integration
```sql
-- Prompt table includes categorization
prompts (
  id, project_id, user_id,
  prompt_text,        -- Clean text without hashtags/folders
  title, description,
  category,           -- Folder path (/frontend/components)
  tags,              -- Array of hashtags ['frontend', 'react']
  order_index, created_at
)

-- Indexes for performance
CREATE INDEX idx_prompts_tags_gin ON prompts USING gin(tags);
CREATE INDEX idx_prompts_category ON prompts(category);
CREATE INDEX idx_prompts_category_pattern ON prompts(category text_pattern_ops);
```

## Frontend Components
### Input Components
```typescript
// Smart input with autocomplete
<HashtagInput 
  value={hashtags}
  onChange={setHashtags}
  suggestions={popularHashtags}
  placeholder="Add hashtags..."
  maxTags={5}
/>

<CategorySelector
  value={category}
  onChange={setCategory}
  suggestions={popularCategories}
  placeholder="Select folder..."
  allowCreate={true}
/>
```

### Sidebar & Navigation
```typescript
// Popular hashtags sidebar
<PopularHashtagsSidebar 
  projectId={projectId}
  onHashtagClick={filterByHashtag}
  showUsageCount={true}
  limit={20}
/>

// Category navigation
<CategoryBreadcrumb 
  category={currentCategory}
  onNavigate={navigateToCategory}
/>

// Filter chips
<FilterChips 
  activeFilters={filters}
  onRemoveFilter={removeFilter}
  onClearAll={clearAllFilters}
/>
```

### Filtering System
```typescript
// Multi-filter support
const { data: filteredPrompts } = useFilteredPrompts(projectId, {
  hashtags: ['frontend', 'react'],
  category: '/components',
  searchQuery: 'button'
})

// Advanced filtering
const advancedFilter = {
  hashtags: {
    include: ['frontend', 'react'],
    exclude: ['deprecated']
  },
  categories: ['/components', '/hooks'],
  dateRange: {
    from: '2024-01-01',
    to: '2024-12-31'
  }
}
```

## Analytics & Insights
### Usage Statistics
```typescript
// Hashtag popularity
const { data: popularTags } = usePopularHashtags(projectId, 20)
// Returns: [{ hashtag: 'frontend', count: 15 }, ...]

// Category usage
const { data: topCategories } = usePopularCategories(projectId)
// Returns: [{ category: '/frontend', count: 8 }, ...]

// Categorization rate
const { data: stats } = useCategorizationStats(projectId)
// Returns: { total: 100, categorized: 85, rate: 85% }
```

### Recommendations
```typescript
// Smart suggestions based on content
const suggestions = await getSmartSuggestions(promptText)
// AI-powered hashtag and category recommendations

// Similar prompts
const similar = await findSimilarPrompts(hashtags, category)

// Trending hashtags
const trending = await getTrendingHashtags({
  timeframe: 'week',
  projectId: projectId
})
```

## Best Practices
### Naming Conventions
```typescript
// Hashtag guidelines
const hashtagRules = {
  format: 'lowercase',
  separator: 'none', // no spaces or special chars
  maxLength: 20,
  examples: ['frontend', 'backend', 'ui', 'api', 'database']
}

// Category structure
const categoryRules = {
  format: '/path/structure',
  maxDepth: 3,
  separator: '/',
  examples: ['/frontend', '/frontend/components', '/backend/api']
}
```

### Performance Optimization
```typescript
// Debounced autocomplete
const debouncedSearch = useDebounce(searchTerm, 300)

// Cached suggestions
const { data: suggestions } = useHashtagSuggestions(debouncedSearch, {
  staleTime: 5 * 60 * 1000 // 5 minutes cache
})

// Virtualized lists for large datasets
<VirtualizedHashtagList
  items={allHashtags}
  itemHeight={32}
  containerHeight={400}
/>
```

## Integration Points
### Prompt Creation
```typescript
// Auto-parse during creation
const handleCreatePrompt = async (text: string) => {
  const { hashtags, category, cleanText } = parsePromptCategories(text)

  await createPrompt({
    prompt_text: cleanText,
    tags: hashtags,
    category: category,
    project_id: projectId
  })
}

// Bulk categorization
const bulkCategorize = async (promptIds: string[], tags: string[], category: string) => {
  await bulkUpdatePrompts(promptIds, {
    tags: tags,
    category: category
  })
}
```

### Search Enhancement
```typescript
// Enhanced search including categories
const searchResults = await searchPrompts({
  query: searchTerm,
  includeHashtags: true,
  includeCategories: true,
  fuzzyMatch: true,
  filters: {
    tags: selectedTags,
    category: selectedCategory
  }
})

// Search analytics
const trackSearch = async (query: string, filters: any, resultCount: number) => {
  await logSearchEvent({
    query,
    filters,
    result_count: resultCount,
    timestamp: new Date()
  })
}
```

## Autocomplete System
### Smart Suggestions
```typescript
// Context-aware suggestions
const getContextualSuggestions = async (
  currentText: string,
  projectId: string,
  existingTags: string[]
) => {
  // Analyze current text for context
  const context = analyzeTextContext(currentText)

  // Get project-specific popular tags
  const projectTags = await getProjectPopularTags(projectId)

  // Filter out existing tags
  const suggestions = projectTags
    .filter(tag => !existingTags.includes(tag.name))
    .filter(tag => tag.name.includes(context.keywords))
    .slice(0, 10)

  return suggestions
}
```

## Update Requirements
- Update this file when modifying categorization logic
- Document new parsing patterns
- Maintain performance optimizations
- Keep analytics features current
- Update autocomplete algorithms when improving suggestions
- Document new search and filtering capabilities
