"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabaseBrowser as supabase } from "@/lib/supabase-browser";
import { Context, ContextCategory } from "@/components/context-gallery";

// Context Categories Hooks
export function useContextCategories() {
  return useQuery({
    queryKey: ["context-categories"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("context_categories")
        .select("*")
        .eq("is_active", true)
        .order("sort_order", { ascending: true });

      if (error) throw error;
      return data as ContextCategory[];
    },
    staleTime: 5 * 60 * 1000, // 5 dakika
  });
}

// Get all contexts with filters
export function useContexts(filters?: {
  category_id?: string;
  is_public?: boolean;
  is_template?: boolean;
  is_featured?: boolean;
  search?: string;
  author_id?: string;
}) {
  return useQuery({
    queryKey: ["contexts", filters],
    queryFn: async () => {
      let query = supabase
        .from("contexts")
        .select(`
          *,
          category:context_categories(*)
        `)
        .eq("status", "active");

      // Apply filters
      if (filters?.category_id) {
        query = query.eq("category_id", filters.category_id);
      }
      
      if (filters?.is_public !== undefined) {
        query = query.eq("is_public", filters.is_public);
      }
      
      if (filters?.is_template !== undefined) {
        query = query.eq("is_template", filters.is_template);
      }
      
      if (filters?.is_featured !== undefined) {
        query = query.eq("is_featured", filters.is_featured);
      }

      if (filters?.author_id) {
        query = query.eq("author_id", filters.author_id);
      }

      if (filters?.search) {
        query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%,tags.cs.{${filters.search}}`);
      }

      // Order by featured first, then by usage count
      query = query.order("is_featured", { ascending: false })
                   .order("usage_count", { ascending: false });

      const { data, error } = await query;

      if (error) throw error;

      // Transform data to match Context interface
      return data.map((item) => ({
        id: item.id,
        title: item.title,
        description: item.description,
        content: item.content,
        category: item.category,
        author_id: item.author_id,
        author_name: 'Kullanıcı', // Şimdilik static, sonra profiles tablosu eklenebilir
        is_public: item.is_public,
        is_featured: item.is_featured,
        is_template: item.is_template,
        tags: item.tags || [],
        usage_count: item.usage_count,
        like_count: item.like_count,
        view_count: item.view_count,
        approval_status: item.approval_status || 'approved',
        approved_by: item.approved_by,
        approved_at: item.approved_at,
        approval_notes: item.approval_notes,
        created_at: item.created_at,
        updated_at: item.updated_at,
      })) as Context[];
    },
    staleTime: 2 * 60 * 1000, // 2 dakika
  });
}

// Get single context
export function useContext(id: string) {
  return useQuery({
    queryKey: ["context", id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("contexts")
        .select(`
          *,
          category:context_categories(*)
        `)
        .eq("id", id)
        .single();

      if (error) throw error;

      return {
        id: data.id,
        title: data.title,
        description: data.description,
        content: data.content,
        category: data.category,
        author_id: data.author_id,
        author_name: 'Kullanıcı', // Şimdilik static, sonra profiles tablosu eklenebilir
        is_public: data.is_public,
        is_featured: data.is_featured,
        is_template: data.is_template,
        tags: data.tags || [],
        usage_count: data.usage_count,
        like_count: data.like_count,
        view_count: data.view_count,
        created_at: data.created_at,
        updated_at: data.updated_at,
      } as Context;
    },
    enabled: !!id,
  });
}

// Create context
export function useCreateContext() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (newContext: {
      title: string;
      description?: string;
      content: string;
      category_id: string;
      is_public?: boolean;
      is_template?: boolean;
      tags?: string[];
    }) => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error("Kullanıcı girişi gerekli");

      const { data, error } = await supabase
        .from("contexts")
        .insert({
          ...newContext,
          author_id: user.user.id,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contexts"] });
    },
  });
}

// Update context
export function useUpdateContext() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      updates
    }: {
      id: string;
      updates: {
        title?: string;
        description?: string;
        content?: string;
        category_id?: string;
        is_public?: boolean;
        is_template?: boolean;
        is_featured?: boolean;
        tags?: string[];
        status?: 'active' | 'inactive' | 'archived';
      }
    }) => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error("Kullanıcı girişi gerekli");

      const { data, error } = await supabase
        .from("contexts")
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .eq("author_id", user.user.id) // Sadece kendi context'lerini güncelleyebilir
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ["contexts"] });
      queryClient.invalidateQueries({ queryKey: ["context", variables.id] });
    },
  });
}

// Delete context
export function useDeleteContext() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error("Kullanıcı girişi gerekli");

      const { error } = await supabase
        .from("contexts")
        .delete()
        .eq("id", id)
        .eq("author_id", user.user.id); // Sadece kendi context'lerini silebilir

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contexts"] });
    },
  });
}

// Approve/Reject context (Admin only)
export function useApproveContext() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      contextId,
      status,
      notes
    }: {
      contextId: string;
      status: 'approved' | 'rejected';
      notes?: string
    }) => {
      const { error } = await supabase.rpc('approve_context', {
        context_id_param: contextId,
        approval_status_param: status,
        approval_notes_param: notes
      });

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contexts"] });
    },
  });
}

// Use context (increment usage count)
export function useUseContext() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      contextId, 
      projectId 
    }: { 
      contextId: string; 
      projectId?: string; 
    }) => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error("Kullanıcı girişi gerekli");

      const { error } = await supabase.rpc("increment_context_usage", {
        context_id_param: contextId,
        user_id_param: user.user.id,
        project_id_param: projectId || null,
      });

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contexts"] });
    },
  });
}

// Toggle like
export function useToggleContextLike() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (contextId: string) => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error("Kullanıcı girişi gerekli");

      const { data, error } = await supabase.rpc("toggle_context_like", {
        context_id_param: contextId,
        user_id_param: user.user.id,
      });

      if (error) throw error;
      return data; // Returns true if liked, false if unliked
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contexts"] });
    },
  });
}

// Get user's liked contexts
export function useUserLikedContexts() {
  return useQuery({
    queryKey: ["user-liked-contexts"],
    queryFn: async () => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error("Kullanıcı girişi gerekli");

      const { data, error } = await supabase
        .from("context_likes")
        .select("context_id")
        .eq("user_id", user.user.id);

      if (error) throw error;
      return data.map(item => item.context_id);
    },
  });
}

// Get context statistics
export function useContextStats() {
  return useQuery({
    queryKey: ["context-stats"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("contexts")
        .select("is_public, is_template, is_featured, status")
        .eq("status", "active");

      if (error) throw error;

      const stats = {
        total: data.length,
        public: data.filter(c => c.is_public).length,
        private: data.filter(c => !c.is_public).length,
        templates: data.filter(c => c.is_template).length,
        featured: data.filter(c => c.is_featured).length,
      };

      return stats;
    },
    staleTime: 5 * 60 * 1000, // 5 dakika
  });
} 