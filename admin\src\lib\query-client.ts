import { QueryClient } from '@tanstack/react-query';

// Create a query client with optimized settings for admin panel
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Cache data for 5 minutes by default (longer for stats)
      staleTime: 5 * 60 * 1000,
      // Keep data in cache for 15 minutes
      gcTime: 15 * 60 * 1000,
      // Retry failed requests 2 times
      retry: 2,
      // Don't refetch on window focus for admin panel
      refetchOnWindowFocus: false,
      // Don't refetch on reconnect for admin panel
      refetchOnReconnect: false,
      // Enable background refetching for fresh data
      refetchOnMount: 'always',
      // Refetch stale data in background
      refetchInterval: false, // Disable automatic refetching
    },
    mutations: {
      // Retry failed mutations once
      retry: 1,
      // Optimistic updates for better UX
      onMutate: async () => {
        // Cancel outgoing refetches
        await queryClient.cancelQueries();
      },
    },
  },
});

// Query keys for consistent caching
export const queryKeys = {
  // Dashboard queries
  dashboard: {
    stats: ['dashboard', 'stats'] as const,
    recentActivity: ['dashboard', 'recent-activity'] as const,
    topProjects: ['dashboard', 'top-projects'] as const,
  },
  
  // User queries
  users: {
    all: ['users'] as const,
    list: (filters?: Record<string, any>) => ['users', 'list', filters] as const,
    infinite: (filters?: Record<string, any>) => ['users', 'infinite', filters] as const,
    detail: (id: string) => ['users', 'detail', id] as const,
    stats: ['users', 'stats'] as const,
  },
  
  // Project queries
  projects: {
    all: ['projects'] as const,
    list: (filters?: Record<string, any>) => ['projects', 'list', filters] as const,
    detail: (id: string) => ['projects', 'detail', id] as const,
    stats: ['projects', 'stats'] as const,
  },
  
  // Prompt queries
  prompts: {
    all: ['prompts'] as const,
    list: (filters?: Record<string, any>) => ['prompts', 'list', filters] as const,
    detail: (id: string) => ['prompts', 'detail', id] as const,
    stats: ['prompts', 'stats'] as const,
  },
  
  // Context queries
  contexts: {
    all: ['contexts'] as const,
    list: (filters?: Record<string, any>) => ['contexts', 'list', filters] as const,
    detail: (id: string) => ['contexts', 'detail', id] as const,
  },
  
  // Admin queries
  admin: {
    profile: ['admin', 'profile'] as const,
    permissions: ['admin', 'permissions'] as const,
    settings: ['admin', 'settings'] as const,
    status: (userId: string) => ['admin', 'status', userId] as const,
  },
} as const;

// Cache invalidation helpers
export const invalidateQueries = {
  dashboard: () => queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.stats }),
  users: () => queryClient.invalidateQueries({ queryKey: queryKeys.users.all }),
  projects: () => queryClient.invalidateQueries({ queryKey: queryKeys.projects.all }),
  prompts: () => queryClient.invalidateQueries({ queryKey: queryKeys.prompts.all }),
  all: () => queryClient.invalidateQueries(),
};
