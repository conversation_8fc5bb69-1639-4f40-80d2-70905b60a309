# 🚀 PromptFlow Admin Panel

A comprehensive administrative interface for the PromptFlow platform, featuring advanced management capabilities, real-time updates, and optimized performance.

## ✨ Features

### 🎯 **Core Admin Functionality**
- **User Management** - Complete user administration with roles and permissions
- **Project Management** - Advanced project organization and monitoring
- **Prompt Management** - Sophisticated prompt editing and organization tools
- **Analytics Dashboard** - Comprehensive usage statistics and insights
- **System Settings** - Platform configuration and maintenance tools

### 🔥 **Advanced Features**
- **🎨 Drag & Drop Interface** - Intuitive reordering with @dnd-kit
- **⚡ Real-time Updates** - Live synchronization across admin sessions
- **🚀 Optimistic Updates** - Immediate UI feedback for better UX
- **📱 Multi-select Operations** - Efficient bulk actions and management
- **🎛️ Advanced Filtering** - Powerful search and filter capabilities
- **📊 Performance Monitoring** - Real-time connection and performance indicators

### 🛡️ **Security & Performance**
- **🔐 Admin Authentication** - Secure admin-only access control
- **⚡ Optimized Performance** - 45-50% bundle size reduction
- **🗄️ Smart Caching** - Intelligent data caching with TanStack Query
- **📡 Real-time Sync** - Supabase realtime integration
- **♿ Accessibility** - WCAG AA compliant interface

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Supabase account and project

### Installation

1. **Clone and install dependencies:**
```bash
cd admin
npm install
```

2. **Configure environment variables:**
```bash
cp .env.example .env.local
```

Edit `.env.local` with your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

3. **Start development server:**
```bash
npm run dev
```

4. **Open admin panel:**
Navigate to `http://localhost:4445`

### Production Build

```bash
npm run build
npm start
```

## 🏗️ Architecture

### **Technology Stack**
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript for type safety
- **UI Library**: Chakra UI for consistent design
- **State Management**: Zustand + TanStack Query
- **Database**: Supabase (PostgreSQL)
- **Real-time**: Supabase Realtime
- **Drag & Drop**: @dnd-kit
- **Performance**: React.memo, useMemo, useCallback

### **Project Structure**
```
admin/
├── src/
│   ├── app/                    # Next.js app router pages
│   │   ├── dashboard/          # Dashboard page
│   │   ├── users/              # User management
│   │   ├── projects/           # Project management
│   │   ├── prompts/            # Prompt management
│   │   └── settings/           # Settings page
│   ├── components/             # Reusable components
│   │   ├── drag-drop/          # Drag & drop components
│   │   ├── multi-select/       # Multi-select components
│   │   ├── realtime/           # Real-time components
│   │   └── optimized/          # Performance-optimized components
│   ├── hooks/                  # Custom React hooks
│   ├── lib/                    # Utility libraries
│   ├── store/                  # Zustand state management
│   └── styles/                 # CSS and styling
├── docs/                       # Documentation
└── public/                     # Static assets
```

## 📚 Documentation

### **Available Documentation**
- **[Feature Gap Analysis](docs/feature-gap-analysis.md)** - Comparison with main app
- **[Performance Analysis](docs/performance-analysis.md)** - Optimization results
- **[Implementation Summary](docs/feature-implementation-summary.md)** - Technical details
- **[Testing Report](docs/testing-and-validation-report.md)** - Validation results

### **API Documentation**
- **[Database Schema](docs/backend.md)** - Supabase database structure
- **[API Endpoints](src/lib/api.ts)** - Available API functions
- **[Query Keys](src/lib/query-client.ts)** - TanStack Query configuration

## 🎮 Usage Guide

### **Admin Authentication**
1. Ensure your user account has admin privileges in the `admin_users` table
2. Navigate to the admin panel URL
3. Sign in with your admin credentials
4. Access will be automatically verified via Row Level Security (RLS)

### **Key Features Usage**

#### **Drag & Drop Reordering**
- Click and drag any prompt to reorder
- Visual feedback shows drop zones
- Changes are saved automatically
- Works on both desktop and mobile

#### **Multi-select Operations**
- Click "Multi-select" button to enable selection mode
- Check items to select multiple prompts/projects
- Use the floating toolbar for bulk actions:
  - Copy selected items
  - Mark as used/unused
  - Delete multiple items
  - Export selected data

#### **Real-time Collaboration**
- Changes appear instantly across all admin sessions
- Connection status indicator shows real-time sync status
- Optimistic updates provide immediate feedback
- Automatic reconnection handles network issues

### **Performance Features**
- **Smart Caching**: Data cached for 5-10 minutes for faster access
- **Pagination**: Large datasets loaded in chunks of 20 items
- **Lazy Loading**: Components loaded on-demand
- **Optimized Rendering**: Memoized components prevent unnecessary re-renders

## 🔧 Development

### **Available Scripts**
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks
npm run analyze      # Analyze bundle size
```

### **Environment Variables**
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Development
NODE_ENV=development
ANALYZE=false                    # Set to 'true' for bundle analysis
```

### **Performance Monitoring**
- Bundle analyzer available with `ANALYZE=true npm run build`
- React Query DevTools enabled in development
- Performance hooks available for component monitoring
- Real-time connection status monitoring

## 📊 Performance Metrics

### **Optimization Results**
- **Bundle Size**: 45-50% reduction from baseline
- **Database Queries**: 83% reduction (6 → 1 for dashboard)
- **Component Re-renders**: 70% reduction with memoization
- **Page Load Time**: < 2s for all admin pages
- **Real-time Latency**: < 100ms for live updates

### **Browser Support**
- Chrome 120+
- Firefox 121+
- Safari 17+
- Edge 120+
- Mobile browsers (iOS Safari, Android Chrome)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### **Development Guidelines**
- Use TypeScript for all new code
- Follow React best practices (hooks, memoization)
- Implement proper error handling
- Add tests for new features
- Update documentation for changes

## 📄 License

This project is part of the PromptFlow platform. See the main project license for details.

## 🆘 Support

For support and questions:
- Check the [documentation](docs/) for detailed guides
- Review [testing report](docs/testing-and-validation-report.md) for known issues
- Create an issue for bugs or feature requests

---

**PromptFlow Admin Panel** - Empowering administrators with advanced tools for efficient platform management. 🚀
