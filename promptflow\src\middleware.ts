import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase-server'

export async function middleware(req: NextRequest) {
  const startTime = Date.now()
  const pathname = req.nextUrl.pathname

  console.log(`🔄 [MIDDLEWARE] ${req.method} ${pathname} - Starting...`)

  const response = NextResponse.next({
    request: {
      headers: req.headers,
    },
  })

  const supabase = createSupabaseServerClient(req, response)

  try {
    // Refresh session if expired - required for Server Components
    console.log(`🔐 [MIDDLEWARE] Getting session...`)
    const {
      data: { session },
      error: sessionError
    } = await supabase.auth.getSession()

    if (sessionError) {
      console.error(`❌ [MIDDLEWARE] Session error:`, sessionError)
    }

    // Also try to get user to double-check
    let user = null
    if (session) {
      try {
        const { data: { user: currentUser }, error: userError } = await supabase.auth.getUser()
        if (userError) {
          console.warn(`⚠️ [MIDDLEWARE] User verification error:`, userError)
        } else {
          user = currentUser
        }
      } catch (e) {
        console.warn(`⚠️ [MIDDLEWARE] Could not verify user:`, e)
      }
    }

    // Check if session is expired
    const isSessionExpired = session && session.expires_at ? new Date(session.expires_at * 1000) < new Date() : false

    // Session is valid if we have both session and user and it's not expired
    const hasValidSession = !!(session && session.user && user && !isSessionExpired)

    console.log(`🔐 [MIDDLEWARE] Session status: ${hasValidSession ? `VALID (user: ${user?.email})` : 'INVALID/NO_SESSION'}`)
    console.log(`🔐 [MIDDLEWARE] Session details:`, {
      hasSession: !!session,
      hasSessionUser: !!session?.user,
      hasUser: !!user,
      sessionExpiry: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : 'N/A',
      isExpired: isSessionExpired
    })

    // Protected routes that require authentication
    const protectedRoutes = ['/dashboard', '/profile', '/settings']
    const isProtectedRoute = protectedRoutes.some(route =>
      pathname.startsWith(route)
    )

    console.log(`🛡️ [MIDDLEWARE] Route protection: ${pathname} is ${isProtectedRoute ? 'PROTECTED' : 'PUBLIC'}`)

    // If accessing a protected route without a valid session, redirect to auth page
    if (isProtectedRoute && !hasValidSession) {
      console.log(`🚫 [MIDDLEWARE] REDIRECT: ${pathname} -> /auth (no valid session)`)
      const redirectUrl = new URL('/auth', req.url)
      return NextResponse.redirect(redirectUrl)
    }

    // If user is logged in and trying to access auth page, redirect to dashboard
    if (pathname === '/auth' && hasValidSession) {
      console.log(`🚫 [MIDDLEWARE] REDIRECT: /auth -> /dashboard (has valid session)`)
      const redirectUrl = new URL('/dashboard', req.url)
      return NextResponse.redirect(redirectUrl)
    }

    // If user is logged in and trying to access root, redirect to dashboard
    if (pathname === '/' && hasValidSession) {
      console.log(`🚫 [MIDDLEWARE] REDIRECT: / -> /dashboard (has valid session)`)
      const redirectUrl = new URL('/dashboard', req.url)
      return NextResponse.redirect(redirectUrl)
    }

    const duration = Date.now() - startTime
    console.log(`✅ [MIDDLEWARE] ${pathname} - Completed in ${duration}ms (session: ${hasValidSession ? 'VALID' : 'INVALID'})`)
    return response

  } catch (error) {
    console.error(`💥 [MIDDLEWARE] Error:`, error)
    return response
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
