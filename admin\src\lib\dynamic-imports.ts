// Dynamic imports for heavy libraries to reduce initial bundle size

// Chart library - only load when needed
export const loadChartLibrary = async () => {
  const { default: Chart } = await import('recharts');
  return Chart;
};

// Date utilities - only load when needed
export const loadDateUtils = async () => {
  const { format, parseISO, formatDistanceToNow } = await import('date-fns');
  const { tr } = await import('date-fns/locale');
  return { format, parseISO, formatDistanceToNow, tr };
};

// Form validation - only load when needed
export const loadFormValidation = async () => {
  const { z } = await import('zod');
  const { zodResolver } = await import('@hookform/resolvers/zod');
  return { z, zodResolver };
};

// CSV export - only load when needed
export const loadCSVExport = async () => {
  // Dynamically import a CSV library when export is needed
  const Papa = await import('papaparse');
  return Papa.default;
};

// PDF export - only load when needed
export const loadPDFExport = async () => {
  const jsPDF = await import('jspdf');
  return jsPDF.default;
};

// Rich text editor - only load when needed
export const loadRichTextEditor = async () => {
  const ReactQuill = await import('react-quill');
  return ReactQuill.default;
};

// Code editor - only load when needed
export const loadCodeEditor = async () => {
  const { Prism } = await import('react-syntax-highlighter');
  const { tomorrow } = await import('react-syntax-highlighter/dist/esm/styles/prism');
  return { Prism, tomorrow };
};

// Image processing - only load when needed
export const loadImageProcessing = async () => {
  const Cropper = await import('react-easy-crop');
  return Cropper.default;
};

// Utility function to preload critical dynamic imports
export const preloadCriticalImports = () => {
  // Preload date utils since they're commonly used
  loadDateUtils();
  
  // Preload form validation for admin forms
  loadFormValidation();
};

// Utility function to check if a dynamic import is already loaded
export const isDynamicImportLoaded = (importName: string): boolean => {
  // Simple cache check - in a real app you might use a more sophisticated cache
  return Boolean((window as any)[`__dynamic_import_${importName}`]);
};

// Cache dynamic imports to avoid re-loading
export const cachedDynamicImport = async <T>(
  importName: string,
  importFn: () => Promise<T>
): Promise<T> => {
  const cacheKey = `__dynamic_import_${importName}`;
  
  if ((window as any)[cacheKey]) {
    return (window as any)[cacheKey];
  }
  
  const result = await importFn();
  (window as any)[cacheKey] = result;
  
  return result;
};
