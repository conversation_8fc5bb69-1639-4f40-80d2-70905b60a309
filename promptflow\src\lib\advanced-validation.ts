/**
 * Advanced Input Validation & Security System
 * Comprehensive validation for all user inputs with security focus
 */

import DOMPurify from 'isomorphic-dompurify'

// Validation rule types
export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  customValidator?: (value: string) => boolean
  sanitizer?: (value: string) => string
  allowedChars?: RegExp
  blockedPatterns?: RegExp[]
  maxFileSize?: number // for file uploads
  allowedFileTypes?: string[]
}

export interface ValidationResult {
  isValid: boolean
  sanitizedValue?: string
  errors: string[]
  warnings: string[]
}

// Common validation patterns
export const VALIDATION_PATTERNS = {
  // Basic patterns
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  
  // Project/content patterns
  projectName: /^[a-zA-Z0-9\s\-_.]+$/,
  promptTitle: /^[a-zA-Z0-9\s\-_.!?]+$/,
  hashtag: /^[a-zA-Z0-9_]+$/,
  folderPath: /^[a-zA-Z0-9\s\-_./]+$/,
  
  // Security patterns
  noScript: /^(?!.*<script).*$/i,
  noSqlInjection: /^(?!.*(union|select|insert|update|delete|drop|create|alter|exec|execute)).*$/i,
  noXss: /^(?!.*(javascript:|data:|vbscript:|onload|onerror|onclick)).*$/i,
  
  // Safe characters only
  alphanumeric: /^[a-zA-Z0-9]+$/,
  alphanumericWithSpaces: /^[a-zA-Z0-9\s]+$/,
  safeText: /^[a-zA-Z0-9\s\-_.!?,:;()]+$/,
} as const

// Blocked patterns for security
export const SECURITY_PATTERNS = {
  sqlInjection: [
    /(\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b)/i,
    /(--|\/\*|\*\/|;|'|"|`)/,
    /(\bor\b|\band\b).*(\b=\b|\blike\b)/i
  ],
  xssAttempts: [
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /data:text\/html/gi,
    /vbscript:/gi,
    /on\w+\s*=/gi,
    /<iframe[^>]*>.*?<\/iframe>/gi,
    /<object[^>]*>.*?<\/object>/gi,
    /<embed[^>]*>/gi
  ],
  pathTraversal: [
    /\.\.\//g,
    /\.\.\\/g,
    /%2e%2e%2f/gi,
    /%2e%2e%5c/gi
  ],
  commandInjection: [
    /[;&|`$(){}[\]]/,
    /\b(cat|ls|pwd|whoami|id|uname|ps|netstat|ifconfig|ping|curl|wget)\b/i
  ]
} as const

/**
 * Advanced input validator with security focus
 */
export class AdvancedValidator {
  /**
   * Validate input against rules and security patterns
   */
  static validate(input: string, rules: ValidationRule = {}): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    let sanitizedValue = input

    // Basic checks
    if (rules.required && (!input || input.trim().length === 0)) {
      errors.push('Bu alan zorunludur')
      return { isValid: false, errors, warnings }
    }

    if (!input) {
      return { isValid: true, sanitizedValue: '', errors, warnings }
    }

    // Sanitize input first
    if (rules.sanitizer) {
      sanitizedValue = rules.sanitizer(input)
    } else {
      sanitizedValue = this.defaultSanitizer(input)
    }

    // Length validation
    if (rules.minLength && sanitizedValue.length < rules.minLength) {
      errors.push(`En az ${rules.minLength} karakter olmalıdır`)
    }

    if (rules.maxLength && sanitizedValue.length > rules.maxLength) {
      errors.push(`En fazla ${rules.maxLength} karakter olabilir`)
    }

    // Pattern validation
    if (rules.pattern && !rules.pattern.test(sanitizedValue)) {
      errors.push('Geçersiz format')
    }

    // Allowed characters
    if (rules.allowedChars && !rules.allowedChars.test(sanitizedValue)) {
      errors.push('Geçersiz karakterler içeriyor')
    }

    // Security checks
    const securityResult = this.checkSecurity(sanitizedValue)
    errors.push(...securityResult.errors)
    warnings.push(...securityResult.warnings)

    // Blocked patterns
    if (rules.blockedPatterns) {
      for (const pattern of rules.blockedPatterns) {
        if (pattern.test(sanitizedValue)) {
          errors.push('Güvenlik nedeniyle engellenmiş içerik')
          break
        }
      }
    }

    // Custom validator
    if (rules.customValidator && !rules.customValidator(sanitizedValue)) {
      errors.push('Özel validation kuralını karşılamıyor')
    }

    return {
      isValid: errors.length === 0,
      sanitizedValue,
      errors,
      warnings
    }
  }

  /**
   * Default sanitizer for text input
   */
  private static defaultSanitizer(input: string): string {
    // Trim whitespace
    let sanitized = input.trim()
    
    // Normalize multiple spaces
    sanitized = sanitized.replace(/\s+/g, ' ')
    
    // Remove null bytes
    sanitized = sanitized.replace(/\0/g, '')
    
    // Remove control characters except newlines and tabs
    sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
    
    return sanitized
  }

  /**
   * Security-focused validation
   */
  private static checkSecurity(input: string): { errors: string[]; warnings: string[] } {
    const errors: string[] = []
    const warnings: string[] = []

    // SQL Injection check
    for (const pattern of SECURITY_PATTERNS.sqlInjection) {
      if (pattern.test(input)) {
        errors.push('Güvenlik nedeniyle engellenmiş içerik (SQL)')
        break
      }
    }

    // XSS check
    for (const pattern of SECURITY_PATTERNS.xssAttempts) {
      if (pattern.test(input)) {
        errors.push('Güvenlik nedeniyle engellenmiş içerik (XSS)')
        break
      }
    }

    // Path traversal check
    for (const pattern of SECURITY_PATTERNS.pathTraversal) {
      if (pattern.test(input)) {
        errors.push('Güvenlik nedeniyle engellenmiş içerik (Path)')
        break
      }
    }

    // Command injection check
    for (const pattern of SECURITY_PATTERNS.commandInjection) {
      if (pattern.test(input)) {
        errors.push('Güvenlik nedeniyle engellenmiş içerik (Command)')
        break
      }
    }

    // Suspicious patterns (warnings)
    if (input.includes('eval(') || input.includes('Function(')) {
      warnings.push('Potansiyel güvenlik riski tespit edildi')
    }

    if (input.length > 10000) {
      warnings.push('Çok uzun içerik - performans sorunu yaratabilir')
    }

    return { errors, warnings }
  }

  /**
   * HTML content sanitization with DOMPurify
   */
  static sanitizeHtml(html: string, options: {
    allowedTags?: string[]
    allowedAttributes?: string[]
    stripTags?: boolean
  } = {}): string {
    const { allowedTags, allowedAttributes, stripTags = false } = options

    if (stripTags) {
      return DOMPurify.sanitize(html, { ALLOWED_TAGS: [] })
    }

    const config: Record<string, unknown> = {
      ALLOWED_TAGS: allowedTags || ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li'],
      ALLOWED_ATTR: allowedAttributes || ['class'],
      FORBID_SCRIPT: true,
      FORBID_TAGS: ['script', 'object', 'embed', 'iframe', 'form', 'input'],
      FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover', 'style']
    }

    return DOMPurify.sanitize(html, config) as unknown as string
  }

  /**
   * File upload validation
   */
  static validateFile(file: File, rules: ValidationRule = {}): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // File size check
    if (rules.maxFileSize && file.size > rules.maxFileSize) {
      errors.push(`Dosya boyutu ${Math.round(rules.maxFileSize / 1024 / 1024)}MB'dan büyük olamaz`)
    }

    // File type check
    if (rules.allowedFileTypes && !rules.allowedFileTypes.includes(file.type)) {
      errors.push('Desteklenmeyen dosya türü')
    }

    // File name security check
    const nameValidation = this.validate(file.name, {
      pattern: /^[a-zA-Z0-9\s\-_.]+$/,
      maxLength: 255,
      blockedPatterns: [...SECURITY_PATTERNS.pathTraversal]
    })

    if (!nameValidation.isValid) {
      errors.push('Güvenli olmayan dosya adı')
    }

    // Suspicious file extensions
    const suspiciousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com', '.js', '.vbs', '.jar']
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
    
    if (suspiciousExtensions.includes(fileExtension)) {
      errors.push('Güvenlik nedeniyle engellenmiş dosya türü')
    }

    return {
      isValid: errors.length === 0,
      sanitizedValue: file.name,
      errors,
      warnings
    }
  }

  /**
   * Batch validation for multiple inputs
   */
  static validateBatch(inputs: Array<{ value: string; rules: ValidationRule; field: string }>): {
    isValid: boolean
    results: Record<string, ValidationResult>
    globalErrors: string[]
  } {
    const results: Record<string, ValidationResult> = {}
    const globalErrors: string[] = []
    let isValid = true

    for (const { value, rules, field } of inputs) {
      const result = this.validate(value, rules)
      results[field] = result
      
      if (!result.isValid) {
        isValid = false
      }
    }

    return { isValid, results, globalErrors }
  }
}

// Predefined validation rules for common use cases
export const COMMON_VALIDATION_RULES = {
  projectName: {
    required: true,
    minLength: 3,
    maxLength: 50,
    pattern: VALIDATION_PATTERNS.projectName,
    allowedChars: VALIDATION_PATTERNS.projectName
  },
  
  promptTitle: {
    required: true,
    minLength: 1,
    maxLength: 200,
    pattern: VALIDATION_PATTERNS.promptTitle
  },
  
  promptContent: {
    required: true,
    minLength: 1,
    maxLength: 50000,
    sanitizer: (value: string) => AdvancedValidator.sanitizeHtml(value, { stripTags: true })
  },
  
  email: {
    required: true,
    pattern: VALIDATION_PATTERNS.email,
    maxLength: 254
  },
  
  password: {
    required: true,
    minLength: 8,
    maxLength: 128,
    customValidator: (value: string) => {
      // At least one uppercase, one lowercase, one number
      return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/.test(value)
    }
  },
  
  hashtag: {
    required: false,
    minLength: 1,
    maxLength: 30,
    pattern: VALIDATION_PATTERNS.hashtag
  },
  
  url: {
    required: false,
    pattern: VALIDATION_PATTERNS.url,
    maxLength: 2048
  },
  
  safeText: {
    required: false,
    maxLength: 1000,
    pattern: VALIDATION_PATTERNS.safeText
  }
} as const

/**
 * React hook for form validation
 */
export function useAdvancedValidation() {
  return {
    validate: AdvancedValidator.validate,
    validateFile: AdvancedValidator.validateFile,
    validateBatch: AdvancedValidator.validateBatch,
    sanitizeHtml: AdvancedValidator.sanitizeHtml,
    rules: COMMON_VALIDATION_RULES
  }
}
