import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { authApi } from '@/lib/api';
import { queryKeys } from '@/lib/query-client';
import { supabase } from '@/lib/supabase';
import { useRouter } from 'next/navigation';
import { useToast } from '@chakra-ui/react';

// Admin status check hook with caching
export function useAdminStatus(userId?: string) {
  return useQuery({
    queryKey: queryKeys.admin.status(userId || ''),
    queryFn: async () => {
      if (!userId) throw new Error('User ID required');

      try {
        return await authApi.checkAdminStatus(userId);
      } catch (error) {
        console.error('Admin status check failed:', error);
        // Handle specific auth errors
        if (error instanceof Error && (
          error.message.includes('401') ||
          error.message.includes('403') ||
          error.message.includes('Unauthorized')
        )) {
          // Clear session on auth errors
          await supabase.auth.signOut();
          throw new Error('Authentication required');
        }
        throw error;
      }
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes cache
    gcTime: 10 * 60 * 1000,
    refetchOnWindowFocus: false,
    refetchOnMount: false, // Don't refetch on mount if cached
    retry: (failureCount, error) => {
      // Don't retry auth failures
      if (error instanceof Error && (
        error.message.includes('Authentication required') ||
        error.message.includes('401') ||
        error.message.includes('403')
      )) {
        return false;
      }
      return failureCount < 2;
    },
  });
}

// Admin user details hook
export function useAdminUser(userId?: string) {
  return useQuery({
    queryKey: [...queryKeys.admin.profile, userId],
    queryFn: () => authApi.getAdminUser(userId!),
    enabled: !!userId,
    staleTime: 10 * 60 * 1000, // 10 minutes cache for user details
    gcTime: 20 * 60 * 1000,
    refetchOnWindowFocus: false,
  });
}

// Current user hook with admin check
export function useCurrentUser() {
  const router = useRouter();
  const toast = useToast();
  const queryClient = useQueryClient();

  const userQuery = useQuery({
    queryKey: ['auth', 'user'],
    queryFn: async () => {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error || !user) throw new Error('Not authenticated');
      return user;
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    refetchOnWindowFocus: false,
    retry: (failureCount, error) => {
      // Don't retry auth failures
      if (error instanceof Error && error.message.includes('Not authenticated')) {
        return false;
      }
      return failureCount < 1;
    },
  });

  const adminStatusQuery = useAdminStatus(userQuery.data?.id);
  const adminUserQuery = useAdminUser(userQuery.data?.id);

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: async () => {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    },
    onSuccess: () => {
      // Clear all caches
      queryClient.clear();
      authApi.clearAdminCache();
      router.push('/');
    },
    onError: (error) => {
      console.error('Logout error:', error);
      toast({
        title: 'Çıkış Hatası',
        description: 'Çıkış yapılırken bir hata oluştu.',
        status: 'error',
        duration: 3000,
      });
    },
  });

  return {
    user: userQuery.data,
    isLoading: userQuery.isLoading || adminStatusQuery.isLoading,
    isAdmin: adminStatusQuery.data || false,
    adminUser: adminUserQuery.data,
    error: userQuery.error || adminStatusQuery.error,
    logout: logoutMutation.mutate,
    isLoggingOut: logoutMutation.isPending,
  };
}

// Auth guard hook for protected routes
export function useAuthGuard() {
  const { user, isAdmin, isLoading } = useCurrentUser();
  const router = useRouter();
  const toast = useToast();

  // Use useEffect to handle redirects and side effects
  useEffect(() => {
    if (!isLoading && (!user || !isAdmin)) {
      if (!user) {
        router.push('/');
      } else if (!isAdmin) {
        toast({
          title: 'Erişim Reddedildi',
          description: 'Bu alana erişim için admin yetkileriniz bulunmuyor.',
          status: 'error',
          duration: 5000,
        });
        router.push('/');
      }
    }
  }, [isLoading, user, isAdmin, router, toast]);

  return {
    isAuthenticated: !!user,
    isAdmin,
    isLoading,
    canAccess: !!user && isAdmin,
  };
}
