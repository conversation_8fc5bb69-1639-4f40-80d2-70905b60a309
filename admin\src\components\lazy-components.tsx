'use client';

import { lazy, Suspense } from 'react';
import { Box, Skeleton, VStack, HStack } from '@chakra-ui/react';

// Lazy load heavy components
export const LazyDataTable = lazy(() => import('./data-table'));
export const LazyChart = lazy(() => import('./chart'));
export const LazyModal = lazy(() => import('./modal'));

// Loading skeletons for different component types
export function TableSkeleton() {
  return (
    <VStack spacing={4} align="stretch">
      {/* Header skeleton */}
      <HStack spacing={4}>
        <Skeleton height="40px" width="200px" />
        <Skeleton height="40px" width="150px" />
        <Skeleton height="40px" width="100px" />
      </HStack>
      
      {/* Table rows skeleton */}
      {Array.from({ length: 5 }).map((_, index) => (
        <HStack key={index} spacing={4}>
          <Skeleton height="60px" width="100%" />
        </HStack>
      ))}
    </VStack>
  );
}

export function ChartSkeleton() {
  return (
    <Box>
      <Skeleton height="300px" width="100%" borderRadius="md" />
    </Box>
  );
}

export function ModalSkeleton() {
  return (
    <VStack spacing={4} p={6}>
      <Skeleton height="30px" width="200px" />
      <Skeleton height="100px" width="100%" />
      <HStack spacing={4}>
        <Skeleton height="40px" width="100px" />
        <Skeleton height="40px" width="100px" />
      </HStack>
    </VStack>
  );
}

// Wrapper components with appropriate loading states
export function LazyDataTableWrapper({ children, ...props }: any) {
  return (
    <Suspense fallback={<TableSkeleton />}>
      <LazyDataTable {...props}>
        {children}
      </LazyDataTable>
    </Suspense>
  );
}

export function LazyChartWrapper({ children, ...props }: any) {
  return (
    <Suspense fallback={<ChartSkeleton />}>
      <LazyChart {...props}>
        {children}
      </LazyChart>
    </Suspense>
  );
}

export function LazyModalWrapper({ children, ...props }: any) {
  return (
    <Suspense fallback={<ModalSkeleton />}>
      <LazyModal {...props}>
        {children}
      </LazyModal>
    </Suspense>
  );
}
