'use client';

import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Badge,
  HStack,
  VStack,
  Icon,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Card,
  CardHeader,
  CardBody,
  useToast,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Flex,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton,
  Textarea,
  Switch,
  Tag,
  TagLabel,
  TagCloseButton,
  Wrap,
  WrapItem,
} from '@chakra-ui/react';
import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { AdminLayout } from '@/components/admin-layout';
import { 
  Search, 
  Filter, 
  Plus, 
  MoreVertical, 
  Edit, 
  Trash2, 
  FileText, 
  BarChart3, 
  Calendar,
  ArrowLeft,
  Eye,
  Users,
  Activity,
  Archive,
  Globe,
  Lock
} from 'lucide-react';

interface Project {
  id: string;
  name: string;
  description: string | null;
  context_text: string;
  status: 'active' | 'inactive' | 'archived';
  tags: string[];
  is_public: boolean;
  created_at: string;
  updated_at: string;
  user_id: string;
  user_email?: string;
  prompts_count?: number;
}

interface ProjectStats {
  totalProjects: number;
  activeProjects: number;
  archivedProjects: number;
  publicProjects: number;
}

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [stats, setStats] = useState<ProjectStats>({
    totalProjects: 0,
    activeProjects: 0,
    archivedProjects: 0,
    publicProjects: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  
  const router = useRouter();
  const toast = useToast();
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure();
  const { isOpen: isViewOpen, onOpen: onViewOpen, onClose: onViewClose } = useDisclosure();
  const cancelRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    fetchProjects();
    fetchStats();
  }, []);

  useEffect(() => {
    filterProjects();
  }, [projects, searchTerm, filterStatus]);

  const fetchProjects = async () => {
    try {
      // Projeleri kullanıcı bilgileri ile birlikte al
      const { data: projectsData, error } = await supabase
        .from('projects')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      // Her proje için kullanıcı email'i ve prompt sayısını al
      const projectsWithDetails = await Promise.all(
        projectsData.map(async (project) => {
          // Kullanıcı email'ini al (user_id null değilse)
          let userData = null;
          if (project.user_id) {
            try {
              const result = await supabase.auth.admin.getUserById(project.user_id);
              userData = result.data;
            } catch (error) {
              console.warn(`User data alınamadı (user_id: ${project.user_id}):`, error);
            }
          }
          
          // Prompt sayısını al
          const { count: promptsCount } = await supabase
            .from('prompts')
            .select('*', { count: 'exact', head: true })
            .eq('project_id', project.id);

          return {
            ...project,
            user_email: userData?.user?.email || 'Bilinmeyen',
            prompts_count: promptsCount || 0,
          };
        })
      );

      setProjects(projectsWithDetails);
    } catch (error) {
      console.error('Projects fetch error:', error);
      toast({
        title: 'Hata',
        description: 'Projeler yüklenirken bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const { count: totalProjects } = await supabase
        .from('projects')
        .select('*', { count: 'exact', head: true });

      const { count: activeProjects } = await supabase
        .from('projects')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'active');

      const { count: archivedProjects } = await supabase
        .from('projects')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'archived');

      const { count: publicProjects } = await supabase
        .from('projects')
        .select('*', { count: 'exact', head: true })
        .eq('is_public', true);

      setStats({
        totalProjects: totalProjects || 0,
        activeProjects: activeProjects || 0,
        archivedProjects: archivedProjects || 0,
        publicProjects: publicProjects || 0,
      });
    } catch (error) {
      console.error('Stats fetch error:', error);
    }
  };

  const filterProjects = () => {
    let filtered = projects;

    // Arama filtresi
    if (searchTerm) {
      filtered = filtered.filter(project =>
        project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.user_email?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Durum filtresi
    if (filterStatus !== 'all') {
      filtered = filtered.filter(project => project.status === filterStatus);
    }

    setFilteredProjects(filtered);
  };

  const handleDeleteProject = async () => {
    if (!selectedProject) return;

    try {
      // Önce projeye ait prompt'ları sil
      await supabase
        .from('prompts')
        .delete()
        .eq('project_id', selectedProject.id);

      // Sonra projeyi sil
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', selectedProject.id);

      if (error) {
        throw error;
      }

      toast({
        title: 'Başarılı',
        description: 'Proje başarıyla silindi.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      fetchProjects();
      fetchStats();
      onDeleteClose();
    } catch (error) {
      console.error('Delete project error:', error);
      toast({
        title: 'Hata',
        description: 'Proje silinirken bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleUpdateProject = async () => {
    if (!editingProject) return;

    try {
      const { error } = await supabase
        .from('projects')
        .update({
          name: editingProject.name,
          description: editingProject.description,
          context_text: editingProject.context_text,
          status: editingProject.status,
          is_public: editingProject.is_public,
          tags: editingProject.tags,
        })
        .eq('id', editingProject.id);

      if (error) {
        throw error;
      }

      toast({
        title: 'Başarılı',
        description: 'Proje başarıyla güncellendi.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      fetchProjects();
      fetchStats();
      onEditClose();
    } catch (error) {
      console.error('Update project error:', error);
      toast({
        title: 'Hata',
        description: 'Proje güncellenirken bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'yellow';
      case 'archived': return 'gray';
      default: return 'gray';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Aktif';
      case 'inactive': return 'Pasif';
      case 'archived': return 'Arşivlenmiş';
      default: return status;
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <Box minH="100vh" bg="gray.50" display="flex" alignItems="center" justifyContent="center">
          <Text>Yükleniyor...</Text>
        </Box>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Container maxW="7xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <Flex align="center" justify="space-between">
            <VStack align="start" spacing={0}>
              <Heading size="lg">Proje Yönetimi</Heading>
              <Text color="gray.600">Sistem projelerini yönetin</Text>
            </VStack>
          </Flex>

          {/* Stats */}
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={FileText} w={4} h={4} />
                      <Text>Toplam Proje</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber color="blue.600">{stats.totalProjects}</StatNumber>
                </Stat>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={Activity} w={4} h={4} />
                      <Text>Aktif Proje</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber color="green.600">{stats.activeProjects}</StatNumber>
                </Stat>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={Archive} w={4} h={4} />
                      <Text>Arşivlenmiş</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber color="gray.600">{stats.archivedProjects}</StatNumber>
                </Stat>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={Globe} w={4} h={4} />
                      <Text>Genel Proje</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber color="purple.600">{stats.publicProjects}</StatNumber>
                </Stat>
              </CardBody>
            </Card>
          </SimpleGrid>

          {/* Filters */}
          <Card>
            <CardHeader>
              <Heading size="md">Proje Listesi</Heading>
            </CardHeader>
            <CardBody>
              <HStack spacing={4} mb={6}>
                <InputGroup maxW="300px">
                  <InputLeftElement>
                    <Icon as={Search} color="gray.400" />
                  </InputLeftElement>
                  <Input
                    placeholder="Projelerde ara..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </InputGroup>
                <Select
                  maxW="200px"
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                >
                  <option value="all">Tüm Durumlar</option>
                  <option value="active">Aktif</option>
                  <option value="inactive">Pasif</option>
                  <option value="archived">Arşivlenmiş</option>
                </Select>
              </HStack>

              <TableContainer>
                <Table variant="simple">
                  <Thead>
                    <Tr>
                      <Th>Proje</Th>
                      <Th>Sahibi</Th>
                      <Th>Durum</Th>
                      <Th>Prompt Sayısı</Th>
                      <Th>Görünürlük</Th>
                      <Th>Oluşturulma</Th>
                      <Th>İşlemler</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {filteredProjects.map((project) => (
                      <Tr key={project.id}>
                        <Td>
                          <VStack align="start" spacing={1}>
                            <Text fontWeight="medium">{project.name}</Text>
                            {project.description && (
                              <Text fontSize="sm" color="gray.600" noOfLines={1}>
                                {project.description}
                              </Text>
                            )}
                            {project.tags.length > 0 && (
                              <Wrap>
                                {project.tags.slice(0, 2).map((tag, index) => (
                                  <WrapItem key={index}>
                                    <Tag size="sm" colorScheme="blue">
                                      <TagLabel>{tag}</TagLabel>
                                    </Tag>
                                  </WrapItem>
                                ))}
                                {project.tags.length > 2 && (
                                  <WrapItem>
                                    <Tag size="sm" colorScheme="gray">
                                      <TagLabel>+{project.tags.length - 2}</TagLabel>
                                    </Tag>
                                  </WrapItem>
                                )}
                              </Wrap>
                            )}
                          </VStack>
                        </Td>
                        <Td>
                          <Text fontSize="sm">{project.user_email}</Text>
                        </Td>
                        <Td>
                          <Badge colorScheme={getStatusColor(project.status)}>
                            {getStatusText(project.status)}
                          </Badge>
                        </Td>
                        <Td>
                          <Text fontWeight="medium">{project.prompts_count}</Text>
                        </Td>
                        <Td>
                          <HStack>
                            <Icon 
                              as={project.is_public ? Globe : Lock} 
                              w={4} 
                              h={4} 
                              color={project.is_public ? 'green.500' : 'gray.500'}
                            />
                            <Text fontSize="sm">
                              {project.is_public ? 'Genel' : 'Özel'}
                            </Text>
                          </HStack>
                        </Td>
                        <Td>
                          <Text fontSize="sm" color="gray.600">
                            {formatDate(project.created_at)}
                          </Text>
                        </Td>
                        <Td>
                          <Menu>
                            <MenuButton
                              as={IconButton}
                              icon={<Icon as={MoreVertical} />}
                              variant="ghost"
                              size="sm"
                            />
                            <MenuList>
                              <MenuItem
                                icon={<Icon as={Eye} />}
                                onClick={() => {
                                  setSelectedProject(project);
                                  onViewOpen();
                                }}
                              >
                                Görüntüle
                              </MenuItem>
                              <MenuItem
                                icon={<Icon as={Edit} />}
                                onClick={() => {
                                  setEditingProject(project);
                                  onEditOpen();
                                }}
                              >
                                Düzenle
                              </MenuItem>
                              <MenuItem
                                icon={<Icon as={Trash2} />}
                                color="red.500"
                                onClick={() => {
                                  setSelectedProject(project);
                                  onDeleteOpen();
                                }}
                              >
                                Sil
                              </MenuItem>
                            </MenuList>
                          </Menu>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </TableContainer>
            </CardBody>
          </Card>
        </VStack>
      </Container>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        isOpen={isDeleteOpen}
        leastDestructiveRef={cancelRef}
        onClose={onDeleteClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              Projeyi Sil
            </AlertDialogHeader>
            <AlertDialogBody>
              <Text>
                <strong>{selectedProject?.name}</strong> projesini silmek istediğinizden emin misiniz?
              </Text>
              <Text color="red.500" fontSize="sm" mt={2}>
                Bu işlem geri alınamaz ve projeye ait tüm promptlar da silinecektir.
              </Text>
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onDeleteClose}>
                İptal
              </Button>
              <Button colorScheme="red" onClick={handleDeleteProject} ml={3}>
                Sil
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>

      {/* Edit Project Modal */}
      <Modal isOpen={isEditOpen} onClose={onEditClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Proje Düzenle</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {editingProject && (
              <VStack spacing={4}>
                <FormControl>
                  <FormLabel>Proje Adı</FormLabel>
                  <Input
                    value={editingProject.name}
                    onChange={(e) => setEditingProject({
                      ...editingProject,
                      name: e.target.value
                    })}
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>Açıklama</FormLabel>
                  <Textarea
                    value={editingProject.description || ''}
                    onChange={(e) => setEditingProject({
                      ...editingProject,
                      description: e.target.value
                    })}
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>Context Metni</FormLabel>
                  <Textarea
                    value={editingProject.context_text}
                    onChange={(e) => setEditingProject({
                      ...editingProject,
                      context_text: e.target.value
                    })}
                    rows={4}
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>Durum</FormLabel>
                  <Select
                    value={editingProject.status}
                    onChange={(e) => setEditingProject({
                      ...editingProject,
                      status: e.target.value as 'active' | 'inactive' | 'archived'
                    })}
                  >
                    <option value="active">Aktif</option>
                    <option value="inactive">Pasif</option>
                    <option value="archived">Arşivlenmiş</option>
                  </Select>
                </FormControl>
                <FormControl>
                  <HStack justify="space-between">
                    <FormLabel mb={0}>Genel Proje</FormLabel>
                    <Switch
                      isChecked={editingProject.is_public}
                      onChange={(e) => setEditingProject({
                        ...editingProject,
                        is_public: e.target.checked
                      })}
                    />
                  </HStack>
                </FormControl>
              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onEditClose}>
              İptal
            </Button>
            <Button colorScheme="blue" onClick={handleUpdateProject}>
              Güncelle
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* View Project Modal */}
      <Modal isOpen={isViewOpen} onClose={onViewClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Proje Detayları</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedProject && (
              <VStack spacing={4} align="stretch">
                <Box>
                  <Text fontWeight="bold" mb={2}>Proje Adı:</Text>
                  <Text>{selectedProject.name}</Text>
                </Box>
                {selectedProject.description && (
                  <Box>
                    <Text fontWeight="bold" mb={2}>Açıklama:</Text>
                    <Text>{selectedProject.description}</Text>
                  </Box>
                )}
                <Box>
                  <Text fontWeight="bold" mb={2}>Context Metni:</Text>
                  <Text 
                    bg="gray.50" 
                    p={3} 
                    rounded="md" 
                    whiteSpace="pre-wrap"
                    maxH="200px"
                    overflowY="auto"
                  >
                    {selectedProject.context_text || 'Context metni bulunmuyor'}
                  </Text>
                </Box>
                <SimpleGrid columns={2} spacing={4}>
                  <Box>
                    <Text fontWeight="bold" mb={2}>Durum:</Text>
                    <Badge colorScheme={getStatusColor(selectedProject.status)}>
                      {getStatusText(selectedProject.status)}
                    </Badge>
                  </Box>
                  <Box>
                    <Text fontWeight="bold" mb={2}>Görünürlük:</Text>
                    <Badge colorScheme={selectedProject.is_public ? 'green' : 'gray'}>
                      {selectedProject.is_public ? 'Genel' : 'Özel'}
                    </Badge>
                  </Box>
                  <Box>
                    <Text fontWeight="bold" mb={2}>Prompt Sayısı:</Text>
                    <Text>{selectedProject.prompts_count}</Text>
                  </Box>
                  <Box>
                    <Text fontWeight="bold" mb={2}>Sahibi:</Text>
                    <Text>{selectedProject.user_email}</Text>
                  </Box>
                </SimpleGrid>
                {selectedProject.tags.length > 0 && (
                  <Box>
                    <Text fontWeight="bold" mb={2}>Etiketler:</Text>
                    <Wrap>
                      {selectedProject.tags.map((tag, index) => (
                        <WrapItem key={index}>
                          <Tag colorScheme="blue">
                            <TagLabel>{tag}</TagLabel>
                          </Tag>
                        </WrapItem>
                      ))}
                    </Wrap>
                  </Box>
                )}
                <SimpleGrid columns={2} spacing={4}>
                  <Box>
                    <Text fontWeight="bold" mb={2}>Oluşturulma:</Text>
                    <Text fontSize="sm">{formatDate(selectedProject.created_at)}</Text>
                  </Box>
                  <Box>
                    <Text fontWeight="bold" mb={2}>Son Güncelleme:</Text>
                    <Text fontSize="sm">{formatDate(selectedProject.updated_at)}</Text>
                  </Box>
                </SimpleGrid>
              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <Button onClick={onViewClose}>Kapat</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </AdminLayout>
  );
}