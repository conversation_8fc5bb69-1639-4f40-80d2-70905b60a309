/**
 * ProjectNameEditor Test Suite
 * Comprehensive testing for project name editing functionality
 */

import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ProjectNameEditor } from '../project-name-editor'
import { useUpdateProjectNameSecure, useProjects } from '@/hooks/use-projects'

// Mock hooks
jest.mock('@/hooks/use-projects')
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn()
  }
}))

const mockUseUpdateProjectNameSecure = useUpdateProjectNameSecure as jest.MockedFunction<typeof useUpdateProjectNameSecure>
const mockUseProjects = useProjects as jest.MockedFunction<typeof useProjects>

// Test wrapper with QueryClient
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })
  
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

// Mock data
const mockProjects = [
  { id: 'project-1', name: 'Test Project 1' },
  { id: 'project-2', name: 'Test Project 2' },
  { id: 'project-3', name: 'Another Project' }
]

const mockMutation = {
  mutateAsync: jest.fn(),
  isPending: false,
  isError: false,
  error: null
}

describe('ProjectNameEditor', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    mockUseProjects.mockReturnValue({
      data: mockProjects,
      isLoading: false,
      error: null
    } as unknown)

    mockUseUpdateProjectNameSecure.mockReturnValue(mockMutation as unknown)
  })

  const defaultProps = {
    projectId: 'project-1',
    currentName: 'Test Project 1',
    onNameUpdated: jest.fn()
  }

  describe('Display Mode', () => {
    it('renders project name correctly', () => {
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )

      expect(screen.getByText('Test Project 1')).toBeInTheDocument()
    })

    it('shows edit button on hover', () => {
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      expect(editButton).toBeInTheDocument()
    })

    it('enters edit mode when edit button is clicked', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      expect(screen.getByRole('textbox', { name: /proje adını düzenle/i })).toBeInTheDocument()
    })

    it('enters edit mode when project name is clicked', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )

      const projectName = screen.getByText('Test Project 1')
      await user.click(projectName)

      expect(screen.getByRole('textbox', { name: /proje adını düzenle/i })).toBeInTheDocument()
    })
  })

  describe('Edit Mode', () => {
    it('shows input with current name as value', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      const input = screen.getByRole('textbox', { name: /proje adını düzenle/i })
      expect(input).toHaveValue('Test Project 1')
    })

    it('shows character counter', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      expect(screen.getByText('16/50')).toBeInTheDocument() // "Test Project 1" = 16 chars
    })

    it('shows save and cancel buttons', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      expect(screen.getByRole('button', { name: /proje adını kaydet/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /düzenlemeyi iptal et/i })).toBeInTheDocument()
    })
  })

  describe('Validation', () => {
    it('shows error for empty name', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      const input = screen.getByRole('textbox', { name: /proje adını düzenle/i })
      await user.clear(input)

      await waitFor(() => {
        expect(screen.getByText(/proje adı boş olamaz/i)).toBeInTheDocument()
      })
    })

    it('shows error for name too short', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      const input = screen.getByRole('textbox', { name: /proje adını düzenle/i })
      await user.clear(input)
      await user.type(input, 'AB')

      await waitFor(() => {
        expect(screen.getByText(/en az 3 karakter/i)).toBeInTheDocument()
      })
    })

    it('shows error for duplicate name', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      const input = screen.getByRole('textbox', { name: /proje adını düzenle/i })
      await user.clear(input)
      await user.type(input, 'Test Project 2') // Duplicate name

      await waitFor(() => {
        expect(screen.getByText(/bu isimde bir proje zaten mevcut/i)).toBeInTheDocument()
      })
    })

    it('shows success state for valid unique name', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      const input = screen.getByRole('textbox', { name: /proje adını düzenle/i })
      await user.clear(input)
      await user.type(input, 'New Unique Project Name')

      await waitFor(() => {
        expect(screen.getByText(/proje adı kullanılabilir/i)).toBeInTheDocument()
      })
    })
  })

  describe('Keyboard Navigation', () => {
    it('saves on Enter key', async () => {
      const user = userEvent.setup()
      mockMutation.mutateAsync.mockResolvedValue({ id: 'project-1', name: 'Updated Name' })
      
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      const input = screen.getByRole('textbox', { name: /proje adını düzenle/i })
      await user.clear(input)
      await user.type(input, 'Updated Name')
      await user.keyboard('{Enter}')

      expect(mockMutation.mutateAsync).toHaveBeenCalledWith({
        projectId: 'project-1',
        newName: 'Updated Name'
      })
    })

    it('cancels on Escape key', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      const input = screen.getByRole('textbox', { name: /proje adını düzenle/i })
      await user.type(input, ' Modified')
      await user.keyboard('{Escape}')

      expect(screen.getByText('Test Project 1')).toBeInTheDocument()
      expect(screen.queryByRole('textbox')).not.toBeInTheDocument()
    })
  })

  describe('Save Functionality', () => {
    it('calls mutation with correct parameters', async () => {
      const user = userEvent.setup()
      mockMutation.mutateAsync.mockResolvedValue({ id: 'project-1', name: 'New Name' })
      
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      const input = screen.getByRole('textbox', { name: /proje adını düzenle/i })
      await user.clear(input)
      await user.type(input, 'New Name')

      const saveButton = screen.getByRole('button', { name: /proje adını kaydet/i })
      await user.click(saveButton)

      expect(mockMutation.mutateAsync).toHaveBeenCalledWith({
        projectId: 'project-1',
        newName: 'New Name'
      })
    })

    it('calls onNameUpdated callback on success', async () => {
      const user = userEvent.setup()
      const onNameUpdated = jest.fn()
      mockMutation.mutateAsync.mockResolvedValue({ id: 'project-1', name: 'New Name' })
      
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} onNameUpdated={onNameUpdated} />
        </TestWrapper>
      )

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      const input = screen.getByRole('textbox', { name: /proje adını düzenle/i })
      await user.clear(input)
      await user.type(input, 'New Name')

      const saveButton = screen.getByRole('button', { name: /proje adını kaydet/i })
      await user.click(saveButton)

      await waitFor(() => {
        expect(onNameUpdated).toHaveBeenCalledWith('New Name')
      })
    })

    it('does not save if name is unchanged', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      const saveButton = screen.getByRole('button', { name: /proje adını kaydet/i })
      expect(saveButton).toBeDisabled()
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA labels', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      const input = screen.getByRole('textbox', { name: /proje adını düzenle/i })
      expect(input).toHaveAttribute('aria-label', 'Proje adını düzenle')
      expect(input).toHaveAttribute('aria-describedby', 'project-name-feedback-project-1')
    })

    it('has live region for validation feedback', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ProjectNameEditor {...defaultProps} />
        </TestWrapper>
      )

      const editButton = screen.getByRole('button', { name: /proje adını düzenle/i })
      await user.click(editButton)

      const input = screen.getByRole('textbox', { name: /proje adını düzenle/i })
      await user.clear(input)

      await waitFor(() => {
        const feedback = screen.getByRole('status')
        expect(feedback).toHaveAttribute('aria-live', 'polite')
      })
    })
  })
})
