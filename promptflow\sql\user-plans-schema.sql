-- PromptFlow Kullanıcı Planları Sistemi
-- Veritabanı Şeması ve RLS Politikaları

-- 1. Plan Türleri Tablosu
CREATE TABLE IF NOT EXISTS plan_types (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  name text NOT NULL UNIQUE, -- 'free', 'professional', 'enterprise'
  display_name text NOT NULL, -- 'Ücretsiz', 'Profesyonel', 'Kurumsal'
  description text,
  price_monthly numeric(10,2) DEFAULT 0, -- <PERSON><PERSON><PERSON><PERSON> fiyat (TL)
  price_yearly numeric(10,2) DEFAULT 0, -- <PERSON><PERSON><PERSON><PERSON><PERSON> fiyat (TL)
  max_projects integer NOT NULL DEFAULT 5,
  max_prompts_per_project integer NOT NULL DEFAULT 100,
  features jsonb DEFAULT '{}', -- Plan özellikleri JSON formatında
  is_active boolean DEFAULT true,
  sort_order integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- 2. <PERSON>llanı<PERSON>ı Planları Tablosu
CREATE TABLE IF NOT EXISTS user_plans (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  plan_type_id uuid NOT NULL REFERENCES plan_types(id),
  status text NOT NULL DEFAULT 'active', -- 'active', 'cancelled', 'expired', 'suspended'
  billing_cycle text DEFAULT 'monthly', -- 'monthly', 'yearly', 'lifetime'
  started_at timestamp with time zone DEFAULT now(),
  expires_at timestamp with time zone, -- NULL for lifetime plans
  cancelled_at timestamp with time zone,
  auto_renew boolean DEFAULT true,
  payment_method text, -- 'credit_card', 'bank_transfer', 'manual'
  subscription_id text, -- External payment provider subscription ID
  metadata jsonb DEFAULT '{}', -- Additional plan metadata
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  -- Constraints
  CONSTRAINT valid_status CHECK (status IN ('active', 'cancelled', 'expired', 'suspended')),
  CONSTRAINT valid_billing_cycle CHECK (billing_cycle IN ('monthly', 'yearly', 'lifetime')),
  CONSTRAINT valid_dates CHECK (expires_at IS NULL OR expires_at > started_at)
);

-- 3. Kullanım İstatistikleri Tablosu
CREATE TABLE IF NOT EXISTS usage_stats (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  stat_date date NOT NULL DEFAULT CURRENT_DATE,
  projects_count integer DEFAULT 0,
  prompts_count integer DEFAULT 0,
  api_calls_count integer DEFAULT 0,
  storage_used_mb numeric(10,2) DEFAULT 0,
  last_activity_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  -- Unique constraint for daily stats per user
  UNIQUE(user_id, stat_date)
);

-- 4. Plan Geçiş Geçmişi Tablosu
CREATE TABLE IF NOT EXISTS plan_transactions (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  from_plan_id uuid REFERENCES plan_types(id),
  to_plan_id uuid NOT NULL REFERENCES plan_types(id),
  transaction_type text NOT NULL, -- 'upgrade', 'downgrade', 'renewal', 'cancellation'
  amount numeric(10,2) DEFAULT 0,
  currency text DEFAULT 'TRY',
  payment_status text DEFAULT 'pending', -- 'pending', 'completed', 'failed', 'refunded'
  payment_provider text, -- 'stripe', 'iyzico', 'manual'
  payment_reference text, -- External payment reference
  notes text,
  processed_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  
  -- Constraints
  CONSTRAINT valid_transaction_type CHECK (transaction_type IN ('upgrade', 'downgrade', 'renewal', 'cancellation', 'initial')),
  CONSTRAINT valid_payment_status CHECK (payment_status IN ('pending', 'completed', 'failed', 'refunded'))
);

-- 5. İndeksler
CREATE INDEX IF NOT EXISTS idx_user_plans_user_id ON user_plans(user_id);
CREATE INDEX IF NOT EXISTS idx_user_plans_status ON user_plans(status);
CREATE INDEX IF NOT EXISTS idx_user_plans_expires_at ON user_plans(expires_at);
CREATE INDEX IF NOT EXISTS idx_usage_stats_user_date ON usage_stats(user_id, stat_date);
CREATE INDEX IF NOT EXISTS idx_usage_stats_date ON usage_stats(stat_date);
CREATE INDEX IF NOT EXISTS idx_plan_transactions_user_id ON plan_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_plan_transactions_created_at ON plan_transactions(created_at);

-- 6. RLS Politikaları

-- Plan Types - Herkes okuyabilir
ALTER TABLE plan_types ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Anyone can view active plan types" ON plan_types
  FOR SELECT USING (is_active = true);

-- User Plans - Kullanıcılar sadece kendi planlarını görebilir
ALTER TABLE user_plans ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own plans" ON user_plans
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own plans" ON user_plans
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own plans" ON user_plans
  FOR UPDATE USING (auth.uid() = user_id);

-- Usage Stats - Kullanıcılar sadece kendi istatistiklerini görebilir
ALTER TABLE usage_stats ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own usage stats" ON usage_stats
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own usage stats" ON usage_stats
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own usage stats" ON usage_stats
  FOR UPDATE USING (auth.uid() = user_id);

-- Plan Transactions - Kullanıcılar sadece kendi işlemlerini görebilir
ALTER TABLE plan_transactions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own plan transactions" ON plan_transactions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own plan transactions" ON plan_transactions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 7. Trigger Functions

-- Updated_at otomatik güncelleme fonksiyonu
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Updated_at trigger'ları
CREATE TRIGGER update_plan_types_updated_at 
  BEFORE UPDATE ON plan_types 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_plans_updated_at 
  BEFORE UPDATE ON user_plans 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_usage_stats_updated_at 
  BEFORE UPDATE ON usage_stats 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 8. Varsayılan Plan Türleri
INSERT INTO plan_types (name, display_name, description, price_monthly, price_yearly, max_projects, max_prompts_per_project, features, sort_order) VALUES
('free', 'Ücretsiz', 'Başlangıç seviyesi kullanıcılar için ideal', 0, 0, 5, 100, '{"context_gallery": "limited", "api_access": false, "support": "email", "team_features": false}', 1),
('professional', 'Profesyonel', 'Profesyonel kullanıcılar ve küçük takımlar için', 99, 990, 50, 5000, '{"context_gallery": "full", "api_access": true, "support": "priority", "team_features": true, "advanced_analytics": true}', 2),
('enterprise', 'Kurumsal', 'Büyük organizasyonlar için özel çözümler', 0, 0, -1, -1, '{"context_gallery": "full", "api_access": true, "support": "24_7", "team_features": true, "advanced_analytics": true, "sso": true, "custom_deployment": true}', 3)
ON CONFLICT (name) DO NOTHING;
