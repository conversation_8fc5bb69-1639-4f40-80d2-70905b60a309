import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface AppState {
  activeProjectId: string | null
  setActiveProjectId: (projectId: string | null) => void
  isContextEnabled: boolean
  setIsContextEnabled: (enabled: boolean) => void
  // Sidebar collapse states
  isProjectSidebarCollapsed: boolean
  setIsProjectSidebarCollapsed: (collapsed: boolean) => void
  isContextSidebarCollapsed: boolean
  setIsContextSidebarCollapsed: (collapsed: boolean) => void
}

export const useAppStore = create<AppState>()(
  persist(
    (set) => ({
      activeProjectId: null,
      setActiveProjectId: (projectId) => set({ activeProjectId: projectId }),
      isContextEnabled: true,
      setIsContextEnabled: (enabled) => set({ isContextEnabled: enabled }),
      // Sidebar collapse states
      isProjectSidebarCollapsed: false,
      setIsProjectSidebarCollapsed: (collapsed) => set({ isProjectSidebarCollapsed: collapsed }),
      isContextSidebarCollapsed: false,
      setIsContextSidebarCollapsed: (collapsed) => set({ isContextSidebarCollapsed: collapsed }),
    }),
    {
      name: 'promptflow-app-store',
    }
  )
)