---
type: "deployment_guide"
role: "production_guide"
dependencies:
  - "core/PROJECT_CORE.md"
  - "frontend/MAIN_APP.md"
  - "frontend/ADMIN_PANEL.md"
  - "backend/SUPABASE.md"
auto_update_from:
  - "core/PROJECT_CORE.md"
  - "fixes/BUILD_ERROR_RESOLUTION.md"
development_history:
  - date: "2025-01-29"
    change: "Production deployment rules updated for dual app architecture"
    deployment_targets: ["Main app (Next.js 15)", "Admin panel (Next.js 14)", "Supabase backend"]
last_updated: "2025-01-29"
---

# PromptFlow Production Deployment Rules

## Infrastructure Overview
### Hosting Platforms
- **Main App**: Vercel (Next.js optimized)
- **Admin Panel**: Vercel (separate deployment)
- **Database**: Supabase Cloud (eu-central-1)
- **CDN**: Vercel Edge Network
- **Monitoring**: Vercel Analytics + Custom monitoring

### Domain Configuration
```bash
# Production domains
Main App: https://promptflow.app
Admin Panel: https://admin.promptflow.app
API: https://api.promptflow.app (Supabase)

# Development domains
Main App: https://dev.promptflow.app
Admin Panel: https://admin-dev.promptflow.app
```

## Build Configuration
### Main App (promptflow/)
```json
// package.json
{
  "scripts": {
    "dev": "next dev --turbopack -p 4444",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit"
  }
}
```

```typescript
// next.config.ts
const nextConfig: NextConfig = {
  productionBrowserSourceMaps: false,
  compress: true,
  experimental: {
    optimizePackageImports: ['lucide-react'],
    optimizeCss: true,
  },
  optimizeFonts: true,
  
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          { key: 'X-Content-Type-Options', value: 'nosniff' },
          { key: 'X-Frame-Options', value: 'DENY' },
          { key: 'X-XSS-Protection', value: '1; mode=block' },
        ],
      },
    ]
  },
}
```

### Admin Panel (admin/)
```json
// package.json
{
  "scripts": {
    "dev": "next dev --port 4445",
    "build": "next build",
    "start": "next start",
    "analyze": "ANALYZE=true npm run build"
  }
}
```

## Environment Variables
### Production Environment
```bash
# Main App (.env.production)
NEXT_PUBLIC_SUPABASE_URL=https://iqehopwgrczylqliajww.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIs...
NEXT_PUBLIC_APP_URL=https://promptflow.app
NODE_ENV=production

# Admin Panel (.env.production)
NEXT_PUBLIC_SUPABASE_URL=https://iqehopwgrczylqliajww.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIs...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIs...
NEXT_PUBLIC_ADMIN_URL=https://admin.promptflow.app
NODE_ENV=production
```

### Staging Environment
```bash
# Staging variables
NEXT_PUBLIC_SUPABASE_URL=https://staging-project.supabase.co
NEXT_PUBLIC_APP_URL=https://dev.promptflow.app
NODE_ENV=staging
```

## CI/CD Pipeline
### GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy-main-app:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'promptflow/package-lock.json'

      - name: Install dependencies
        run: cd promptflow && npm ci

      - name: Type check
        run: cd promptflow && npm run type-check

      - name: Build
        run: cd promptflow && npm run build
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}

      - name: Deploy to Vercel
        uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: promptflow
```

## Database Deployment
### Migration Strategy
```sql
-- Database migrations
-- migrations/001_initial_schema.sql
-- migrations/002_user_plans.sql
-- migrations/003_context_gallery.sql

-- Deployment script
-- deploy/deploy-database.sql
\i migrations/001_initial_schema.sql;
\i migrations/002_user_plans.sql;
\i migrations/003_context_gallery.sql;
```

### Backup Strategy
```bash
# Automated backups
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql

# Backup retention
- Daily backups: 30 days
- Weekly backups: 12 weeks
- Monthly backups: 12 months
```

## Monitoring & Alerting
### Performance Monitoring
```typescript
// Custom monitoring
const trackPerformance = (metric: string, value: number) => {
  // Send to monitoring service
  fetch('/api/metrics', {
    method: 'POST',
    body: JSON.stringify({ metric, value, timestamp: Date.now() })
  })
}

// Error tracking
const trackError = (error: Error, context: Record<string, any>) => {
  console.error('Application error:', error, context)

  // Send to error tracking service
  fetch('/api/errors', {
    method: 'POST',
    body: JSON.stringify({
      message: error.message,
      stack: error.stack,
      context,
      timestamp: Date.now()
    })
  })
}
```

### Health Checks
```typescript
// API health check
export async function GET() {
  try {
    // Check database connection
    const { data, error } = await supabase
      .from('projects')
      .select('count')
      .limit(1)

    if (error) throw error

    return Response.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: 'connected'
    })
  } catch (error) {
    return Response.json({
      status: 'unhealthy',
      error: error.message
    }, { status: 500 })
  }
}
```

## Security in Production
### SSL/TLS Configuration
```bash
# Vercel automatic HTTPS
- SSL certificates auto-managed
- HTTP to HTTPS redirects
- HSTS headers enabled
- Perfect Forward Secrecy
```

### Security Headers
```typescript
// Security headers in production
const securityHeaders = {
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';",
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()'
}
```

## Update Requirements
- Update this file when modifying deployment process
- Document infrastructure changes
- Maintain environment configurations
- Keep monitoring setup current
- Update CI/CD pipelines when adding new steps
- Document new security configurations
