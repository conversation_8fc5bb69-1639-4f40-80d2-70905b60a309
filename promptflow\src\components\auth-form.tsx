'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useSignInWithEmail, useSignUpWithEmail } from '@/hooks/use-auth'
import { AlertCircle, Loader2 } from 'lucide-react'

export function AuthForm() {
  const [isSignUp, setIsSignUp] = useState(false)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')

  const signInMutation = useSignInWithEmail()
  const signUpMutation = useSignUpWithEmail()

  const isLoading = signInMutation.isPending || signUpMutation.isPending

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    console.log(`📝 [AUTH_FORM] Form submitted: ${isSignUp ? 'SIGN_UP' : 'SIGN_IN'} for ${email}`)

    if (!email || !password) {
      console.warn(`⚠️ [AUTH_FORM] Missing fields: email=${!!email}, password=${!!password}`)
      setError('Lütfen tüm alanları doldurun')
      return
    }

    try {
      if (isSignUp) {
        console.log(`📝 [AUTH_FORM] Attempting sign up...`)
        await signUpMutation.mutateAsync({ email, password })
        console.log(`✅ [AUTH_FORM] Sign up successful`)
      } else {
        console.log(`📝 [AUTH_FORM] Attempting sign in...`)
        const result = await signInMutation.mutateAsync({ email, password })
        console.log(`✅ [AUTH_FORM] Sign in successful:`, result)
        // Redirect will be handled by auth state listener in use-auth.ts
      }
    } catch (error) {
      console.error(`❌ [AUTH_FORM] Auth failed:`, error)
      setError(error instanceof Error ? error.message : 'Bir hata oluştu')
    }
  }

  return (
    <div className="full-height-mobile flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4 safe-area-top safe-area-bottom">
      <div className="w-full max-w-md">
        <div className="text-center mb-6 lg:mb-8">
          <div className="flex items-center justify-center mb-4">
            <img
              src="/logo.png"
              alt="Promptbir Logo"
              className="h-12 w-auto lg:h-16"
            />
          </div>
          <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2 mobile-text-base">Promptbir</h1>
          <p className="text-gray-600 mobile-text-base">AI Prompt Yönetim Platformu</p>
        </div>

        <Card className="shadow-xl border-0 mobile-card-shadow mobile-transition">
          <CardHeader className="space-y-1 p-4 lg:p-6">
            <CardTitle className="text-xl lg:text-2xl text-center mobile-text-base">
              {isSignUp ? 'Hesap Oluştur' : 'Giriş Yap'}
            </CardTitle>
            <CardDescription className="text-center mobile-text-base">
              {isSignUp
                ? 'Promptbir\'e katılın ve prompt\'larınızı yönetin'
                : 'Hesabınıza giriş yapın'
              }
            </CardDescription>
          </CardHeader>
          
          <CardContent className="p-4 lg:p-6 pt-0">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="mobile-text-base">E-posta</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                  className="mobile-text-base touch-target"
                  autoComplete="email"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password" className="mobile-text-base">Şifre</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={isLoading}
                  className="mobile-text-base touch-target"
                  autoComplete={isSignUp ? "new-password" : "current-password"}
                />
              </div>

              {error && (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md mobile-card-spacing">
                  <AlertCircle className="h-4 w-4 text-red-600 flex-shrink-0" />
                  <p className="text-sm text-red-700 mobile-text-base">{error}</p>
                </div>
              )}

              <Button 
                type="submit" 
                className="w-full touch-target mobile-btn mobile-transition focus-visible-enhanced"
                disabled={isLoading}
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isSignUp ? 'Hesap Oluştur' : 'Giriş Yap'}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <button
                type="button"
                onClick={() => {
                  setIsSignUp(!isSignUp)
                  setError('')
                }}
                className="text-sm text-blue-600 hover:text-blue-700 underline mobile-text-base touch-target focus-visible-enhanced mobile-transition"
                disabled={isLoading}
              >
                {isSignUp 
                  ? 'Zaten hesabınız var mı? Giriş yapın' 
                  : 'Hesabınız yok mu? Hesap oluşturun'
                }
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 