import { useEffect, useRef, useCallback } from 'react';

// Hook to measure component render performance
export function useRenderPerformance(componentName: string) {
  const renderStartTime = useRef<number>(0);
  const renderCount = useRef<number>(0);

  useEffect(() => {
    renderStartTime.current = performance.now();
    renderCount.current += 1;
  });

  useEffect(() => {
    const renderTime = performance.now() - renderStartTime.current;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔍 ${componentName} render #${renderCount.current}: ${renderTime.toFixed(2)}ms`);
      
      // Warn about slow renders
      if (renderTime > 16) { // 60fps = 16.67ms per frame
        console.warn(`⚠️ Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`);
      }
    }
  });

  return {
    renderCount: renderCount.current,
    measureRender: useCallback((label: string) => {
      const start = performance.now();
      return () => {
        const end = performance.now();
        if (process.env.NODE_ENV === 'development') {
          console.log(`📊 ${componentName} - ${label}: ${(end - start).toFixed(2)}ms`);
        }
      };
    }, [componentName]),
  };
}

// Hook to detect unnecessary re-renders
export function useWhyDidYouUpdate(name: string, props: Record<string, any>) {
  const previousProps = useRef<Record<string, any>>();

  useEffect(() => {
    if (previousProps.current) {
      const allKeys = Object.keys({ ...previousProps.current, ...props });
      const changedProps: Record<string, { from: any; to: any }> = {};

      allKeys.forEach((key) => {
        if (previousProps.current![key] !== props[key]) {
          changedProps[key] = {
            from: previousProps.current![key],
            to: props[key],
          };
        }
      });

      if (Object.keys(changedProps).length && process.env.NODE_ENV === 'development') {
        console.log('🔄 [why-did-you-update]', name, changedProps);
      }
    }

    previousProps.current = props;
  });
}

// Hook to measure async operations
export function useAsyncPerformance() {
  return useCallback((operationName: string) => {
    const start = performance.now();
    
    return {
      end: () => {
        const duration = performance.now() - start;
        if (process.env.NODE_ENV === 'development') {
          console.log(`⏱️ ${operationName}: ${duration.toFixed(2)}ms`);
        }
        return duration;
      },
    };
  }, []);
}

// Hook to track component mount/unmount performance
export function useComponentLifecycle(componentName: string) {
  const mountTime = useRef<number>(0);

  useEffect(() => {
    mountTime.current = performance.now();
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`🚀 ${componentName} mounted`);
    }

    return () => {
      const lifetime = performance.now() - mountTime.current;
      if (process.env.NODE_ENV === 'development') {
        console.log(`💀 ${componentName} unmounted after ${lifetime.toFixed(2)}ms`);
      }
    };
  }, [componentName]);
}

// Hook to measure memory usage (experimental)
export function useMemoryUsage(componentName: string) {
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && 'memory' in performance) {
      const memory = (performance as any).memory;
      console.log(`🧠 ${componentName} memory:`, {
        used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
        total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
        limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`,
      });
    }
  });
}
