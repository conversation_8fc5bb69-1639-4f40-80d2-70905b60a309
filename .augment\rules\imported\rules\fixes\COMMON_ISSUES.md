---
type: "fixes"
role: "general_troubleshooting_guide"
dependencies:
  - "core/PROJECT_CORE.md"
related_fixes:
  - "fixes/BUILD_ERROR_RESOLUTION.md"
  - "fixes/CONTEXT_GALLERY_ISSUES.md"
  - "fixes/ESLINT_CONFIGURATION.md"
  - "fixes/TYPESCRIPT_PATTERNS.md"
  - "fixes/DEPENDENCY_MANAGEMENT.md"
auto_update_from:
  - "features/CONTEXT_GALLERY.md"
  - "frontend/MAIN_APP.md"
  - "frontend/ADMIN_PANEL.md"
solution_history:
  - date: "2025-01-29"
    issue: "Context Gallery Ekle button not working"
    solution: "Documented in CONTEXT_GALLERY_ISSUES.md with complete fix"
    status: "resolved"
last_updated: "2025-01-29"
---

# PromptFlow Common Issues & Fixes

## Authentication Errors

### Issue: "relation 'plan_types' does not exist"
**Symptoms:** 500 error during user signup
**Root Cause:** Trigger function lacks schema references
**Solution:**
```sql
-- Recreate function with explicit schema
CREATE OR REPLACE FUNCTION public.assign_free_plan_to_new_user()
RETURNS TRIGGER AS $$
DECLARE
  free_plan_id uuid;
BEGIN
  SELECT id INTO free_plan_id 
  FROM public.plan_types 
  WHERE name = 'free' AND is_active = true;
  
  IF free_plan_id IS NOT NULL THEN
    INSERT INTO public.user_plans (user_id, plan_type_id, status, billing_cycle)
    VALUES (NEW.id, free_plan_id, 'active', 'lifetime');
    
    INSERT INTO public.usage_stats (user_id, stat_date, projects_count, prompts_count)
    VALUES (NEW.id, CURRENT_DATE, 0, 0);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate trigger
DROP TRIGGER IF EXISTS assign_free_plan_on_signup ON auth.users;
CREATE TRIGGER assign_free_plan_on_signup
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.assign_free_plan_to_new_user();
```

### Issue: "isAdmin is not a function"
**Symptoms:** TypeError in admin login
**Root Cause:** Naming conflict between boolean and function
**Solution:**
```typescript
// Rename conflicting variables
const { data: { user }, error } = await supabase.auth.getUser()
const adminStatus = await checkIsAdmin(user?.id)

// Use clear naming
const handleLogin = async () => {
  try {
    const { data: { user }, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    
    if (authError) throw authError
    
    const isUserAdmin = await checkIsAdmin(user.id)
    if (isUserAdmin) {
      router.push('/admin/dashboard')
    }
  } catch (error) {
    console.error('Login error:', error)
  }
}
```

## Performance Issues

### Issue: Slow Plan Limit Checks
**Symptoms:** UI lag when checking limits
**Solution:** Use cached results with React Query
```typescript
const { data: limits } = useUserLimits({
  staleTime: 30 * 1000, // 30 seconds cache
  gcTime: 5 * 60 * 1000, // 5 minutes garbage collection
})

// Optimistic limit checks
const canCreateProject = useMemo(() => {
  return limits?.can_create_project ?? true // Optimistic default
}, [limits])
```

### Issue: Font Preload Warnings
**Symptoms:** Browser console warnings about unused fonts
**Solution:**
```typescript
const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
  preload: true,
});

// Next.js config optimization
const nextConfig = {
  optimizeFonts: true,
  experimental: {
    optimizeCss: true,
  }
}
```

## Database Issues

### Issue: RLS Policy Violations
**Symptoms:** "permission denied" errors in queries
**Root Cause:** Missing or incorrect RLS policies
**Solution:**
```sql
-- Check existing policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies
WHERE tablename = 'your_table';

-- Fix user data access
CREATE POLICY "Users access own data" ON prompts
  FOR ALL USING (auth.uid() = user_id);

-- Fix admin access
CREATE POLICY "Admins full access" ON prompts
  FOR ALL USING (
    EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
  );
```

### Issue: Slow Query Performance
**Symptoms:** Long database response times
**Solution:**
```sql
-- Add missing indexes
CREATE INDEX CONCURRENTLY idx_prompts_user_project
ON prompts(user_id, project_id);

CREATE INDEX CONCURRENTLY idx_prompts_tags_gin
ON prompts USING gin(tags);

-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM prompts WHERE user_id = $1;
```

## Frontend Issues

### Issue: Hydration Mismatches
**Symptoms:** React hydration errors
**Root Cause:** Server/client rendering differences
**Solution:**
```typescript
// Use dynamic imports for client-only components
const ClientOnlyComponent = dynamic(
  () => import('./ClientOnlyComponent'),
  { ssr: false }
)

// Use useEffect for client-side only logic
const [mounted, setMounted] = useState(false)

useEffect(() => {
  setMounted(true)
}, [])

if (!mounted) return null
```

### Issue: Drag & Drop Not Working
**Symptoms:** DnD functionality broken
**Solution:**
```typescript
// Ensure proper DnD context setup
import { DndContext, closestCenter } from '@dnd-kit/core'
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'

// Check for touch device support
const sensors = useSensors(
  useSensor(PointerSensor),
  useSensor(KeyboardSensor, {
    coordinateGetter: sortableKeyboardCoordinates,
  })
)

<DndContext
  sensors={sensors}
  collisionDetection={closestCenter}
  onDragEnd={handleDragEnd}
>
  <SortableContext items={items} strategy={verticalListSortingStrategy}>
    {/* sortable items */}
  </SortableContext>
</DndContext>
```

## Build & Deployment Issues

### Issue: Systematic Build Error Resolution
**Symptoms:** Multiple build errors requiring systematic approach
**Solution:** Follow BUILD_ERROR_RESOLUTION.md process
```bash
# 1. Identify error types
npm run build 2>&1 | tee build-errors.log

# 2. Fix TypeScript any types
# Replace Record<string, any> with Record<string, boolean | string | number>

# 3. Fix ESLint escape characters
# Replace ' with &apos; in Turkish text

# 4. Remove unused imports/variables
# Clean up unused code identified by ESLint

# 5. Update Next.js configuration
# Remove deprecated options like optimizeFonts

# 6. Install missing dependencies
npm install critters  # For CSS optimization
npm audit fix         # Security fixes

# 7. Verify build success
npm run build && npm run dev
```

### Issue: TypeScript `any` Type Violations
**Symptoms:** `@typescript-eslint/no-explicit-any` errors
**Solution:**
```typescript
// Common patterns to fix:
// ❌ features: Record<string, any>
// ✅ features: Record<string, boolean | string | number>

// ❌ metadata: Record<string, any>
// ✅ metadata: Record<string, boolean | string | number>

// Files commonly affected:
// - src/lib/supabase.ts
// - src/lib/plan-limits.ts
// - src/components/plan-upgrade-modal.tsx
```

### Issue: ESLint Escape Character Errors
**Symptoms:** Unescaped apostrophes in Turkish text
**Solution:**
```typescript
// ❌ <span>Prompt'lar</span>
// ✅ <span>Prompt&apos;lar</span>

// Common locations:
// - Plan display components
// - Usage statistics
// - User interface text
```

### Issue: Next.js Configuration Deprecation
**Symptoms:** "Invalid next.config.ts options detected"
**Solution:**
```typescript
// ❌ Next.js 15 deprecated
const nextConfig = {
  optimizeFonts: true, // Remove this line
}

// ✅ Current approach
const nextConfig = {
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react'],
  }
}
```

### Issue: Build Failures
**Symptoms:** Next.js build errors
**Common Causes & Solutions:**
```bash
# TypeScript errors
npm run type-check
# Fix type errors before building

# Memory issues
NODE_OPTIONS="--max-old-space-size=4096" npm run build

# Dependency conflicts
rm -rf node_modules package-lock.json
npm install

# Missing dependencies
npm install critters  # CSS optimization
npm audit fix         # Security fixes

# Environment variable issues
# Ensure all required env vars are set in production
```

### Issue: Vercel Deployment Failures
**Symptoms:** Deployment fails on Vercel
**Solution:**
```bash
# Check build logs for specific errors
# Common fixes:

# 1. Build command issues
"scripts": {
  "build": "next build",
  "start": "next start"
}

# 2. Environment variables
# Ensure all NEXT_PUBLIC_ vars are set in Vercel dashboard

# 3. Node.js version
# Set Node.js version in package.json
"engines": {
  "node": ">=18.0.0"
}
```

## Real-time Issues

### Issue: Supabase Realtime Not Working
**Symptoms:** Live updates not appearing
**Solution:**
```typescript
// Check subscription setup
useEffect(() => {
  const channel = supabase
    .channel('prompts')
    .on('postgres_changes', {
      event: '*',
      schema: 'public',
      table: 'prompts',
      filter: `project_id=eq.${projectId}`
    }, (payload) => {
      console.log('Realtime update:', payload)
      // Handle update
    })
    .subscribe((status) => {
      console.log('Subscription status:', status)
    })

  return () => {
    supabase.removeChannel(channel)
  }
}, [projectId])

// Check RLS policies allow realtime
-- Ensure RLS policies don't block realtime
CREATE POLICY "Enable realtime" ON prompts
  FOR SELECT USING (auth.uid() = user_id);
```

## Related Documentation
- **BUILD_ERROR_RESOLUTION.md**: Systematic build error resolution process
- **TYPESCRIPT_PATTERNS.md**: TypeScript type safety and error patterns
- **ESLINT_CONFIGURATION.md**: ESLint rules and automated fixes
- **DEPENDENCY_MANAGEMENT.md**: Package management and security procedures

## Update Requirements
- Update this file when encountering new issues
- Document root causes and complete solutions
- Maintain troubleshooting procedures
- Keep fix verification steps current
- Add new issue categories as they arise
- Document prevention strategies for common issues
- Cross-reference with specialized rule files for detailed procedures
