---
type: "admin_frontend_guide"
role: "admin_ui_guide"
dependencies:
  - "core/PROJECT_CORE.md"
  - "backend/SUPABASE.md"
related_admin_features:
  - "features/CONTEXT_GALLERY_ADMIN.md"
  - "features/USER_PLANS.md"
auto_update_targets:
  - "features/CONTEXT_GALLERY_ADMIN.md"
development_history:
  - date: "2025-01-29"
    change: "Admin panel structure documented for Context Gallery integration"
    admin_pages: ["/admin/contexts", "/admin/users", "/admin/dashboard"]
    integration_points: ["Context management", "User management", "Analytics"]
last_updated: "2025-01-29"
---

# PromptFlow Admin Panel Frontend Rules

## Technology Stack
- **Framework**: Next.js 14.2.18 (App Router)
- **React**: 18.3.1 (Stable)
- **TypeScript**: Strict mode enabled
- **UI Library**: Chakra UI + Tailwind CSS
- **Charts**: Recharts for analytics
- **Performance**: Lazy loading + Code splitting

## Project Structure
```
admin/src/
├── app/                    # Next.js App Router
│   ├── dashboard/          # Main dashboard
│   ├── users/              # User management
│   ├── projects/           # Project management
│   ├── prompts/            # Prompt management
│   ├── contexts/           # Context management
│   ├── plans/              # User plans management
│   └── settings/           # System settings
├── components/             # Reusable components
│   ├── admin-layout.tsx    # Main layout
│   ├── admin-sidebar.tsx   # Navigation
│   ├── drag-drop/          # DnD components
│   ├── multi-select/       # Bulk operations
│   ├── realtime/           # Real-time features
│   └── lazy-components.tsx # Performance
├── hooks/                  # Admin-specific hooks
│   ├── use-admin-auth.ts   # Admin authentication
│   ├── use-users.ts        # User management
│   ├── use-admin-prompts.ts
│   └── use-admin-contexts.ts
└── lib/                    # Utilities
    ├── chakra-components.ts # Tree-shaken imports
    ├── performance-monitor.ts
    └── query-client.ts     # Optimized config
```

## Chakra UI Patterns
### Tree-shaken Imports
```typescript
import { 
  Box, Container, Flex, VStack, HStack,
  Button, Input, Select, Textarea,
  Table, Thead, Tbody, Tr, Th, Td,
  Modal, ModalOverlay, ModalContent,
  useToast, useDisclosure
} from '@/lib/chakra-components'
```

### Layout Components
```typescript
<AdminLayout>
  <Container maxW="7xl">
    <VStack spacing={6}>
      <Flex justify="space-between" w="full">
        <Heading>Page Title</Heading>
        <Button colorScheme="blue">Action</Button>
      </Flex>
      <Box w="full">
        {/* Page content */}
      </Box>
    </VStack>
  </Container>
</AdminLayout>
```

## Performance Optimizations
### Lazy Loading
```typescript
const LazyDashboard = lazy(() => import('@/app/dashboard/page'))
const LazyUsers = lazy(() => import('@/app/users/page'))
const LazyCharts = lazy(() => import('@/components/charts'))

<Suspense fallback={<LoadingSpinner />}>
  <LazyDashboard />
</Suspense>
```

### Code Splitting
```typescript
// Dynamic imports for heavy features
const loadChartLibrary = () => import('recharts')
const loadDateUtils = () => import('date-fns')
const loadFormValidation = () => import('zod')
```

## Admin Features
### User Management
```typescript
// Paginated user lists with search/filters
const { data: users } = useUsers({
  page: currentPage,
  limit: 20,
  search: searchTerm,
  role: selectedRole
})

// Bulk operations with multi-select
<MultiSelectToolbar
  selectedItems={selectedUsers}
  onBulkAction={handleBulkAction}
  actions={['activate', 'deactivate', 'delete']}
/>
```

### Plan Management
```typescript
// Plan statistics dashboard
const { data: planStats } = usePlanStatistics()

// User plan assignments
const changePlanMutation = useChangePlan()
await changePlanMutation.mutateAsync({
  userId,
  planName: 'professional',
  billingCycle: 'yearly'
})
```

### System Monitoring
```typescript
// Performance metrics tracking
<PerformanceMonitor />

// Real-time connection status
<ConnectionStatus />

// Error monitoring and alerts
<ErrorBoundary fallback={<ErrorFallback />}>
  <AdminContent />
</ErrorBoundary>
```

### Data Tables
```typescript
// Optimized data tables with virtualization
<DataTable
  data={users}
  columns={userColumns}
  pagination={true}
  sorting={true}
  filtering={true}
  virtualizeRows={true}
/>
```

## Security Rules
- Admin-only route protection via `<AdminGuard />`
- RLS policy enforcement on all data access
- Secure API endpoints with proper validation
- Audit trail logging for all admin actions

## Navigation Structure
```typescript
const navigationItems = [
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { name: 'Kullanıcılar', href: '/users', icon: Users },
  { name: 'Projeler', href: '/projects', icon: FolderOpen },
  { name: 'Prompt\'lar', href: '/prompts', icon: FileText },
  { name: 'Context\'ler', href: '/contexts', icon: MessageSquare },
  { name: 'Kullanıcı Planları', href: '/plans', icon: Crown },
  { name: 'Ayarlar', href: '/settings', icon: Settings },
]
```

## Update Requirements
- Update this file when adding admin features
- Document new management capabilities
- Maintain security protocols
- Keep performance optimizations current
- Update navigation when adding new pages
- Document new data table implementations
