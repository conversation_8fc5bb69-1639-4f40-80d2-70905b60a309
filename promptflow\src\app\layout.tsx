import type { Metada<PERSON>, Viewport } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { QueryProvider } from "@/providers/query-provider";
import { performRuntimeSecurityCheck } from "@/lib/env-security";
import { Toaster } from "@/components/ui/sonner";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
  preload: true,
});

export const metadata: Metadata = {
  title: {
    default: "Promptbir - AI Prompt Yönetim Platformu",
    template: "%s | Promptbir"
  },
  description: "Yapay zeka prompt'larınızı profesyonelce yönetin. Takımınızla paylaşın, organize edin ve verimliliğinizi artırın. 10,000+ geliştirici tarafından güvenilir.",
  keywords: [
    "AI prompt yönetimi",
    "yapay zeka araçları",
    "prompt organizasyonu",
    "AI geliştirici araçları",
    "prompt sharing",
    "takım çalışması",
    "AI workflow",
    "prompt templates",
    "ChatGPT prompts",
    "AI productivity"
  ],
  authors: [{ name: "Promptbir Team", url: "https://promptbir.com" }],
  creator: "Promptbir",
  publisher: "Promptbir",
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon.ico', sizes: 'any' }
    ],
    shortcut: '/favicon.ico',
    apple: '/apple-touch-icon.png',
  },
  manifest: '/site.webmanifest',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "tr_TR",
    url: "https://promptbir.com",
    siteName: "Promptbir",
    title: "Promptbir - AI Prompt Yönetim Platformu",
    description: "Yapay zeka prompt'larınızı profesyonelce yönetin. Takımınızla paylaşın, organize edin ve verimliliğinizi artırın. 10,000+ geliştirici tarafından güvenilir.",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "Promptbir - AI Prompt Yönetim Platformu",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    site: "@promptbir",
    creator: "@promptbir",
    title: "Promptbir - AI Prompt Yönetim Platformu",
    description: "Yapay zeka prompt'larınızı profesyonelce yönetin. Takımınızla paylaşın, organize edin ve verimliliğinizi artırın.",
    images: ["/twitter-image.png"],
  },
  verification: {
    google: "your-google-verification-code",
  },
  alternates: {
    canonical: "https://promptbir.com",
    languages: {
      'tr-TR': 'https://promptbir.com',
      'en-US': 'https://promptbir.com/en',
    },
  },
  category: "Technology",
  metadataBase: new URL('https://promptbir.com'),
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#000000' },
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Run security check on server-side
  if (typeof window === 'undefined') {
    performRuntimeSecurityCheck();
  }

  return (
    <html lang="tr">
      <head>
        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      </head>
      <body
        className={`${inter.variable} font-sans antialiased`}
      >
        <QueryProvider>
          {children}
          <Toaster />
        </QueryProvider>
      </body>
    </html>
  );
}
