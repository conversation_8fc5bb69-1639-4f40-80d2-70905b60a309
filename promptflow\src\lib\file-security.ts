/**
 * File Upload Security System
 * Comprehensive file validation and security checks
 */

import { AdvancedValidator, ValidationResult } from './advanced-validation'

// File type configurations
export const FILE_CONFIGS = {
  // Image files
  images: {
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg']
  },
  
  // Document files
  documents: {
    allowedTypes: ['text/plain', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedExtensions: ['.txt', '.pdf', '.doc', '.docx']
  },
  
  // Data files
  data: {
    allowedTypes: ['application/json', 'text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
    maxSize: 50 * 1024 * 1024, // 50MB
    allowedExtensions: ['.json', '.csv', '.xls', '.xlsx']
  },
  
  // Archive files
  archives: {
    allowedTypes: ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed'],
    maxSize: 100 * 1024 * 1024, // 100MB
    allowedExtensions: ['.zip', '.rar', '.7z']
  }
} as const

// Dangerous file types that should never be allowed
export const DANGEROUS_FILE_TYPES = [
  // Executable files
  '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.vbe', '.js', '.jse', '.wsf', '.wsh',
  
  // Script files
  '.ps1', '.ps1xml', '.ps2', '.ps2xml', '.psc1', '.psc2', '.msh', '.msh1', '.msh2', '.mshxml',
  
  // System files
  '.msi', '.msp', '.mst', '.dll', '.sys', '.drv',
  
  // Macro files
  '.xlsm', '.xltm', '.docm', '.dotm', '.pptm', '.potm', '.ppam', '.ppsm', '.sldm',
  
  // Other dangerous
  '.jar', '.class', '.dex', '.apk', '.ipa', '.dmg', '.pkg', '.deb', '.rpm'
] as const

// MIME type spoofing detection
export const MIME_SIGNATURES = {
  'image/jpeg': [0xFF, 0xD8, 0xFF],
  'image/png': [0x89, 0x50, 0x4E, 0x47],
  'image/gif': [0x47, 0x49, 0x46],
  'application/pdf': [0x25, 0x50, 0x44, 0x46],
  'application/zip': [0x50, 0x4B, 0x03, 0x04],
  'text/plain': [] // No specific signature
} as const

interface FileSecurityOptions {
  allowedTypes?: string[]
  maxSize?: number
  allowedExtensions?: string[]
  checkMimeType?: boolean
  scanContent?: boolean
  quarantineOnSuspicion?: boolean
}

interface FileSecurityResult extends ValidationResult {
  fileInfo: {
    name: string
    size: number
    type: string
    extension: string
    lastModified: Date
  }
  securityChecks: {
    mimeTypeValid: boolean
    extensionSafe: boolean
    sizeValid: boolean
    contentSafe: boolean
    signatureValid: boolean
  }
  recommendations: string[]
}

/**
 * Comprehensive file security validator
 */
export class FileSecurityValidator {
  /**
   * Validate file with comprehensive security checks
   */
  static async validateFile(
    file: File, 
    options: FileSecurityOptions = {}
  ): Promise<FileSecurityResult> {
    const errors: string[] = []
    const warnings: string[] = []
    const recommendations: string[] = []

    // Extract file info
    const fileInfo = {
      name: file.name,
      size: file.size,
      type: file.type,
      extension: this.getFileExtension(file.name),
      lastModified: new Date(file.lastModified)
    }

    // Initialize security checks
    const securityChecks = {
      mimeTypeValid: false,
      extensionSafe: false,
      sizeValid: false,
      contentSafe: false,
      signatureValid: false
    }

    // 1. File name validation
    const nameValidation = AdvancedValidator.validate(file.name, {
      required: true,
      maxLength: 255,
      pattern: /^[a-zA-Z0-9\s\-_.()]+$/,
      blockedPatterns: [/\.\./g, /[<>:"|?*]/g]
    })

    if (!nameValidation.isValid) {
      errors.push('Güvenli olmayan dosya adı')
    }

    // 2. Extension security check
    securityChecks.extensionSafe = this.checkExtensionSecurity(fileInfo.extension)
    if (!securityChecks.extensionSafe) {
      errors.push('Güvenlik nedeniyle engellenmiş dosya türü')
    }

    // 3. Size validation
    const maxSize = options.maxSize || 10 * 1024 * 1024 // Default 10MB
    securityChecks.sizeValid = file.size <= maxSize
    if (!securityChecks.sizeValid) {
      errors.push(`Dosya boyutu ${Math.round(maxSize / 1024 / 1024)}MB'dan büyük olamaz`)
    }

    // 4. MIME type validation
    if (options.allowedTypes) {
      securityChecks.mimeTypeValid = options.allowedTypes.includes(file.type)
      if (!securityChecks.mimeTypeValid) {
        errors.push('Desteklenmeyen dosya türü')
      }
    } else {
      securityChecks.mimeTypeValid = true
    }

    // 5. Extension vs MIME type consistency
    if (options.checkMimeType !== false) {
      const mimeConsistency = await this.checkMimeTypeConsistency(file)
      if (!mimeConsistency.isConsistent) {
        warnings.push('Dosya türü ve uzantısı uyumsuz')
        recommendations.push('Dosya içeriğini kontrol edin')
      }
    }

    // 6. File signature validation
    if (options.checkMimeType !== false) {
      securityChecks.signatureValid = await this.validateFileSignature(file)
      if (!securityChecks.signatureValid) {
        warnings.push('Dosya imzası geçersiz')
      }
    } else {
      securityChecks.signatureValid = true
    }

    // 7. Content scanning (basic)
    if (options.scanContent !== false) {
      securityChecks.contentSafe = await this.scanFileContent(file)
      if (!securityChecks.contentSafe) {
        errors.push('Dosya içeriği güvenli değil')
      }
    } else {
      securityChecks.contentSafe = true
    }

    // 8. Additional security recommendations
    if (file.size > 50 * 1024 * 1024) { // 50MB
      recommendations.push('Büyük dosyalar performans sorunlarına neden olabilir')
    }

    if (fileInfo.extension === '.svg') {
      recommendations.push('SVG dosyaları script içerebilir, içeriği kontrol edin')
    }

    if (file.type.startsWith('application/')) {
      recommendations.push('Uygulama dosyaları ek güvenlik kontrolü gerektirir')
    }

    return {
      isValid: errors.length === 0,
      sanitizedValue: file.name,
      errors,
      warnings,
      fileInfo,
      securityChecks,
      recommendations
    }
  }

  /**
   * Get file extension safely
   */
  private static getFileExtension(filename: string): string {
    const lastDot = filename.lastIndexOf('.')
    return lastDot === -1 ? '' : filename.substring(lastDot).toLowerCase()
  }

  /**
   * Check if file extension is safe
   */
  private static checkExtensionSecurity(extension: string): boolean {
    return !DANGEROUS_FILE_TYPES.includes(extension.toLowerCase() as typeof DANGEROUS_FILE_TYPES[number])
  }

  /**
   * Check MIME type consistency with file extension
   */
  private static async checkMimeTypeConsistency(file: File): Promise<{
    isConsistent: boolean
    detectedType?: string
  }> {
    try {
      // Read first few bytes to detect file signature
      const buffer = await this.readFileBytes(file, 0, 16)
      const detectedType = this.detectFileTypeFromSignature(buffer)
      
      if (!detectedType) {
        return { isConsistent: true } // Can't detect, assume consistent
      }

      const isConsistent = file.type === detectedType || 
                          this.areCompatibleTypes(file.type, detectedType)

      return { isConsistent, detectedType }
    } catch (error) {
      console.warn('MIME type consistency check failed:', error)
      return { isConsistent: true } // Fail open for usability
    }
  }

  /**
   * Validate file signature against declared MIME type
   */
  private static async validateFileSignature(file: File): Promise<boolean> {
    try {
      const buffer = await this.readFileBytes(file, 0, 16)
      const signature = MIME_SIGNATURES[file.type as keyof typeof MIME_SIGNATURES]
      
      if (!signature || signature.length === 0) {
        return true // No signature to check
      }

      // Check if file starts with expected signature
      for (let i = 0; i < signature.length; i++) {
        if (buffer[i] !== signature[i]) {
          return false
        }
      }

      return true
    } catch (error) {
      console.warn('File signature validation failed:', error)
      return true // Fail open
    }
  }

  /**
   * Basic content scanning for malicious patterns
   */
  private static async scanFileContent(file: File): Promise<boolean> {
    try {
      // Only scan text-based files
      if (!file.type.startsWith('text/') && 
          !file.type.includes('json') && 
          !file.type.includes('xml')) {
        return true // Skip binary files
      }

      const text = await file.text()
      
      // Check for script injection patterns
      const maliciousPatterns = [
        /<script[^>]*>/gi,
        /javascript:/gi,
        /vbscript:/gi,
        /data:text\/html/gi,
        /eval\s*\(/gi,
        /Function\s*\(/gi,
        /setTimeout\s*\(/gi,
        /setInterval\s*\(/gi
      ]

      for (const pattern of maliciousPatterns) {
        if (pattern.test(text)) {
          return false
        }
      }

      return true
    } catch (error) {
      console.warn('Content scanning failed:', error)
      return true // Fail open
    }
  }

  /**
   * Read specific bytes from file
   */
  private static readFileBytes(file: File, start: number, length: number): Promise<Uint8Array> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = () => {
        const arrayBuffer = reader.result as ArrayBuffer
        resolve(new Uint8Array(arrayBuffer))
      }
      
      reader.onerror = () => reject(reader.error)
      
      const blob = file.slice(start, start + length)
      reader.readAsArrayBuffer(blob)
    })
  }

  /**
   * Detect file type from signature
   */
  private static detectFileTypeFromSignature(buffer: Uint8Array): string | null {
    for (const [mimeType, signature] of Object.entries(MIME_SIGNATURES)) {
      if (signature.length === 0) continue
      
      let matches = true
      for (let i = 0; i < signature.length; i++) {
        if (buffer[i] !== signature[i]) {
          matches = false
          break
        }
      }
      
      if (matches) {
        return mimeType
      }
    }
    
    return null
  }

  /**
   * Check if two MIME types are compatible
   */
  private static areCompatibleTypes(declared: string, detected: string): boolean {
    const compatibilityMap: Record<string, string[]> = {
      'text/plain': ['application/octet-stream'],
      'application/octet-stream': ['text/plain']
    }

    return compatibilityMap[declared]?.includes(detected) || false
  }

  /**
   * Get recommended file configuration for use case
   */
  static getRecommendedConfig(useCase: keyof typeof FILE_CONFIGS): FileSecurityOptions {
    const config = FILE_CONFIGS[useCase]
    
    return {
      allowedTypes: [...config.allowedTypes],
      maxSize: config.maxSize,
      allowedExtensions: [...config.allowedExtensions],
      checkMimeType: true,
      scanContent: true,
      quarantineOnSuspicion: true
    }
  }

  /**
   * Batch file validation
   */
  static async validateFiles(
    files: FileList | File[], 
    options: FileSecurityOptions = {}
  ): Promise<{
    isValid: boolean
    results: FileSecurityResult[]
    summary: {
      total: number
      valid: number
      invalid: number
      warnings: number
    }
  }> {
    const results: FileSecurityResult[] = []
    let valid = 0
    let invalid = 0
    let warnings = 0

    for (const file of Array.from(files)) {
      const result = await this.validateFile(file, options)
      results.push(result)
      
      if (result.isValid) {
        valid++
      } else {
        invalid++
      }
      
      if (result.warnings.length > 0) {
        warnings++
      }
    }

    return {
      isValid: invalid === 0,
      results,
      summary: {
        total: files.length,
        valid,
        invalid,
        warnings
      }
    }
  }
}

/**
 * React hook for file security validation
 */
export function useFileSecurityValidation() {
  return {
    validateFile: FileSecurityValidator.validateFile,
    validateFiles: FileSecurityValidator.validateFiles,
    getRecommendedConfig: FileSecurityValidator.getRecommendedConfig,
    configs: FILE_CONFIGS
  }
}
