/**
 * API Route Rate Limiting Middleware for Next.js
 * Provides server-side rate limiting for API endpoints
 */

import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { RATE_LIMITS, RateLimitAction } from './rate-limiter'

// In-memory rate limiting store (for development)
// In production, use Redis or database
const rateLimitStore = new Map<string, {
  count: number
  windowStart: number
  requests: number[]
}>()

interface RateLimitOptions {
  action: RateLimitAction
  skipAuthCheck?: boolean
  customLimits?: {
    maxRequests: number
    windowMinutes: number
  }
  keyGenerator?: (req: NextRequest, userId?: string) => string
}

/**
 * Rate limiting middleware for API routes
 */
export function withRateLimit(options: RateLimitOptions) {
  return async function rateLimitMiddleware(
    req: NextRequest,
    handler: (req: NextRequest) => Promise<NextResponse> | NextResponse
  ): Promise<NextResponse> {
    try {
      const { action, skipAuthCheck = false, customLimits, keyGenerator } = options
      
      // Get rate limit configuration
      const limits = customLimits || RATE_LIMITS[action]
      const windowMs = limits.windowMinutes * 60 * 1000
      const now = Date.now()

      // Get user ID if authentication is required
      let userId: string | null = null
      if (!skipAuthCheck) {
        try {
          const supabase = createServerClient(
            process.env.NEXT_PUBLIC_SUPABASE_URL!,
            process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
            {
              cookies: {
                get(name: string) {
                  return req.cookies.get(name)?.value
                },
                set() {}, // Not needed for rate limiting
                remove() {} // Not needed for rate limiting
              }
            }
          )

          const { data: { user } } = await supabase.auth.getUser()
          if (!user) {
            return NextResponse.json(
              { error: 'Authentication required' },
              { status: 401 }
            )
          }
          userId = user.id
        } catch (error) {
          console.error('Auth check failed in rate limiter:', error)
          return NextResponse.json(
            { error: 'Authentication failed' },
            { status: 401 }
          )
        }
      }

      // Generate rate limit key
      const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'
      const rateLimitKey = keyGenerator 
        ? keyGenerator(req, userId || undefined)
        : `${action}:${userId || clientIP}`

      // Check rate limit
      const rateLimitData = rateLimitStore.get(rateLimitKey)
      
      if (!rateLimitData) {
        // First request
        rateLimitStore.set(rateLimitKey, {
          count: 1,
          windowStart: now,
          requests: [now]
        })
      } else {
        // Clean old requests outside window
        const validRequests = rateLimitData.requests.filter(
          timestamp => now - timestamp < windowMs
        )

        // Check if limit exceeded
        if (validRequests.length >= limits.maxRequests) {
          const oldestRequest = Math.min(...validRequests)
          const retryAfter = Math.ceil((oldestRequest + windowMs - now) / 1000)
          
          return NextResponse.json(
            {
              error: 'Rate limit exceeded',
              action,
              retryAfter,
              resetTime: oldestRequest + windowMs
            },
            {
              status: 429,
              headers: {
                'Retry-After': retryAfter.toString(),
                'X-RateLimit-Limit': limits.maxRequests.toString(),
                'X-RateLimit-Remaining': '0',
                'X-RateLimit-Reset': Math.ceil((oldestRequest + windowMs) / 1000).toString()
              }
            }
          )
        }

        // Update rate limit data
        validRequests.push(now)
        rateLimitStore.set(rateLimitKey, {
          count: validRequests.length,
          windowStart: rateLimitData.windowStart,
          requests: validRequests
        })
      }

      // Add rate limit headers to response
      const currentData = rateLimitStore.get(rateLimitKey)!
      const remaining = Math.max(0, limits.maxRequests - currentData.requests.length)
      const resetTime = Math.ceil((currentData.windowStart + windowMs) / 1000)

      // Execute the handler
      const response = await handler(req)

      // Add rate limit headers
      response.headers.set('X-RateLimit-Limit', limits.maxRequests.toString())
      response.headers.set('X-RateLimit-Remaining', remaining.toString())
      response.headers.set('X-RateLimit-Reset', resetTime.toString())
      response.headers.set('X-RateLimit-Window', limits.windowMinutes.toString())

      return response
    } catch (error) {
      console.error('Rate limiting middleware error:', error)
      // On error, allow the request but log it
      return await handler(req)
    }
  }
}

/**
 * IP-based rate limiting for public endpoints
 */
export function withIPRateLimit(
  maxRequests: number = 100,
  windowMinutes: number = 10
) {
  return withRateLimit({
    action: 'api_general',
    skipAuthCheck: true,
    customLimits: { maxRequests, windowMinutes },
    keyGenerator: (req) => {
      const ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'
      return `ip:${ip}`
    }
  })
}

/**
 * User-based rate limiting for authenticated endpoints
 */
export function withUserRateLimit(action: RateLimitAction) {
  return withRateLimit({
    action,
    skipAuthCheck: false,
    keyGenerator: (req, userId) => `user:${userId}:${action}`
  })
}

/**
 * Combined IP + User rate limiting
 */
export function withCombinedRateLimit(
  action: RateLimitAction,
  ipLimits?: { maxRequests: number; windowMinutes: number }
) {
  return async function combinedRateLimitMiddleware(
    req: NextRequest,
    handler: (req: NextRequest) => Promise<NextResponse> | NextResponse
  ): Promise<NextResponse> {
    // First check IP-based rate limit
    if (ipLimits) {
      const ipRateLimiter = withIPRateLimit(ipLimits.maxRequests, ipLimits.windowMinutes)
      const ipResult = await ipRateLimiter(req, async () => NextResponse.next())
      
      if (ipResult.status === 429) {
        return ipResult
      }
    }

    // Then check user-based rate limit
    const userRateLimiter = withUserRateLimit(action)
    return await userRateLimiter(req, handler)
  }
}

/**
 * Cleanup old rate limit entries (call periodically)
 */
export function cleanupRateLimitStore(): void {
  const now = Date.now()
  const maxAge = 24 * 60 * 60 * 1000 // 24 hours
  
  for (const [key, data] of rateLimitStore.entries()) {
    if (now - data.windowStart > maxAge) {
      rateLimitStore.delete(key)
    }
  }
}

// Cleanup every hour
if (typeof window === 'undefined') {
  setInterval(cleanupRateLimitStore, 60 * 60 * 1000)
}

/**
 * Rate limit response helper
 */
export function createRateLimitResponse(
  action: RateLimitAction,
  retryAfter: number,
  resetTime: number
): NextResponse {
  return NextResponse.json(
    {
      error: 'Rate limit exceeded',
      message: `Too many ${action} requests. Please try again later.`,
      action,
      retryAfter,
      resetTime
    },
    {
      status: 429,
      headers: {
        'Retry-After': retryAfter.toString(),
        'X-RateLimit-Reset': Math.ceil(resetTime / 1000).toString()
      }
    }
  )
}

/**
 * Get rate limit status for debugging
 */
export function getRateLimitStatus(): {
  totalKeys: number
  activeKeys: number
  oldestEntry: number | null
  newestEntry: number | null
} {
  const now = Date.now()
  let oldestEntry: number | null = null
  let newestEntry: number | null = null
  let activeKeys = 0

  for (const [, data] of rateLimitStore.entries()) {
    const age = now - data.windowStart
    if (age < 24 * 60 * 60 * 1000) { // Active within 24 hours
      activeKeys++
    }
    
    if (oldestEntry === null || data.windowStart < oldestEntry) {
      oldestEntry = data.windowStart
    }
    
    if (newestEntry === null || data.windowStart > newestEntry) {
      newestEntry = data.windowStart
    }
  }

  return {
    totalKeys: rateLimitStore.size,
    activeKeys,
    oldestEntry,
    newestEntry
  }
}
