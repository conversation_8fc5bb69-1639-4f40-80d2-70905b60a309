---
type: "feature"
role: "feature_guide"
dependencies:
  - "core/PROJECT_CORE.md"
  - "backend/SUPABASE.md"
  - "frontend/MAIN_APP.md"
related_features:
  - "HASHTAG_SYSTEM.md"
  - "USER_PLANS.md"
admin_integration:
  - "frontend/ADMIN_PANEL.md"
  - "features/CONTEXT_GALLERY_ADMIN.md"
fixes_reference:
  - "fixes/CONTEXT_GALLERY_ISSUES.md"
  - "fixes/COMMON_ISSUES.md"
auto_update_targets:
  - "fixes/CONTEXT_GALLERY_ISSUES.md"
  - "features/CONTEXT_GALLERY_ADMIN.md"
development_history:
  - date: "2025-01-29"
    change: "Build error fixes applied - TypeScript any types, ESLint escapes"
    files_modified: ["context-gallery components", "admin integration"]
    issues_resolved: ["Ekle button functionality", "Template loading issues"]
    admin_integration: "Admin approval workflow documented"
last_updated: "2025-01-29"
---

# PromptFlow Context Gallery System

## Overview
Context Gallery provides pre-built prompt templates and contexts organized by categories for quick reuse and inspiration.

## Database Schema
```sql
-- Context categories
context_categories (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  name text NOT NULL,
  description text,
  icon text,
  color text DEFAULT '#3B82F6',
  sort_order integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
)

-- Context templates
context_templates (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  category_id uuid REFERENCES context_categories(id),
  title text NOT NULL,
  description text,
  content text NOT NULL,
  tags text[],
  author_id uuid REFERENCES auth.users(id),
  is_public boolean DEFAULT false,
  is_featured boolean DEFAULT false,
  usage_count integer DEFAULT 0,
  like_count integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
)

-- User interactions
context_likes (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id uuid REFERENCES auth.users(id),
  template_id uuid REFERENCES context_templates(id),
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, template_id)
)

context_usage (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id uuid REFERENCES auth.users(id),
  template_id uuid REFERENCES context_templates(id),
  used_at timestamptz DEFAULT now()
)
```

## Frontend Implementation
### Components
```typescript
// Gallery interface
<ContextGallery />                // Main gallery view
<ContextCategoryGrid />           // Category navigation
<ContextTemplateCard />           // Template preview
<ContextModal />                  // Detailed view
<ContextSearch />                 // Search and filters

// Integration with main app
<ContextSidebar />                // Sidebar integration
<ContextButton />                 // Quick access button
<EnhancedContextGalleryModal />   // Enhanced modal with animations
```

### Hooks
```typescript
// Data fetching
const { data: categories } = useContextCategories()
const { data: templates } = useContextTemplates(categoryId)
const { data: featured } = useFeaturedContexts()

// User interactions
const likeMutation = useLikeContext()
const usageMutation = useTrackContextUsage()
```

## Plan Integration
### Access Control
```typescript
// Free plan: Limited access
const { hasFeature } = usePlanFeature('context_gallery')
const accessLevel = hasFeature ? 'full' : 'limited'

// Show upgrade prompt for premium templates
if (template.is_premium && !hasFeature) {
  return <UpgradePrompt feature="Full Context Gallery" />
}
```

### Usage Limits
```typescript
// Track context usage in plan limits
await updateUsageStats(userId, {
  context_usage_count: currentUsage + 1
})

// Free plan limitations
const FREE_PLAN_LIMITS = {
  context_views_per_day: 10,
  template_usage_per_month: 5,
  featured_access: false
}
```

## Admin Management (/admin Panel)

### Template Approval Workflow
```typescript
// Admin panel: /admin/contexts
// All user-submitted templates require admin approval before going public

// Template states
type TemplateStatus = 'pending' | 'approved' | 'rejected' | 'draft'

// Admin can manage all templates
const { data: allTemplates } = useAdminContexts({
  status: 'all', // pending, approved, rejected
  featured: true,
  public: true
})

// Approval workflow
await approveContext(templateId, {
  status: 'approved',
  is_public: true,
  is_featured: true,
  approval_notes: 'High quality template',
  approved_by: adminUserId,
  approved_at: new Date().toISOString()
})

// Rejection workflow
await rejectContext(templateId, {
  status: 'rejected',
  rejection_reason: 'Content quality issues',
  rejected_by: adminUserId,
  rejected_at: new Date().toISOString()
})
```

### Admin Panel Features (/admin/contexts)
```typescript
// Context management dashboard
<AdminContextDashboard>
  <PendingApprovals />        // Templates waiting for approval
  <FeaturedManagement />      // Manage featured templates
  <CategoryManagement />      // Add/edit categories
  <UsageAnalytics />          // Usage statistics
  <BulkActions />             // Bulk approve/reject
</AdminContextDashboard>

// Admin-only operations
const adminOperations = {
  // Bulk approve templates
  bulkApprove: async (templateIds: string[]) => {
    return await supabase
      .from('context_templates')
      .update({
        status: 'approved',
        is_public: true,
        approved_by: adminId,
        approved_at: new Date().toISOString()
      })
      .in('id', templateIds)
  },

  // Set featured templates
  setFeatured: async (templateId: string, featured: boolean) => {
    return await supabase
      .from('context_templates')
      .update({ is_featured: featured })
      .eq('id', templateId)
  },

  // Admin analytics
  getAdminAnalytics: async () => {
    return await supabase.rpc('get_context_admin_analytics')
  }
}
```

### Analytics & Monitoring
```typescript
// Context usage analytics
const analytics = await getContextAnalytics({
  period: '30d',
  metrics: ['usage', 'likes', 'shares', 'approvals']
})

// Popular templates
const popular = await getPopularTemplates({
  timeframe: 'week',
  limit: 10
})

// Admin-specific analytics
const adminAnalytics = {
  pendingApprovals: await getPendingApprovalsCount(),
  dailySubmissions: await getDailySubmissions(),
  approvalRate: await getApprovalRate(),
  topCategories: await getTopCategories()
}
```

## Categories
### Default Categories
- **Development**: Code snippets, API documentation, debugging prompts
- **Writing**: Content creation, copywriting, blog posts
- **Analysis**: Data analysis, research prompts, report generation
- **Creative**: Design briefs, creative writing, brainstorming
- **Business**: Meeting notes, project planning, strategy
- **Education**: Learning materials, explanations, tutorials

### Category Management
```typescript
// Category CRUD operations
const createCategory = async (category: {
  name: string
  description: string
  icon: string
  color: string
}) => {
  return await supabase
    .from('context_categories')
    .insert(category)
}

// Sort categories
const updateCategoryOrder = async (categoryId: string, newOrder: number) => {
  return await supabase
    .from('context_categories')
    .update({ sort_order: newOrder })
    .eq('id', categoryId)
}
```

## Template Features
### Template Structure
```typescript
interface ContextTemplate {
  id: string
  title: string
  description: string
  content: string
  tags: string[]
  category: ContextCategory
  author: {
    id: string
    name: string
    email: string
  }
  is_public: boolean
  is_featured: boolean
  is_premium: boolean
  usage_count: number
  like_count: number
  created_at: string
  updated_at: string
}
```

### Template Actions
```typescript
// Use template in prompt
const useTemplate = async (templateId: string, projectId: string) => {
  // Track usage
  await trackContextUsage(templateId)

  // Insert into prompt workspace
  const template = await getTemplate(templateId)
  await createPrompt({
    project_id: projectId,
    prompt_text: template.content,
    title: template.title,
    tags: template.tags
  })
}

// Like/unlike template
const toggleLike = async (templateId: string) => {
  const isLiked = await checkIfLiked(templateId)

  if (isLiked) {
    await unlikeTemplate(templateId)
  } else {
    await likeTemplate(templateId)
  }
}
```

## Common Issues & Solutions

### Issue 1: "Ekle" Button Not Working
**Symptoms**: Context Gallery "Ekle" (Add) button doesn't respond
**Root Causes & Solutions**:

```typescript
// ❌ Common cause: Missing event handler
<Button onClick={undefined}>Ekle</Button>

// ✅ Fix: Proper event handler
<Button onClick={() => handleAddContext(template)}>Ekle</Button>

// ❌ Common cause: Missing permission check
const handleAddContext = (template) => {
  // Direct add without checking limits
  addToProject(template)
}

// ✅ Fix: Check user limits and permissions
const handleAddContext = async (template: ContextTemplate) => {
  try {
    // Check if user can add more prompts
    const { data: limits } = await checkUserLimits()
    if (!limits.can_create_prompt) {
      toast.error('Plan limitinize ulaştınız')
      return
    }

    // Check if template is accessible
    if (template.is_premium && !userPlan.has_premium_access) {
      showUpgradeModal()
      return
    }

    // Add to current project
    await addContextToProject(template, currentProjectId)
    toast.success('Context başarıyla eklendi')

    // Track usage
    await trackContextUsage(template.id)

  } catch (error) {
    console.error('Context add error:', error)
    toast.error('Context eklenirken hata oluştu')
  }
}
```

### Issue 2: Templates Not Loading
**Symptoms**: Context Gallery shows empty or loading state
**Solutions**:

```typescript
// ❌ Missing error handling
const { data: templates } = useContextTemplates(categoryId)

// ✅ Proper error handling and loading states
const {
  data: templates,
  isLoading,
  error,
  refetch
} = useContextTemplates(categoryId)

if (isLoading) return <ContextSkeleton />
if (error) return <ErrorState onRetry={refetch} />
if (!templates?.length) return <EmptyState />

// ❌ Missing RLS policy check
// User can't see templates due to RLS

// ✅ Verify RLS policies
-- Enable public templates for all users
CREATE POLICY "Public templates visible to all" ON context_templates
  FOR SELECT USING (is_public = true AND status = 'approved');

-- User can see their own templates
CREATE POLICY "Users see own templates" ON context_templates
  FOR SELECT USING (auth.uid() = author_id);
```

### Issue 3: Admin Approval Not Working
**Symptoms**: Templates remain in pending state after admin approval
**Solutions**:

```typescript
// ❌ Missing admin permission check
const approveTemplate = async (templateId: string) => {
  await supabase
    .from('context_templates')
    .update({ status: 'approved' })
    .eq('id', templateId)
}

// ✅ Proper admin approval with permission check
const approveTemplate = async (templateId: string) => {
  // Check admin permissions
  const { data: isAdmin } = await checkIsAdmin()
  if (!isAdmin) {
    throw new Error('Admin yetkisi gerekli')
  }

  // Update template with full approval data
  const { data, error } = await supabase
    .from('context_templates')
    .update({
      status: 'approved',
      is_public: true,
      approved_by: adminUserId,
      approved_at: new Date().toISOString()
    })
    .eq('id', templateId)
    .select()

  if (error) throw error

  // Notify template author
  await notifyAuthor(templateId, 'approved')

  return data
}
```

### Issue 4: Search Not Working
**Symptoms**: Context search returns no results
**Solutions**:

```typescript
// ❌ Case-sensitive search
const searchTemplates = (query: string) => {
  return templates.filter(t => t.title.includes(query))
}

// ✅ Proper search implementation
const searchTemplates = (query: string) => {
  const searchTerm = query.toLowerCase().trim()

  return templates.filter(template =>
    template.title.toLowerCase().includes(searchTerm) ||
    template.description.toLowerCase().includes(searchTerm) ||
    template.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
    template.content.toLowerCase().includes(searchTerm)
  )
}

// ✅ Database-level search with full-text search
const searchTemplatesDB = async (query: string) => {
  const { data, error } = await supabase
    .from('context_templates')
    .select('*')
    .textSearch('title,description,content', query)
    .eq('is_public', true)
    .eq('status', 'approved')

  if (error) throw error
  return data
}
```

## Backend Integration

### Database Functions
```sql
-- Get user's accessible templates
CREATE OR REPLACE FUNCTION get_user_accessible_templates(user_uuid uuid)
RETURNS TABLE (
  id uuid,
  title text,
  description text,
  content text,
  tags text[],
  category_name text,
  is_premium boolean,
  usage_count integer,
  like_count integer,
  is_liked boolean
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    ct.id,
    ct.title,
    ct.description,
    ct.content,
    ct.tags,
    cc.name as category_name,
    ct.is_premium,
    ct.usage_count,
    ct.like_count,
    EXISTS(SELECT 1 FROM context_likes cl WHERE cl.user_id = user_uuid AND cl.template_id = ct.id) as is_liked
  FROM context_templates ct
  JOIN context_categories cc ON ct.category_id = cc.id
  WHERE ct.is_public = true
    AND ct.status = 'approved'
    AND cc.is_active = true
  ORDER BY ct.is_featured DESC, ct.usage_count DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Admin analytics function
CREATE OR REPLACE FUNCTION get_context_admin_analytics()
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_templates', (SELECT COUNT(*) FROM context_templates),
    'pending_approvals', (SELECT COUNT(*) FROM context_templates WHERE status = 'pending'),
    'approved_templates', (SELECT COUNT(*) FROM context_templates WHERE status = 'approved'),
    'featured_templates', (SELECT COUNT(*) FROM context_templates WHERE is_featured = true),
    'total_usage', (SELECT SUM(usage_count) FROM context_templates),
    'total_likes', (SELECT SUM(like_count) FROM context_templates),
    'categories_count', (SELECT COUNT(*) FROM context_categories WHERE is_active = true),
    'daily_submissions', (
      SELECT COUNT(*)
      FROM context_templates
      WHERE created_at >= CURRENT_DATE
    )
  ) INTO result;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### RLS Policies
```sql
-- Context templates policies
CREATE POLICY "Public approved templates visible to all" ON context_templates
  FOR SELECT USING (is_public = true AND status = 'approved');

CREATE POLICY "Users can view own templates" ON context_templates
  FOR SELECT USING (auth.uid() = author_id);

CREATE POLICY "Users can create templates" ON context_templates
  FOR INSERT WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Users can update own templates" ON context_templates
  FOR UPDATE USING (auth.uid() = author_id);

-- Admin policies
CREATE POLICY "Admins can manage all templates" ON context_templates
  FOR ALL USING (
    EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
  );

-- Context categories policies
CREATE POLICY "Active categories visible to all" ON context_categories
  FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage categories" ON context_categories
  FOR ALL USING (
    EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
  );
```

## Development Guidelines

### Adding New Context Features
1. **Database Changes**: Update schema in migration files
2. **Backend Functions**: Add/update Supabase functions
3. **Frontend Hooks**: Create/update React Query hooks
4. **Admin Interface**: Update admin panel if needed
5. **RLS Policies**: Ensure proper security policies
6. **Testing**: Test both user and admin workflows

### Context Gallery Development Checklist
- [ ] Database schema updated
- [ ] RLS policies configured
- [ ] Frontend components implemented
- [ ] Admin panel integration added
- [ ] Error handling implemented
- [ ] Loading states added
- [ ] Plan integration checked
- [ ] Usage tracking implemented
- [ ] Search functionality working
- [ ] Mobile responsiveness verified

## Automatic System Integration

### Auto-Update Targets
When this file is modified, the following files are automatically updated:
- **CONTEXT_GALLERY_ADMIN.md**: Admin panel integration details
- **CONTEXT_GALLERY_ISSUES.md**: New error patterns and solutions

### Development History Tracking
```yaml
recent_changes:
  - date: "2025-01-29"
    change: "Text area feature example"
    description: "User input text area added to Context Gallery"
    admin_integration: "Text area content visible in /admin/contexts"
    files_modified: ["context-gallery-modal.tsx", "admin/contexts/page.tsx"]
    database_changes: ["Added user_input column to context_templates"]
```

### Related Documentation
- **CONTEXT_GALLERY_ADMIN.md**: Admin panel development guide (auto-updated)
- **CONTEXT_GALLERY_ISSUES.md**: Troubleshooting guide (auto-updated)
- **COMMON_ISSUES.md**: General project issues including Context Gallery fixes

## Update Requirements
- This file auto-updates when Context Gallery features change
- Development history is automatically tracked
- Related files receive automatic updates via the dependency system
- Admin integration documentation is automatically synchronized
