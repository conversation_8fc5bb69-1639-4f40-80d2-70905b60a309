---
type: "feature"
role: "plans_guide"
dependencies:
  - "core/PROJECT_CORE.md"
  - "backend/SUPABASE.md"
  - "frontend/MAIN_APP.md"
related_features:
  - "features/CONTEXT_GALLERY.md"
  - "features/HASHTAG_SYSTEM.md"
admin_integration:
  - "frontend/ADMIN_PANEL.md"
auto_update_targets:
  - "features/CONTEXT_GALLERY.md"
development_history:
  - date: "2025-01-29"
    change: "Plan integration with Context Gallery documented"
    integration_points: ["Premium templates", "Usage limits", "Feature access"]
last_updated: "2025-01-29"
---

# PromptFlow User Plans System

## Plan Types
### Free Plan
- **Limits**: 5 projects, 100 prompts/project
- **Features**: Basic features, Limited Context Gallery, Email support
- **Price**: ₺0

### Professional Plan
- **Limits**: 50 projects, 5000 prompts/project
- **Features**: Full Context Gallery, API access, Team features, Priority support
- **Price**: ₺99/month or ₺990/year (17% discount)

### Enterprise Plan
- **Limits**: Unlimited projects and prompts
- **Features**: All Professional + SSO, Custom deployment, 24/7 support
- **Price**: Custom pricing

## Implementation
### Frontend Components
```typescript
// Plan display and management
<PlanDisplay />                    // Current plan status
<PlanUpgradeModal />              // Upgrade interface
<LimitWarning />                  // Limit notifications
<UsageStats />                    // Usage analytics

// Limit checks in UI
const { data: limits } = useUserLimits()
const canCreate = limits?.can_create_project

<Button disabled={!canCreate}>
  Create Project
</Button>

// Upgrade prompts
{!canCreate && (
  <InlineLimitWarning 
    message="Project limit reached"
    upgradeFeature="More Projects"
  />
)}
```

### Backend Integration
```typescript
// Limit checking before operations
const limitCheck = await canCreateProject()
if (!limitCheck.allowed) {
  throw new Error(limitCheck.reason)
}

// Usage tracking
await updateUsageStats(userId)

// Plan feature checks
const hasFeature = await checkPlanFeature(userId, 'api_access')
```

### Database Schema
```sql
-- Plan definitions
plan_types (
  id, name, display_name, description,
  price_monthly, price_yearly,
  max_projects, max_prompts_per_project,
  features jsonb
)

-- User plan assignments
user_plans (
  id, user_id, plan_type_id, status,
  billing_cycle, started_at, expires_at
)

-- Usage tracking
usage_stats (
  id, user_id, stat_date,
  projects_count, prompts_count,
  last_activity_at
)
```

## Limit Enforcement
### Project Creation
```typescript
// Check before creating
const { data: limits } = await supabase.rpc('check_user_limits', {
  user_uuid: userId
})

if (!limits.can_create_project) {
  throw new Error('Project limit reached')
}
```

### Prompt Creation
```typescript
// Check per-project limits
const promptCount = await getProjectPromptCount(projectId)
if (promptCount >= userPlan.max_prompts_per_project) {
  throw new Error('Prompt limit reached for this project')
}
```

## Admin Management
### Plan Assignment
```typescript
// Admin can change user plans
await supabase.rpc('change_user_plan', {
  user_uuid: userId,
  new_plan_name: 'professional',
  billing_cycle_param: 'yearly'
})

// Bulk plan operations
const bulkChangePlan = async (userIds: string[], planName: string) => {
  for (const userId of userIds) {
    await changePlan(userId, planName)
  }
}
```

### Analytics
```typescript
// Plan statistics for admin dashboard
const stats = await supabase.rpc('get_plan_statistics')
// Returns: plan distribution, revenue, usage metrics

// Revenue tracking
const revenue = await supabase
  .from('plan_transactions')
  .select('amount, currency, created_at')
  .gte('created_at', startDate)
  .lte('created_at', endDate)
```

## Hooks & Utilities
### Custom Hooks
```typescript
// Plan management hooks
const { data: activePlan } = useUserActivePlan()
const { data: limits } = useUserLimits()
const { data: planTypes } = usePlanTypes()
const changePlan = useChangePlan()

// Usage tracking
const { data: usage } = useUsageStats()
const updateUsage = useUpdateUsageStats()
```

### Utility Functions
```typescript
// Plan limit utilities
export const canCreateProject = async (userId: string): Promise<boolean> => {
  const limits = await checkUserLimits(userId)
  return limits.can_create_project
}

export const canCreatePrompt = async (projectId: string): Promise<boolean> => {
  const count = await getProjectPromptCount(projectId)
  const userPlan = await getUserActivePlan(userId)
  return count < userPlan.max_prompts_per_project
}

// Feature access
export const hasPlanFeature = async (userId: string, feature: string): Promise<boolean> => {
  const plan = await getUserActivePlan(userId)
  return plan.features[feature] === true
}
```

## Plan Upgrade Flow
### Frontend Flow
1. User hits limit → Show limit warning
2. Click upgrade → Open plan comparison modal
3. Select plan & billing cycle → Confirm upgrade
4. Process payment → Update plan status
5. Show success message → Refresh limits

### Backend Flow
1. Validate plan change request
2. Create plan transaction record
3. Update user_plans table
4. Refresh usage statistics
5. Send confirmation email

## Error Handling
```typescript
// Plan limit errors
export const PLAN_LIMIT_MESSAGES = {
  PROJECT_LIMIT: 'You have reached your project limit. Upgrade to create more projects.',
  PROMPT_LIMIT: 'You have reached the prompt limit for this project. Upgrade for more prompts.',
  FEATURE_LOCKED: 'This feature is not available in your current plan. Upgrade to unlock it.'
}

// Error boundaries for plan-related errors
<PlanErrorBoundary>
  <FeatureComponent />
</PlanErrorBoundary>
```

## Update Requirements
- Update this file when modifying plan features
- Document new limit types
- Maintain pricing information
- Keep admin tools current
- Update hooks when adding new plan features
- Document new upgrade flows
