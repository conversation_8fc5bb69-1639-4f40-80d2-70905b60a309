'use client';

import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  Icon,
  Card,
  CardHeader,
  CardBody,
  useToast,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Switch,
  Select,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Divider,
  Badge,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  useDisclosure,
  Flex,
  Spacer,
  Code,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  IconButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
} from '@chakra-ui/react';
import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { AdminLayout } from '@/components/admin-layout';
import { 
  ArrowLeft,
  Settings,
  Database,
  Shield,
  Mail,
  Bell,
  Palette,
  Server,
  Key,
  Users,
  Activity,
  Save,
  RefreshCw,
  Trash2,
  Edit,
  Plus,
  MoreVertical,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';

interface SystemSetting {
  id: string;
  key: string;
  value: string | number | boolean;
  description: string | null;
  created_at: string;
  updated_at: string;
}

interface SystemStats {
  totalSettings: number;
  lastBackup: string | null;
  systemHealth: 'healthy' | 'warning' | 'error';
  uptime: string;
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<SystemSetting[]>([]);
  const [stats, setStats] = useState<SystemStats>({
    totalSettings: 0,
    lastBackup: null,
    systemHealth: 'healthy',
    uptime: '99.9%',
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [editingSetting, setEditingSetting] = useState<SystemSetting | null>(null);
  const [newSettingKey, setNewSettingKey] = useState('');
  const [newSettingValue, setNewSettingValue] = useState('');
  const [newSettingDescription, setNewSettingDescription] = useState('');
  
  const router = useRouter();
  const toast = useToast();
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
  const { isOpen: isAddOpen, onOpen: onAddOpen, onClose: onAddClose } = useDisclosure();
  const cancelRef = useRef<HTMLButtonElement>(null);

  // Önceden tanımlanmış ayarlar
  const predefinedSettings = [
    {
      key: 'app_name',
      value: 'PromptFlow',
      description: 'Uygulama adı',
      type: 'text'
    },
    {
      key: 'app_description',
      value: 'AI Prompt Yönetim Sistemi',
      description: 'Uygulama açıklaması',
      type: 'text'
    },
    {
      key: 'max_projects_per_user',
      value: 10,
      description: 'Kullanıcı başına maksimum proje sayısı',
      type: 'number'
    },
    {
      key: 'max_prompts_per_project',
      value: 100,
      description: 'Proje başına maksimum prompt sayısı',
      type: 'number'
    },
    {
      key: 'enable_user_registration',
      value: true,
      description: 'Kullanıcı kaydına izin ver',
      type: 'boolean'
    },
    {
      key: 'enable_public_projects',
      value: true,
      description: 'Genel projelere izin ver',
      type: 'boolean'
    },
    {
      key: 'email_notifications',
      value: true,
      description: 'Email bildirimlerini etkinleştir',
      type: 'boolean'
    },
    {
      key: 'maintenance_mode',
      value: false,
      description: 'Bakım modu',
      type: 'boolean'
    },
    {
      key: 'backup_frequency',
      value: 'daily',
      description: 'Yedekleme sıklığı',
      type: 'select',
      options: ['hourly', 'daily', 'weekly', 'monthly']
    },
    {
      key: 'log_level',
      value: 'info',
      description: 'Log seviyesi',
      type: 'select',
      options: ['debug', 'info', 'warning', 'error']
    }
  ];

  useEffect(() => {
    fetchSettings();
    fetchStats();
    initializeDefaultSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('system_settings')
        .select('*')
        .order('key');

      if (error) {
        throw error;
      }

      setSettings(data || []);
    } catch (error) {
      console.error('Settings fetch error:', error);
      toast({
        title: 'Hata',
        description: 'Ayarlar yüklenirken bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const { count: totalSettings } = await supabase
        .from('system_settings')
        .select('*', { count: 'exact', head: true });

      // Örnek istatistikler
      setStats({
        totalSettings: totalSettings || 0,
        lastBackup: new Date().toISOString(),
        systemHealth: 'healthy',
        uptime: '99.9%',
      });
    } catch (error) {
      console.error('Stats fetch error:', error);
    }
  };

  const initializeDefaultSettings = async () => {
    try {
      // Mevcut ayarları kontrol et
      const { data: existingSettings } = await supabase
        .from('system_settings')
        .select('key');

      const existingKeys = existingSettings?.map(s => s.key) || [];

      // Eksik ayarları ekle
      const missingSettings = predefinedSettings.filter(
        setting => !existingKeys.includes(setting.key)
      );

      if (missingSettings.length > 0) {
        const { error } = await supabase
          .from('system_settings')
          .insert(
            missingSettings.map(setting => ({
              key: setting.key,
              value: setting.value,
              description: setting.description,
            }))
          );

        if (error) {
          console.error('Default settings initialization error:', error);
        }
      }
    } catch (error) {
      console.error('Initialize default settings error:', error);
    }
  };

  const handleSaveSetting = async (setting: SystemSetting) => {
    setIsSaving(true);
    try {
      const { error } = await supabase
        .from('system_settings')
        .update({
          value: setting.value,
          description: setting.description,
        })
        .eq('id', setting.id);

      if (error) {
        throw error;
      }

      toast({
        title: 'Başarılı',
        description: 'Ayar başarıyla güncellendi.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      fetchSettings();
    } catch (error) {
      console.error('Save setting error:', error);
      toast({
        title: 'Hata',
        description: 'Ayar güncellenirken bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteSetting = async () => {
    if (!editingSetting) return;

    try {
      const { error } = await supabase
        .from('system_settings')
        .delete()
        .eq('id', editingSetting.id);

      if (error) {
        throw error;
      }

      toast({
        title: 'Başarılı',
        description: 'Ayar başarıyla silindi.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      fetchSettings();
      fetchStats();
      onDeleteClose();
    } catch (error) {
      console.error('Delete setting error:', error);
      toast({
        title: 'Hata',
        description: 'Ayar silinirken bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleAddSetting = async () => {
    if (!newSettingKey.trim()) return;

    try {
      let parsedValue: string | number | boolean = newSettingValue;
      
      // Değer tipini otomatik tespit et
      if (newSettingValue === 'true' || newSettingValue === 'false') {
        parsedValue = newSettingValue === 'true';
      } else if (!isNaN(Number(newSettingValue))) {
        parsedValue = Number(newSettingValue);
      }

      const { error } = await supabase
        .from('system_settings')
        .insert({
          key: newSettingKey,
          value: parsedValue,
          description: newSettingDescription || null,
        });

      if (error) {
        throw error;
      }

      toast({
        title: 'Başarılı',
        description: 'Yeni ayar başarıyla eklendi.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      fetchSettings();
      fetchStats();
      onAddClose();
      setNewSettingKey('');
      setNewSettingValue('');
      setNewSettingDescription('');
    } catch (error) {
      console.error('Add setting error:', error);
      toast({
        title: 'Hata',
        description: 'Ayar eklenirken bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const renderSettingValue = (setting: SystemSetting) => {
    const predefinedSetting = predefinedSettings.find(p => p.key === setting.key);
    
    if (predefinedSetting?.type === 'boolean') {
      return (
        <Switch
          isChecked={setting.value}
          onChange={(e) => {
            const updatedSetting = { ...setting, value: e.target.checked };
            handleSaveSetting(updatedSetting);
          }}
        />
      );
    }

    if (predefinedSetting?.type === 'number') {
      return (
        <NumberInput
          value={setting.value}
          onChange={(value) => {
            const updatedSetting = { ...setting, value: Number(value) };
            handleSaveSetting(updatedSetting);
          }}
          maxW="200px"
        >
          <NumberInputField />
          <NumberInputStepper>
            <NumberIncrementStepper />
            <NumberDecrementStepper />
          </NumberInputStepper>
        </NumberInput>
      );
    }

    if (predefinedSetting?.type === 'select') {
      return (
        <Select
          value={setting.value}
          onChange={(e) => {
            const updatedSetting = { ...setting, value: e.target.value };
            handleSaveSetting(updatedSetting);
          }}
          maxW="200px"
        >
          {predefinedSetting.options?.map(option => (
            <option key={option} value={option}>{option}</option>
          ))}
        </Select>
      );
    }

    return (
      <Input
        value={setting.value}
        onChange={(e) => {
          const updatedSetting = { ...setting, value: e.target.value };
          handleSaveSetting(updatedSetting);
        }}
        onBlur={() => handleSaveSetting(setting)}
        maxW="300px"
      />
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'green';
      case 'warning': return 'yellow';
      case 'error': return 'red';
      default: return 'gray';
    }
  };

  const getHealthIcon = (health: string) => {
    switch (health) {
      case 'healthy': return CheckCircle;
      case 'warning': return AlertTriangle;
      case 'error': return AlertTriangle;
      default: return Info;
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <Box minH="100vh" bg="gray.50" display="flex" alignItems="center" justifyContent="center">
          <Text>Yükleniyor...</Text>
        </Box>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Container maxW="7xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <Flex align="center" justify="space-between">
            <VStack align="start" spacing={0}>
              <Heading size="lg">Sistem Ayarları</Heading>
              <Text color="gray.600">Sistem konfigürasyonunu yönetin</Text>
            </VStack>
            <Button
              leftIcon={<Icon as={Plus} />}
              colorScheme="blue"
              onClick={onAddOpen}
            >
              Ayar Ekle
            </Button>
          </Flex>

          {/* System Stats */}
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={Settings} w={4} h={4} />
                      <Text>Toplam Ayar</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber color="blue.600">{stats.totalSettings}</StatNumber>
                </Stat>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={getHealthIcon(stats.systemHealth)} w={4} h={4} />
                      <Text>Sistem Durumu</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber>
                    <Badge colorScheme={getHealthColor(stats.systemHealth)}>
                      {stats.systemHealth === 'healthy' ? 'Sağlıklı' : 
                       stats.systemHealth === 'warning' ? 'Uyarı' : 'Hata'}
                    </Badge>
                  </StatNumber>
                </Stat>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={Activity} w={4} h={4} />
                      <Text>Uptime</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber color="green.600">{stats.uptime}</StatNumber>
                </Stat>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={Database} w={4} h={4} />
                      <Text>Son Yedek</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber fontSize="sm" color="purple.600">
                    {stats.lastBackup ? formatDate(stats.lastBackup) : 'Hiç'}
                  </StatNumber>
                </Stat>
              </CardBody>
            </Card>
          </SimpleGrid>

          {/* Settings List */}
          <Card>
            <CardHeader>
              <Flex align="center" justify="space-between">
                <HStack>
                  <Icon as={Settings} w={5} h={5} color="blue.500" />
                  <Heading size="md">Sistem Ayarları</Heading>
                </HStack>
                <Button
                  leftIcon={<Icon as={RefreshCw} />}
                  variant="outline"
                  size="sm"
                  onClick={fetchSettings}
                  isLoading={isLoading}
                >
                  Yenile
                </Button>
              </Flex>
            </CardHeader>
            <CardBody>
              <TableContainer>
                <Table variant="simple">
                  <Thead>
                    <Tr>
                      <Th>Anahtar</Th>
                      <Th>Değer</Th>
                      <Th>Açıklama</Th>
                      <Th>Son Güncelleme</Th>
                      <Th>İşlemler</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {settings.map((setting) => (
                      <Tr key={setting.id}>
                        <Td>
                          <Code colorScheme="blue">{setting.key}</Code>
                        </Td>
                        <Td>
                          {renderSettingValue(setting)}
                        </Td>
                        <Td>
                          <Text fontSize="sm" color="gray.600">
                            {setting.description || 'Açıklama yok'}
                          </Text>
                        </Td>
                        <Td>
                          <Text fontSize="sm" color="gray.600">
                            {formatDate(setting.updated_at)}
                          </Text>
                        </Td>
                        <Td>
                          <Menu>
                            <MenuButton
                              as={IconButton}
                              icon={<Icon as={MoreVertical} />}
                              variant="ghost"
                              size="sm"
                            />
                            <MenuList>
                              <MenuItem
                                icon={<Icon as={Edit} />}
                                onClick={() => {
                                  // Edit functionality can be added here
                                }}
                              >
                                Düzenle
                              </MenuItem>
                              <MenuItem
                                icon={<Icon as={Trash2} />}
                                color="red.500"
                                onClick={() => {
                                  setEditingSetting(setting);
                                  onDeleteOpen();
                                }}
                              >
                                Sil
                              </MenuItem>
                            </MenuList>
                          </Menu>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </TableContainer>
            </CardBody>
          </Card>

          {/* System Actions */}
          <Card>
            <CardHeader>
              <HStack>
                <Icon as={Server} w={5} h={5} color="purple.500" />
                <Heading size="md">Sistem İşlemleri</Heading>
              </HStack>
            </CardHeader>
            <CardBody>
              <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
                <Button
                  leftIcon={<Icon as={Database} />}
                  variant="outline"
                  onClick={() => {
                    toast({
                      title: 'Yedekleme Başlatıldı',
                      description: 'Sistem yedeklemesi başlatılıyor...',
                      status: 'info',
                      duration: 3000,
                      isClosable: true,
                    });
                  }}
                >
                  Yedek Al
                </Button>
                <Button
                  leftIcon={<Icon as={RefreshCw} />}
                  variant="outline"
                  onClick={() => {
                    toast({
                      title: 'Önbellek Temizlendi',
                      description: 'Sistem önbelleği temizlendi.',
                      status: 'success',
                      duration: 3000,
                      isClosable: true,
                    });
                  }}
                >
                  Önbellek Temizle
                </Button>
                <Button
                  leftIcon={<Icon as={Activity} />}
                  variant="outline"
                  onClick={() => {
                    toast({
                      title: 'Sistem Durumu',
                      description: 'Tüm servisler çalışıyor.',
                      status: 'success',
                      duration: 3000,
                      isClosable: true,
                    });
                  }}
                >
                  Sistem Kontrolü
                </Button>
                <Button
                  leftIcon={<Icon as={Shield} />}
                  variant="outline"
                  onClick={() => {
                    toast({
                      title: 'Güvenlik Taraması',
                      description: 'Güvenlik taraması başlatılıyor...',
                      status: 'info',
                      duration: 3000,
                      isClosable: true,
                    });
                  }}
                >
                  Güvenlik Tarama
                </Button>
              </SimpleGrid>
            </CardBody>
          </Card>
        </VStack>
      </Container>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        isOpen={isDeleteOpen}
        leastDestructiveRef={cancelRef}
        onClose={onDeleteClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              Ayarı Sil
            </AlertDialogHeader>
            <AlertDialogBody>
              <Text>
                <strong>{editingSetting?.key}</strong> ayarını silmek istediğinizden emin misiniz?
              </Text>
              <Text color="red.500" fontSize="sm" mt={2}>
                Bu işlem geri alınamaz ve sistem davranışını etkileyebilir.
              </Text>
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onDeleteClose}>
                İptal
              </Button>
              <Button colorScheme="red" onClick={handleDeleteSetting} ml={3}>
                Sil
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>

      {/* Add Setting Dialog */}
      <AlertDialog
        isOpen={isAddOpen}
        leastDestructiveRef={cancelRef}
        onClose={onAddClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              Yeni Ayar Ekle
            </AlertDialogHeader>
            <AlertDialogBody>
              <VStack spacing={4}>
                <FormControl>
                  <FormLabel>Anahtar</FormLabel>
                  <Input
                    placeholder="setting_key"
                    value={newSettingKey}
                    onChange={(e) => setNewSettingKey(e.target.value)}
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>Değer</FormLabel>
                  <Input
                    placeholder="setting_value"
                    value={newSettingValue}
                    onChange={(e) => setNewSettingValue(e.target.value)}
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>Açıklama</FormLabel>
                  <Textarea
                    placeholder="Ayar açıklaması..."
                    value={newSettingDescription}
                    onChange={(e) => setNewSettingDescription(e.target.value)}
                  />
                </FormControl>
              </VStack>
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onAddClose}>
                İptal
              </Button>
              <Button colorScheme="blue" onClick={handleAddSetting} ml={3}>
                Ekle
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </AdminLayout>
  );
}