import { supabase } from './supabase';

// Optimized dashboard API functions
export const dashboardApi = {
  // Consolidated dashboard stats query - using secure API route
  async getStats() {
    try {
      const response = await fetch('/api/admin/dashboard/stats', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        const errorMessage = errorData.error || `HTTP ${response.status}: ${response.statusText}`;

        // Throw specific error messages for auth issues
        if (response.status === 401) {
          throw new Error('Unauthorized - Please login again');
        }
        if (response.status === 403) {
          throw new Error('Admin access required');
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      return typeof data === 'string' ? JSON.parse(data) : data;
    } catch (error) {
      console.error('Dashboard stats error:', error);
      // Re-throw the original error to preserve error messages
      throw error instanceof Error ? error : new Error('Failed to fetch dashboard stats');
    }
  },

  // Paginated recent activity
  async getRecentActivity(limit = 10, offset = 0) {
    const { data, error } = await supabase
      .from('admin_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw new Error('Failed to fetch recent activity');
    return data;
  },

  // Top projects with limited fields
  async getTopProjects(limit = 5) {
    const { data, error } = await supabase
      .from('projects')
      .select('id, name, created_at, status')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw new Error('Failed to fetch top projects');
    return data;
  },
};

// Optimized users API functions
export const usersApi = {
  // Paginated users list with search and filters - using secure API route
  async getUsers(options: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
    status?: string;
  } = {}) {
    try {
      const params = new URLSearchParams();
      if (options.page) params.append('page', options.page.toString());
      if (options.limit) params.append('limit', options.limit.toString());
      if (options.search) params.append('search', options.search);
      if (options.role) params.append('role', options.role);
      if (options.status) params.append('status', options.status);

      const response = await fetch(`/api/admin/users?${params.toString()}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch users');
      }

      return await response.json();
    } catch (error) {
      console.error('Users fetch error:', error);
      throw new Error('Failed to fetch users');
    }
  },

  // User stats - using secure API route
  async getUserStats() {
    try {
      const response = await fetch('/api/admin/users/stats', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch user stats');
      }

      const data = await response.json();
      return typeof data === 'string' ? JSON.parse(data) : data;
    } catch (error) {
      console.error('User stats error:', error);
      throw new Error('Failed to fetch user stats');
    }
  },

  // Single user detail
  async getUser(id: string) {
    const { data, error } = await supabase
      .from('admin_users_view')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw new Error('Failed to fetch user');
    return data;
  },

  // Create user
  async createUser(userData: any) {
    const { data, error } = await supabase
      .from('admin_users')
      .insert(userData)
      .select()
      .single();

    if (error) throw new Error('Failed to create user');
    return data;
  },

  // Update user
  async updateUser(id: string, userData: any) {
    const { data, error } = await supabase
      .from('admin_users')
      .update(userData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw new Error('Failed to update user');
    return data;
  },

  // Delete user
  async deleteUser(id: string) {
    const { error } = await supabase
      .from('admin_users')
      .delete()
      .eq('id', id);

    if (error) throw new Error('Failed to delete user');
    return true;
  },
};

// Optimized projects API functions
export const projectsApi = {
  // Paginated projects with search
  async getProjects(options: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
  } = {}) {
    const { page = 1, limit = 20, search, status } = options;
    const offset = (page - 1) * limit;

    let query = supabase
      .from('projects')
      .select('id, name, created_at, status, user_id, context_text', { count: 'exact' });

    if (search) {
      query = query.ilike('name', `%${search}%`);
    }
    if (status) {
      query = query.eq('status', status);
    }

    const { data, error, count } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw new Error('Failed to fetch projects');
    
    return {
      projects: data || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit),
    };
  },
};

// Optimized prompts API functions
export const promptsApi = {
  // Paginated prompts with search
  async getPrompts(options: {
    page?: number;
    limit?: number;
    search?: string;
    projectId?: string;
  } = {}) {
    const { page = 1, limit = 20, search, projectId } = options;
    const offset = (page - 1) * limit;

    let query = supabase
      .from('prompts')
      .select('id, prompt_text, project_id, is_used, order_index, created_at, updated_at, user_id', { count: 'exact' });

    if (search) {
      query = query.ilike('prompt_text', `%${search}%`);
    }
    if (projectId) {
      query = query.eq('project_id', projectId);
    }

    const { data, error, count } = await query
      .order('order_index', { ascending: true })
      .range(offset, offset + limit - 1);

    if (error) throw new Error('Failed to fetch prompts');

    return {
      prompts: data || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit),
    };
  },

  // Single prompt detail
  async getPrompt(id: string) {
    const { data, error } = await supabase
      .from('prompts')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw new Error('Failed to fetch prompt');
    return data;
  },

  // Create prompt
  async createPrompt(promptData: any) {
    const { data, error } = await supabase
      .from('prompts')
      .insert(promptData)
      .select()
      .single();

    if (error) throw new Error('Failed to create prompt');
    return data;
  },

  // Update prompt
  async updatePrompt(id: string, promptData: any) {
    const { data, error } = await supabase
      .from('prompts')
      .update(promptData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw new Error('Failed to update prompt');
    return data;
  },

  // Delete prompt
  async deletePrompt(id: string) {
    const { error } = await supabase
      .from('prompts')
      .delete()
      .eq('id', id);

    if (error) throw new Error('Failed to delete prompt');
    return true;
  },

  // Bulk update prompts (for drag & drop reordering)
  async bulkUpdatePrompts(updates: Array<{ id: string; order_index?: number; is_used?: boolean }>) {
    const { data, error } = await supabase
      .from('prompts')
      .upsert(updates, { onConflict: 'id' })
      .select();

    if (error) throw new Error('Failed to bulk update prompts');
    return data;
  },

  // Bulk mark as used
  async bulkMarkAsUsed(promptIds: string[]) {
    const { data, error } = await supabase
      .from('prompts')
      .update({ is_used: true })
      .in('id', promptIds)
      .select();

    if (error) throw new Error('Failed to bulk mark as used');
    return data;
  },
};

// Auth optimization - cached admin check
let adminCheckCache: { userId: string; isAdmin: boolean; timestamp: number } | null = null;
const ADMIN_CHECK_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export const authApi = {
  async checkAdminStatus(userId: string): Promise<boolean> {
    // Check cache first
    if (adminCheckCache &&
        adminCheckCache.userId === userId &&
        Date.now() - adminCheckCache.timestamp < ADMIN_CHECK_CACHE_DURATION) {
      return adminCheckCache.isAdmin;
    }

    const { data, error } = await supabase
      .from('admin_users')
      .select('user_id, is_active')
      .eq('user_id', userId)
      .eq('is_active', true)
      .single();

    const isAdmin = !error && !!data;

    // Update cache
    adminCheckCache = {
      userId,
      isAdmin,
      timestamp: Date.now(),
    };

    return isAdmin;
  },

  // Get admin user details with cache
  async getAdminUser(userId: string) {
    const { data, error } = await supabase
      .from('admin_users_view')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) throw new Error('Failed to fetch admin user');
    return data;
  },

  clearAdminCache() {
    adminCheckCache = null;
  },
};
