import { useQuery } from '@tanstack/react-query';
import { dashboardApi } from '@/lib/api';
import { queryKeys } from '@/lib/query-client';

// Optimized dashboard stats hook
export function useDashboardStats() {
  return useQuery({
    queryKey: queryKeys.dashboard.stats,
    queryFn: dashboardApi.getStats,
    staleTime: 5 * 60 * 1000, // 5 minutes - dashboard stats can be slightly stale
    gcTime: 10 * 60 * 1000, // 10 minutes cache
    refetchOnWindowFocus: false,
    refetchOnMount: false, // Don't refetch on component mount if data exists
    // Enable background refetching for fresh data
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes in background
    retry: (failureCount, error) => {
      // Don't retry on auth errors
      if (error instanceof Error && (
        error.message.includes('401') ||
        error.message.includes('403') ||
        error.message.includes('Admin access required') ||
        error.message.includes('Unauthorized')
      )) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

// Recent activity hook with pagination
export function useRecentActivity(limit = 10, offset = 0) {
  return useQuery({
    queryKey: [...queryKeys.dashboard.recentActivity, { limit, offset }],
    queryFn: () => dashboardApi.getRecentActivity(limit, offset),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });
}

// Top projects
export function useTopProjects(limit = 5) {
  return useQuery({
    queryKey: [...queryKeys.dashboard.topProjects, { limit }],
    queryFn: () => dashboardApi.getTopProjects(limit),
    staleTime: 5 * 60 * 1000, // 5 minutes - projects change less frequently
    gcTime: 10 * 60 * 1000,
  });
}
