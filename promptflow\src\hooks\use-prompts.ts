'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabaseBrowser as supabase } from '@/lib/supabase-browser'
import { Database } from '@/lib/supabase'
import { canCreatePrompt, updateUsageStats, PLAN_LIMIT_MESSAGES } from '@/lib/plan-limits'
// Advanced validation will be integrated in future updates
// import { checkRateLimit, RateLimitError } from '@/lib/rate-limiter'
// import { AdvancedValidator, COMMON_VALIDATION_RULES } from '@/lib/advanced-validation'

type Prompt = Database['public']['Tables']['prompts']['Row']
type PromptInsert = Database['public']['Tables']['prompts']['Insert']
type PromptUpdate = Database['public']['Tables']['prompts']['Update']

// Proje prompt'larını getir
export function usePrompts(projectId: string | null) {
  return useQuery({
    queryKey: ['prompts', projectId],
    queryFn: async (): Promise<Prompt[]> => {
      if (!projectId) {
        console.log(`📝 [USE_PROMPTS] No project ID provided`)
        return []
      }

      console.log(`📝 [USE_PROMPTS] Fetching prompts for project:`, projectId)

      try {
        // Check if we have a session first
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        console.log(`📝 [USE_PROMPTS] Session check:`, {
          hasSession: !!session,
          sessionError: sessionError?.message,
          userId: session?.user?.id
        })

        const { data, error } = await supabase
          .from('prompts')
          .select('*')
          .eq('project_id', projectId)
          .order('order_index', { ascending: true })

        if (error) {
          console.error(`❌ [USE_PROMPTS] Error fetching prompts:`, error)
          throw new Error(error.message)
        }

        console.log(`✅ [USE_PROMPTS] Prompts fetched:`, data?.length || 0, 'prompts')
        return data || []
      } catch (err) {
        console.error(`💥 [USE_PROMPTS] Exception:`, err)
        throw err
      }
    },
    enabled: !!projectId,
  })
}

// Prompt oluştur
export function useCreatePrompt() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (prompt: Omit<PromptInsert, 'user_id'>): Promise<Prompt> => {
      // Plan limiti kontrol et
      const limitCheck = await canCreatePrompt(prompt.project_id)
      if (!limitCheck.allowed) {
        throw new Error(limitCheck.reason || PLAN_LIMIT_MESSAGES.PROMPT_LIMIT_REACHED)
      }

      const { data: { user }, error: userError } = await supabase.auth.getUser()

      if (userError || !user) {
        throw new Error('Kullanıcı oturumu bulunamadı')
      }

      // Task code otomatik oluştur
      const taskCode = prompt.task_code || `task-${prompt.order_index}`

      const { data, error } = await supabase
        .from('prompts')
        .insert({
          ...prompt,
          user_id: user.id,
          task_code: taskCode,
          tags: prompt.tags || [],
          is_favorite: prompt.is_favorite || false,
          usage_count: prompt.usage_count || 0,
        })
        .select()
        .single()

      if (error) {
        throw new Error(error.message)
      }

      return data
    },
    onSuccess: async (data) => {
      queryClient.invalidateQueries({ queryKey: ['prompts', data.project_id] })
      // Invalidate hashtag and category queries
      queryClient.invalidateQueries({ queryKey: ['popular-hashtags', data.project_id] })
      queryClient.invalidateQueries({ queryKey: ['all-hashtags', data.project_id] })
      queryClient.invalidateQueries({ queryKey: ['popular-categories', data.project_id] })
      queryClient.invalidateQueries({ queryKey: ['all-categories', data.project_id] })

      // Kullanım istatistiklerini güncelle
      try {
        await updateUsageStats()
        queryClient.invalidateQueries({ queryKey: ['user-limits'] })
        queryClient.invalidateQueries({ queryKey: ['usage-stats'] })
      } catch (error) {
        console.warn('Kullanım istatistikleri güncellenemedi:', error)
      }
    },
  })
}

// Prompt güncelle
export function useUpdatePrompt() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ id, ...updates }: PromptUpdate & { id: string }): Promise<Prompt> => {
      const { data, error } = await supabase
        .from('prompts')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single()

      if (error) {
        throw new Error(error.message)
      }

      return data
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['prompts', data.project_id] })
      // Invalidate hashtag and category queries if tags or category were updated
      queryClient.invalidateQueries({ queryKey: ['popular-hashtags', data.project_id] })
      queryClient.invalidateQueries({ queryKey: ['all-hashtags', data.project_id] })
      queryClient.invalidateQueries({ queryKey: ['popular-categories', data.project_id] })
      queryClient.invalidateQueries({ queryKey: ['all-categories', data.project_id] })
    },
  })
}

// Prompt sil
export function useDeletePrompt() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ id }: { id: string; projectId: string }): Promise<void> => {
      const { error } = await supabase
        .from('prompts')
        .delete()
        .eq('id', id)

      if (error) {
        throw new Error(error.message)
      }
    },
    onSuccess: (_, { projectId }) => {
      queryClient.invalidateQueries({ queryKey: ['prompts', projectId] })
    },
  })
}

// Prompt'u kullanıldı olarak işaretle (optimistic update ile)
export function useMarkPromptAsUsed() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (promptId: string): Promise<Prompt> => {
      const { data, error } = await supabase
        .from('prompts')
        .update({ is_used: true })
        .eq('id', promptId)
        .select()
        .single()

      if (error) {
        throw new Error(error.message)
      }

      return data
    },
    onMutate: async (promptId) => {
      // Optimistic update
      const queryKey = ['prompts']
      await queryClient.cancelQueries({ queryKey })

      const previousPrompts = queryClient.getQueriesData({ queryKey })

      queryClient.setQueriesData({ queryKey }, (old: unknown) => {
        if (!old || !Array.isArray(old)) return old
        return old.map((prompt: Prompt) =>
          prompt.id === promptId ? { ...prompt, is_used: true } : prompt
        )
      })

      return { previousPrompts }
    },
    onError: (err, _promptId, context) => {
      // Hata durumunda geri al
      if (context?.previousPrompts) {
        context.previousPrompts.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data)
        })
      }
    },
    onSettled: (data) => {
      // Her durumda cache'i yenile
      if (data) {
        queryClient.invalidateQueries({ queryKey: ['prompts', data.project_id] })
      }
    },
  })
}

// Proje prompt'larını sıfırla
export function useResetPrompts() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (projectId: string): Promise<void> => {
      const { error } = await supabase
        .from('prompts')
        .update({ is_used: false })
        .eq('project_id', projectId)

      if (error) {
        throw new Error(error.message)
      }
    },
    onSuccess: (_, projectId) => {
      queryClient.invalidateQueries({ queryKey: ['prompts', projectId] })
    },
  })
}

// Toplu prompt güncelleme (optimistic update ile)
export function useBulkUpdatePrompts() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (updates: Array<{ id: string; order_index?: number; task_code?: string; is_used?: boolean }>): Promise<Prompt[]> => {
      // Try to use RPC function for order updates, fallback to individual updates for other fields
      const orderUpdates = updates.filter(u => u.order_index !== undefined || u.task_code !== undefined)
      const otherUpdates = updates.filter(u => u.order_index === undefined && u.task_code === undefined)

      const results: Prompt[] = []

      // Use RPC function for order/task_code updates (more efficient)
      if (orderUpdates.length > 0) {
        try {
          const { data: rpcData, error: rpcError } = await supabase.rpc('bulk_update_prompts_order', {
            prompt_updates: orderUpdates
          })

          if (rpcError) {
            console.warn('RPC function failed, falling back to individual updates:', rpcError)
            // Fallback to individual updates
            const fallbackPromises = orderUpdates.map(async (update) => {
              const { data, error } = await supabase
                .from('prompts')
                .update({
                  ...update,
                  updated_at: new Date().toISOString(),
                })
                .eq('id', update.id)
                .select()
                .single()

              if (error) {
                throw new Error(`Failed to update prompt ${update.id}: ${error.message}`)
              }
              return data
            })

            const fallbackResults = await Promise.all(fallbackPromises)
            results.push(...fallbackResults)
          } else if (rpcData) {
            // Get full prompt data for RPC results
            const rpcIds = rpcData.map((r: { id: string }) => r.id)
            const { data: fullPrompts, error: selectError } = await supabase
              .from('prompts')
              .select('*')
              .in('id', rpcIds)

            if (selectError) {
              throw new Error(`Failed to fetch updated prompts: ${selectError.message}`)
            }

            results.push(...(fullPrompts || []))
          }
        } catch (error) {
          console.error('Bulk update error:', error)
          throw error
        }
      }

      // Handle other updates (is_used, etc.) with individual calls
      if (otherUpdates.length > 0) {
        const updatePromises = otherUpdates.map(async (update) => {
          const { data, error } = await supabase
            .from('prompts')
            .update({
              ...update,
              updated_at: new Date().toISOString(),
            })
            .eq('id', update.id)
            .select()
            .single()

          if (error) {
            throw new Error(`Failed to update prompt ${update.id}: ${error.message}`)
          }

          return data
        })

        const updatedPrompts = await Promise.all(updatePromises)
        results.push(...updatedPrompts)
      }

      return results
    },
    onMutate: async (updates) => {
      // Optimistic update
      const queryKey = ['prompts']
      await queryClient.cancelQueries({ queryKey })

      const previousPrompts = queryClient.getQueriesData({ queryKey })

      queryClient.setQueriesData({ queryKey }, (old: unknown) => {
        if (!old || !Array.isArray(old)) return old

        return old.map((prompt: Prompt) => {
          const update = updates.find(u => u.id === prompt.id)
          return update ? { ...prompt, ...update } : prompt
        })
      })

      return { previousPrompts }
    },
    onError: (err, _updates, context) => {
      // Hata durumunda geri al
      if (context?.previousPrompts) {
        context.previousPrompts.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data)
        })
      }
    },
    onSettled: (data) => {
      // Her durumda cache'i yenile
      if (data && data.length > 0) {
        queryClient.invalidateQueries({ queryKey: ['prompts', data[0].project_id] })
      }
    },
  })
}