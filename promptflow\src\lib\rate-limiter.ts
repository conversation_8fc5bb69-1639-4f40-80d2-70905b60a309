'use client'

/**
 * Advanced Rate Limiting System for PromptFlow
 * Provides both client-side and server-side rate limiting
 */

import { supabaseBrowser as supabase } from '@/lib/supabase-browser'

// Rate limiting configurations for different actions
export const RATE_LIMITS = {
  // Authentication actions
  login: { maxRequests: 5, windowMinutes: 15 },
  signup: { maxRequests: 3, windowMinutes: 60 },
  password_reset: { maxRequests: 3, windowMinutes: 60 },
  
  // Project actions
  project_create: { maxRequests: 10, windowMinutes: 60 },
  project_update: { maxRequests: 20, windowMinutes: 10 },
  project_delete: { maxRequests: 5, windowMinutes: 60 },
  
  // Prompt actions
  prompt_create: { maxRequests: 50, windowMinutes: 10 },
  prompt_update: { maxRequests: 100, windowMinutes: 10 },
  prompt_delete: { maxRequests: 20, windowMinutes: 10 },
  
  // Context actions
  context_create: { maxRequests: 20, windowMinutes: 10 },
  context_batch_add: { maxRequests: 5, windowMinutes: 10 },
  
  // Plan actions
  plan_change: { maxRequests: 3, windowMinutes: 60 },
  
  // General API
  api_general: { maxRequests: 200, windowMinutes: 10 },
} as const

export type RateLimitAction = keyof typeof RATE_LIMITS

interface RateLimitData {
  count: number
  windowStart: number
  lastRequest: number
}

/**
 * Client-side rate limiting with localStorage
 */
export class ClientRateLimiter {
  private getStorageKey(action: RateLimitAction, userId?: string): string {
    const userSuffix = userId ? `_${userId}` : ''
    return `rate_limit_${action}${userSuffix}`
  }

  /**
   * Check if action is allowed based on rate limits
   */
  async checkLimit(action: RateLimitAction, userId?: string): Promise<{
    allowed: boolean
    remainingRequests: number
    resetTime: number
    retryAfter?: number
  }> {
    try {
      const config = RATE_LIMITS[action]
      const storageKey = this.getStorageKey(action, userId)
      const now = Date.now()
      const windowMs = config.windowMinutes * 60 * 1000

      // Get current data
      const stored = localStorage.getItem(storageKey)
      let data: RateLimitData

      if (!stored) {
        // First request
        data = {
          count: 1,
          windowStart: now,
          lastRequest: now
        }
        localStorage.setItem(storageKey, JSON.stringify(data))
        
        return {
          allowed: true,
          remainingRequests: config.maxRequests - 1,
          resetTime: now + windowMs
        }
      }

      data = JSON.parse(stored)

      // Check if window has expired
      if (now - data.windowStart > windowMs) {
        // Reset window
        data = {
          count: 1,
          windowStart: now,
          lastRequest: now
        }
        localStorage.setItem(storageKey, JSON.stringify(data))
        
        return {
          allowed: true,
          remainingRequests: config.maxRequests - 1,
          resetTime: now + windowMs
        }
      }

      // Check if limit exceeded
      if (data.count >= config.maxRequests) {
        const resetTime = data.windowStart + windowMs
        const retryAfter = Math.ceil((resetTime - now) / 1000)
        
        return {
          allowed: false,
          remainingRequests: 0,
          resetTime,
          retryAfter
        }
      }

      // Increment counter
      data.count++
      data.lastRequest = now
      localStorage.setItem(storageKey, JSON.stringify(data))

      return {
        allowed: true,
        remainingRequests: config.maxRequests - data.count,
        resetTime: data.windowStart + windowMs
      }
    } catch (error) {
      console.warn(`Rate limit check failed for ${action}:`, error)
      // On error, allow the request but log it
      return {
        allowed: true,
        remainingRequests: 0,
        resetTime: Date.now() + 60000 // 1 minute fallback
      }
    }
  }

  /**
   * Reset rate limit for specific action
   */
  resetLimit(action: RateLimitAction, userId?: string): void {
    try {
      const storageKey = this.getStorageKey(action, userId)
      localStorage.removeItem(storageKey)
    } catch (error) {
      console.warn(`Rate limit reset failed for ${action}:`, error)
    }
  }

  /**
   * Get current rate limit status
   */
  getStatus(action: RateLimitAction, userId?: string): {
    count: number
    remaining: number
    resetTime: number
  } | null {
    try {
      const config = RATE_LIMITS[action]
      const storageKey = this.getStorageKey(action, userId)
      const stored = localStorage.getItem(storageKey)
      
      if (!stored) {
        return {
          count: 0,
          remaining: config.maxRequests,
          resetTime: Date.now() + config.windowMinutes * 60 * 1000
        }
      }

      const data: RateLimitData = JSON.parse(stored)
      const windowMs = config.windowMinutes * 60 * 1000
      const now = Date.now()

      // Check if window expired
      if (now - data.windowStart > windowMs) {
        return {
          count: 0,
          remaining: config.maxRequests,
          resetTime: now + windowMs
        }
      }

      return {
        count: data.count,
        remaining: Math.max(0, config.maxRequests - data.count),
        resetTime: data.windowStart + windowMs
      }
    } catch (error) {
      console.warn(`Rate limit status check failed for ${action}:`, error)
      return null
    }
  }
}

/**
 * Server-side rate limiting with Supabase
 */
export class ServerRateLimiter {
  /**
   * Check server-side rate limit using Supabase function
   */
  async checkLimit(
    action: RateLimitAction,
    userId?: string
  ): Promise<{
    allowed: boolean
    remainingRequests: number
    resetTime: number
    retryAfter?: number
  }> {
    try {
      const config = RATE_LIMITS[action]
      
      // Get current user if not provided
      let targetUserId = userId
      if (!targetUserId) {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          throw new Error('User not authenticated')
        }
        targetUserId = user.id
      }

      // Call Supabase rate limiting function
      const { data, error } = await supabase.rpc('check_rate_limit', {
        p_user_id: targetUserId,
        p_action_type: action,
        p_max_requests: config.maxRequests,
        p_window_minutes: config.windowMinutes
      })

      if (error) {
        console.error(`Server rate limit check failed for ${action}:`, error)
        // On error, allow but log
        return {
          allowed: true,
          remainingRequests: 0,
          resetTime: Date.now() + config.windowMinutes * 60 * 1000
        }
      }

      const allowed = data === true
      const windowMs = config.windowMinutes * 60 * 1000
      const resetTime = Date.now() + windowMs

      if (!allowed) {
        return {
          allowed: false,
          remainingRequests: 0,
          resetTime,
          retryAfter: Math.ceil(windowMs / 1000)
        }
      }

      return {
        allowed: true,
        remainingRequests: config.maxRequests - 1, // Approximate
        resetTime
      }
    } catch (error) {
      console.error(`Server rate limit error for ${action}:`, error)
      // On error, allow but log
      return {
        allowed: true,
        remainingRequests: 0,
        resetTime: Date.now() + 60000
      }
    }
  }
}

// Global instances
export const clientRateLimiter = new ClientRateLimiter()
export const serverRateLimiter = new ServerRateLimiter()

/**
 * Combined rate limiting check (client + server)
 */
export async function checkRateLimit(
  action: RateLimitAction,
  options: {
    clientOnly?: boolean
    serverOnly?: boolean
    userId?: string
  } = {}
): Promise<{
  allowed: boolean
  remainingRequests: number
  resetTime: number
  retryAfter?: number
  source: 'client' | 'server' | 'both'
}> {
  const { clientOnly = false, serverOnly = false, userId } = options

  try {
    // Client-side check
    if (!serverOnly) {
      const clientResult = await clientRateLimiter.checkLimit(action, userId)
      if (!clientResult.allowed) {
        return {
          ...clientResult,
          source: 'client'
        }
      }
      
      if (clientOnly) {
        return {
          ...clientResult,
          source: 'client'
        }
      }
    }

    // Server-side check
    if (!clientOnly) {
      const serverResult = await serverRateLimiter.checkLimit(action, userId)
      return {
        ...serverResult,
        source: serverOnly ? 'server' : 'both'
      }
    }

    // Fallback (shouldn't reach here)
    return {
      allowed: true,
      remainingRequests: 0,
      resetTime: Date.now() + 60000,
      source: 'client'
    }
  } catch (error) {
    console.error(`Rate limit check failed for ${action}:`, error)
    return {
      allowed: true,
      remainingRequests: 0,
      resetTime: Date.now() + 60000,
      source: 'client'
    }
  }
}

/**
 * Rate limit error class
 */
export class RateLimitError extends Error {
  constructor(
    public action: RateLimitAction,
    public retryAfter: number,
    public resetTime: number
  ) {
    super(`Rate limit exceeded for ${action}. Try again in ${retryAfter} seconds.`)
    this.name = 'RateLimitError'
  }
}

/**
 * Rate limiting hook for React components
 */
export function useRateLimit(action: RateLimitAction) {
  return {
    checkLimit: (options?: { clientOnly?: boolean; serverOnly?: boolean }) =>
      checkRateLimit(action, options),
    resetLimit: () => clientRateLimiter.resetLimit(action),
    getStatus: () => clientRateLimiter.getStatus(action)
  }
}
