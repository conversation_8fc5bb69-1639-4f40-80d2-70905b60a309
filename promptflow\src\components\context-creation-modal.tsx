'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Plus, 
  X, 
  Loader2, 
  AlertCircle, 
  Globe, 
  Lock, 
  Tag,
  FileText
} from 'lucide-react'
import { useContextCategories, useCreateContext } from '@/hooks/use-contexts'
import { useAddContextToProject } from '@/hooks/use-context-to-prompt'
import { useProjects } from '@/hooks/use-projects'
import { toast } from 'sonner'

interface ContextCreationModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
  /** Show option to add directly to current project */
  showAddToProject?: boolean
}

export function ContextCreationModal({
  open,
  onOpenChange,
  onSuccess,
  showAddToProject = true
}: ContextCreationModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    category_id: '',
    is_public: false,
    is_template: false,
    tags: [] as string[]
  })
  const [newTag, setNewTag] = useState('')
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [addToProject, setAddToProject] = useState(false)

  const { data: categories = [], isLoading: categoriesLoading } = useContextCategories()
  const { data: projects = [] } = useProjects()
  const createContextMutation = useCreateContext()
  const { addContext: addContextToProject, isLoading: isAddingToProject } = useAddContextToProject()

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = 'Başlık gereklidir'
    }

    if (!formData.content.trim()) {
      newErrors.content = 'İçerik gereklidir'
    }

    if (!formData.category_id) {
      newErrors.category_id = 'Kategori seçimi gereklidir'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      toast.error('Lütfen tüm gerekli alanları doldurun')
      return
    }

    try {
      // Create context first
      const newContext = await createContextMutation.mutateAsync({
        title: formData.title.trim(),
        description: formData.description.trim() || undefined,
        content: formData.content.trim(),
        category_id: formData.category_id,
        is_public: formData.is_public,
        is_template: formData.is_template,
        tags: formData.tags
      })

      // If "Add to Project" is checked, add to current project
      if (addToProject && showAddToProject) {
        try {
          await addContextToProject(newContext, formData.title.trim())
          toast.success('Context oluşturuldu ve projeye eklendi!')
        } catch (projectError) {
          console.error('Failed to add to project:', projectError)
          toast.warning('Context oluşturuldu ancak projeye eklenirken hata oluştu')
        }
      } else {
        toast.success('Context başarıyla oluşturuldu!')
      }

      // Reset form
      setFormData({
        title: '',
        description: '',
        content: '',
        category_id: '',
        is_public: false,
        is_template: false,
        tags: []
      })
      setErrors({})
      setAddToProject(false)

      onSuccess?.()
      onOpenChange(false)
    } catch (error) {
      console.error('Context creation error:', error)
      toast.error('Context oluşturulurken bir hata oluştu')
    }
  }

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }))
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addTag()
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Yeni Context Oluştur
          </DialogTitle>
          <DialogDescription>
            Yeni bir context oluşturun. Herkese açık contextler admin onayı gerektirir.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <form onSubmit={handleSubmit} className="space-y-6 p-1">
            {/* Title */}
            <div className="space-y-2">
              <Label htmlFor="title">
                Başlık <span className="text-red-500">*</span>
              </Label>
              <Input
                id="title"
                placeholder="Context başlığını girin..."
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                className={errors.title ? 'border-red-500' : ''}
              />
              {errors.title && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.title}
                </p>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Açıklama</Label>
              <Input
                id="description"
                placeholder="Context açıklaması (isteğe bağlı)..."
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>

            {/* Category */}
            <div className="space-y-2">
              <Label htmlFor="category">
                Kategori <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formData.category_id}
                onValueChange={(value) => setFormData(prev => ({ ...prev, category_id: value }))}
              >
                <SelectTrigger className={errors.category_id ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Kategori seçin..." />
                </SelectTrigger>
                <SelectContent>
                  {categoriesLoading ? (
                    <SelectItem value="loading" disabled>
                      Kategoriler yükleniyor...
                    </SelectItem>
                  ) : (
                    categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        <span className="flex items-center gap-2">
                          <span>{category.icon}</span>
                          {category.name}
                        </span>
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {errors.category_id && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.category_id}
                </p>
              )}
            </div>

            {/* Content */}
            <div className="space-y-2">
              <Label htmlFor="content">
                İçerik <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="content"
                placeholder="Context içeriğini girin..."
                value={formData.content}
                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                className={`min-h-[120px] resize-none ${errors.content ? 'border-red-500' : ''}`}
              />
              {errors.content && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.content}
                </p>
              )}
            </div>

            {/* Tags */}
            <div className="space-y-2">
              <Label htmlFor="tags">Etiketler</Label>
              <div className="flex gap-2">
                <Input
                  id="tags"
                  placeholder="Etiket ekle..."
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addTag}
                  disabled={!newTag.trim()}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {formData.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      <Tag className="h-3 w-3" />
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="ml-1 hover:text-red-500"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Visibility Options */}
            <div className="space-y-3">
              <Label>Görünürlük Ayarları</Label>
              <div className="space-y-2">
                <label className="flex items-center gap-3 cursor-pointer">
                  <input
                    type="radio"
                    name="visibility"
                    checked={!formData.is_public}
                    onChange={() => setFormData(prev => ({ ...prev, is_public: false }))}
                    className="w-4 h-4"
                  />
                  <div className="flex items-center gap-2">
                    <Lock className="h-4 w-4 text-gray-500" />
                    <div className="flex flex-col">
                      <span className="text-sm">Özel</span>
                      <span className="text-xs text-gray-500">Sadece ben görebilirim</span>
                    </div>
                  </div>
                </label>
                <label className="flex items-center gap-3 cursor-pointer">
                  <input
                    type="radio"
                    name="visibility"
                    checked={formData.is_public}
                    onChange={() => setFormData(prev => ({ ...prev, is_public: true }))}
                    className="w-4 h-4"
                  />
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-blue-500" />
                    <div className="flex flex-col">
                      <span className="text-sm">Herkese Açık</span>
                      <span className="text-xs text-gray-500">Tüm kullanıcılar görebilir</span>
                    </div>
                  </div>
                </label>
              </div>
            </div>

            {/* Template Option */}
            <div className="space-y-2">
              <label className="flex items-center gap-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.is_template}
                  onChange={(e) => setFormData(prev => ({ ...prev, is_template: e.target.checked }))}
                  className="w-4 h-4"
                />
                <span className="text-sm">Bu contexti şablon olarak işaretle</span>
              </label>
            </div>

            {/* Add to Project Option */}
            {showAddToProject && projects.length > 0 && (
              <div className="space-y-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <label className="flex items-center gap-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={addToProject}
                    onChange={(e) => setAddToProject(e.target.checked)}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-blue-600" />
                    <div className="flex flex-col">
                      <span className="text-sm font-medium text-blue-900">Mevcut projeye ekle</span>
                      <span className="text-xs text-blue-700">Context oluşturulduktan sonra aktif projeye prompt olarak eklenecek</span>
                    </div>
                  </div>
                </label>
              </div>
            )}

            {/* Submit Buttons */}
            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="flex-1"
              >
                İptal
              </Button>
              <Button
                type="submit"
                disabled={createContextMutation.isPending || isAddingToProject}
                className="flex-1"
              >
                {(createContextMutation.isPending || isAddingToProject) && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {addToProject ? 'Oluştur ve Ekle' : 'Oluştur'}
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  )
}
