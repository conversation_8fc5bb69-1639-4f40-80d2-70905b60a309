-- PromptFlow Kullanıcı Planları Sistemi
-- Yardımcı Fonksiyonlar ve Stored Procedures

-- 1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ın Aktif Planını Getir
CREATE OR REPLACE FUNCTION get_user_active_plan(user_uuid uuid)
RETURNS TABLE (
  plan_id uuid,
  plan_name text,
  display_name text,
  max_projects integer,
  max_prompts_per_project integer,
  features jsonb,
  status text,
  expires_at timestamp with time zone
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pt.id as plan_id,
    pt.name as plan_name,
    pt.display_name,
    pt.max_projects,
    pt.max_prompts_per_project,
    pt.features,
    up.status,
    up.expires_at
  FROM user_plans up
  JOIN plan_types pt ON up.plan_type_id = pt.id
  WHERE up.user_id = user_uuid 
    AND up.status = 'active'
    AND (up.expires_at IS NULL OR up.expires_at > now())
  ORDER BY up.created_at DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. <PERSON><PERSON><PERSON><PERSON><PERSON>nın Mevcut Kullanımını Kontrol Et
CREATE OR REPLACE FUNCTION check_user_limits(user_uuid uuid)
RETURNS TABLE (
  current_projects integer,
  current_prompts integer,
  max_projects integer,
  max_prompts_per_project integer,
  can_create_project boolean,
  can_create_prompt boolean
) AS $$
DECLARE
  user_plan RECORD;
  project_count integer;
  max_prompts integer;
BEGIN
  -- Kullanıcının aktif planını al
  SELECT * INTO user_plan FROM get_user_active_plan(user_uuid);
  
  -- Eğer plan bulunamazsa, ücretsiz plan limitlerini kullan
  IF user_plan IS NULL THEN
    SELECT 5, 100 INTO user_plan.max_projects, user_plan.max_prompts_per_project;
  END IF;
  
  -- Mevcut proje sayısını al
  SELECT COUNT(*) INTO project_count
  FROM projects 
  WHERE user_id = user_uuid;
  
  -- En fazla prompt sayısını hesapla (herhangi bir projede)
  SELECT COALESCE(MAX(prompt_count), 0) INTO max_prompts
  FROM (
    SELECT COUNT(*) as prompt_count
    FROM prompts 
    WHERE user_id = user_uuid
    GROUP BY project_id
  ) project_prompts;
  
  RETURN QUERY
  SELECT 
    project_count as current_projects,
    max_prompts as current_prompts,
    user_plan.max_projects,
    user_plan.max_prompts_per_project,
    (user_plan.max_projects = -1 OR project_count < user_plan.max_projects) as can_create_project,
    (user_plan.max_prompts_per_project = -1 OR max_prompts < user_plan.max_prompts_per_project) as can_create_prompt;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Kullanım İstatistiklerini Güncelle
CREATE OR REPLACE FUNCTION update_usage_stats(user_uuid uuid)
RETURNS void AS $$
DECLARE
  project_count integer;
  prompt_count integer;
BEGIN
  -- Mevcut proje sayısını al
  SELECT COUNT(*) INTO project_count
  FROM projects 
  WHERE user_id = user_uuid;
  
  -- Mevcut prompt sayısını al
  SELECT COUNT(*) INTO prompt_count
  FROM prompts 
  WHERE user_id = user_uuid;
  
  -- Bugünkü istatistikleri güncelle veya ekle
  INSERT INTO usage_stats (user_id, stat_date, projects_count, prompts_count, last_activity_at)
  VALUES (user_uuid, CURRENT_DATE, project_count, prompt_count, now())
  ON CONFLICT (user_id, stat_date)
  DO UPDATE SET
    projects_count = EXCLUDED.projects_count,
    prompts_count = EXCLUDED.prompts_count,
    last_activity_at = EXCLUDED.last_activity_at,
    updated_at = now();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Yeni Kullanıcıya Ücretsiz Plan Ata
CREATE OR REPLACE FUNCTION assign_free_plan_to_new_user()
RETURNS TRIGGER AS $$
DECLARE
  free_plan_id uuid;
BEGIN
  -- Ücretsiz planın ID'sini al
  SELECT id INTO free_plan_id 
  FROM plan_types 
  WHERE name = 'free' AND is_active = true;
  
  -- Eğer ücretsiz plan varsa, kullanıcıya ata
  IF free_plan_id IS NOT NULL THEN
    INSERT INTO user_plans (user_id, plan_type_id, status, billing_cycle)
    VALUES (NEW.id, free_plan_id, 'active', 'lifetime');
    
    -- İlk kullanım istatistiğini oluştur
    INSERT INTO usage_stats (user_id, stat_date, projects_count, prompts_count, last_activity_at)
    VALUES (NEW.id, CURRENT_DATE, 0, 0, now());
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Kullanıcı kaydı trigger'ı
CREATE TRIGGER assign_free_plan_on_signup
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION assign_free_plan_to_new_user();

-- 6. Plan Değiştirme Fonksiyonu
CREATE OR REPLACE FUNCTION change_user_plan(
  user_uuid uuid,
  new_plan_name text,
  billing_cycle_param text DEFAULT 'monthly',
  payment_reference_param text DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
  current_plan_id uuid;
  new_plan_id uuid;
  new_user_plan_id uuid;
  transaction_id uuid;
BEGIN
  -- Mevcut aktif planı al
  SELECT plan_type_id INTO current_plan_id
  FROM user_plans
  WHERE user_id = user_uuid AND status = 'active'
  ORDER BY created_at DESC
  LIMIT 1;
  
  -- Yeni planın ID'sini al
  SELECT id INTO new_plan_id
  FROM plan_types
  WHERE name = new_plan_name AND is_active = true;
  
  IF new_plan_id IS NULL THEN
    RAISE EXCEPTION 'Plan bulunamadı: %', new_plan_name;
  END IF;
  
  -- Mevcut planı iptal et (eğer varsa)
  IF current_plan_id IS NOT NULL THEN
    UPDATE user_plans 
    SET status = 'cancelled', cancelled_at = now()
    WHERE user_id = user_uuid AND status = 'active';
  END IF;
  
  -- Yeni planı oluştur
  INSERT INTO user_plans (user_id, plan_type_id, status, billing_cycle)
  VALUES (user_uuid, new_plan_id, 'active', billing_cycle_param)
  RETURNING id INTO new_user_plan_id;
  
  -- Plan değişiklik işlemini kaydet
  INSERT INTO plan_transactions (
    user_id, 
    from_plan_id, 
    to_plan_id, 
    transaction_type,
    payment_status,
    payment_reference,
    processed_at
  )
  VALUES (
    user_uuid,
    current_plan_id,
    new_plan_id,
    CASE 
      WHEN current_plan_id IS NULL THEN 'initial'
      ELSE 'upgrade'
    END,
    'completed',
    payment_reference_param,
    now()
  )
  RETURNING id INTO transaction_id;
  
  -- Kullanım istatistiklerini güncelle
  PERFORM update_usage_stats(user_uuid);
  
  RETURN new_user_plan_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Süresi Dolan Planları Kontrol Et
CREATE OR REPLACE FUNCTION expire_old_plans()
RETURNS integer AS $$
DECLARE
  expired_count integer;
BEGIN
  UPDATE user_plans 
  SET status = 'expired'
  WHERE status = 'active' 
    AND expires_at IS NOT NULL 
    AND expires_at < now();
  
  GET DIAGNOSTICS expired_count = ROW_COUNT;
  
  RETURN expired_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Admin Fonksiyonları

-- Kullanıcı plan istatistikleri
CREATE OR REPLACE FUNCTION get_plan_statistics()
RETURNS TABLE (
  plan_name text,
  display_name text,
  active_users bigint,
  total_revenue numeric
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pt.name as plan_name,
    pt.display_name,
    COUNT(up.id) as active_users,
    SUM(pt.price_monthly) as total_revenue
  FROM plan_types pt
  LEFT JOIN user_plans up ON pt.id = up.plan_type_id AND up.status = 'active'
  WHERE pt.is_active = true
  GROUP BY pt.id, pt.name, pt.display_name, pt.price_monthly
  ORDER BY pt.sort_order;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
