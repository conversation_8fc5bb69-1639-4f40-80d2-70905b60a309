/**
 * Utility functions for hashtag and category management
 */

// Simple memoization cache for performance optimization
const parseCache = new Map<string, { category: string | null; tags: string[] }>();
const CACHE_SIZE_LIMIT = 100; // Prevent memory leaks

// Extract hashtags from text (words starting with #)
export function extractHashtags(text: string): string[] {
  const hashtagRegex = /#[\w\u00C0-\u017F]+/g;
  const matches = text.match(hashtagRegex);
  return matches ? matches.map(tag => tag.toLowerCase()) : [];
}

// Extract folder paths from text (words starting with /)
export function extractFolderPaths(text: string): string[] {
  const folderRegex = /\/[\w\u00C0-\u017F\/]+/g;
  const matches = text.match(folderRegex);
  return matches ? matches.map(path => path.toLowerCase()) : [];
}

// Clean and validate hashtag
export function cleanHashtag(hashtag: string): string {
  // Remove # if present, clean whitespace, convert to lowercase
  return hashtag.replace(/^#/, '').trim().toLowerCase();
}

// Clean and validate folder path
export function cleanFolderPath(path: string): string {
  // Ensure starts with /, clean multiple slashes, convert to lowercase
  let cleaned = path.trim().toLowerCase();
  if (!cleaned.startsWith('/')) {
    cleaned = '/' + cleaned;
  }
  // Remove multiple consecutive slashes
  cleaned = cleaned.replace(/\/+/g, '/');
  // Remove trailing slash unless it's root
  if (cleaned.length > 1 && cleaned.endsWith('/')) {
    cleaned = cleaned.slice(0, -1);
  }
  return cleaned;
}

// Format hashtag for display (with #)
export function formatHashtag(hashtag: string): string {
  const cleaned = cleanHashtag(hashtag);
  return cleaned ? `#${cleaned}` : '';
}

// Parse hashtags and folder paths from prompt text (LEGACY - removes text)
export function parsePromptCategories(text: string): {
  hashtags: string[];
  folderPaths: string[];
  cleanText: string;
} {
  const hashtags = extractHashtags(text);
  const folderPaths = extractFolderPaths(text);

  // Remove hashtags and folder paths from text
  let cleanText = text;
  hashtags.forEach(tag => {
    cleanText = cleanText.replace(new RegExp(tag, 'gi'), '');
  });
  folderPaths.forEach(path => {
    cleanText = cleanText.replace(new RegExp(path.replace(/\//g, '\\/'), 'gi'), '');
  });

  // Clean up extra whitespace
  cleanText = cleanText.replace(/\s+/g, ' ').trim();

  return {
    hashtags: hashtags.map(cleanHashtag),
    folderPaths: folderPaths.map(cleanFolderPath),
    cleanText
  };
}

// NEW: Parse hashtags and folder paths while preserving original text
export function parsePromptCategoriesPreserveText(text: string): {
  hashtags: string[];
  folderPaths: string[];
  originalText: string;
} {
  const hashtags = extractHashtags(text);
  const folderPaths = extractFolderPaths(text);

  return {
    hashtags: hashtags.map(cleanHashtag),
    folderPaths: folderPaths.map(cleanFolderPath),
    originalText: text // Preserve original text exactly as typed
  };
}

// NEW: Extract only categories and tags for database storage (OPTIMIZED with MEMOIZATION)
export function extractCategoriesOnly(text: string): {
  category: string | null;
  tags: string[];
} {
  // Early return for empty text
  if (!text || text.trim().length === 0) {
    return { category: null, tags: [] };
  }

  // Check cache first
  const cacheKey = text.trim();
  if (parseCache.has(cacheKey)) {
    return parseCache.get(cacheKey)!;
  }

  // Use more efficient regex with single pass
  const hashtagMatches = text.match(/#[\w\u00C0-\u017F]+/g);
  const folderMatches = text.match(/\/[\w\u00C0-\u017F\/]+/g);

  // Use first folder path as category, or null if none
  const category = folderMatches && folderMatches.length > 0
    ? cleanFolderPath(folderMatches[0])
    : null;

  // Clean hashtags for tags (avoid unnecessary map if no hashtags)
  const tags = hashtagMatches
    ? hashtagMatches.map(cleanHashtag)
    : [];

  const result = { category, tags };

  // Cache the result (with size limit)
  if (parseCache.size >= CACHE_SIZE_LIMIT) {
    // Remove oldest entry (simple LRU)
    const firstKey = parseCache.keys().next().value;
    if (firstKey !== undefined) {
      parseCache.delete(firstKey);
    }
  }
  parseCache.set(cacheKey, result);

  return result;
}

// Merge hashtags with existing tags
export function mergeHashtags(existingTags: string[], newHashtags: string[]): string[] {
  const allTags = [...existingTags, ...newHashtags];
  // Remove duplicates and empty tags
  return [...new Set(allTags.filter(tag => tag.trim().length > 0))];
}

// Get folder hierarchy from path
export function getFolderHierarchy(path: string): string[] {
  if (!path || path === '/') return ['/'];
  
  const parts = path.split('/').filter(part => part.length > 0);
  const hierarchy = ['/'];
  
  let currentPath = '';
  parts.forEach(part => {
    currentPath += '/' + part;
    hierarchy.push(currentPath);
  });
  
  return hierarchy;
}

// Get parent folder from path
export function getParentFolder(path: string): string {
  if (!path || path === '/') return '/';
  
  const lastSlashIndex = path.lastIndexOf('/');
  if (lastSlashIndex <= 0) return '/';
  
  return path.substring(0, lastSlashIndex) || '/';
}

// Get folder name from path
export function getFolderName(path: string): string {
  if (!path || path === '/') return 'Root';
  
  const lastSlashIndex = path.lastIndexOf('/');
  return path.substring(lastSlashIndex + 1) || 'Root';
}

// Validate hashtag format
export function isValidHashtag(hashtag: string): boolean {
  const cleaned = cleanHashtag(hashtag);
  return /^[\w\u00C0-\u017F]+$/.test(cleaned) && cleaned.length > 0;
}

// Validate folder path format
export function isValidFolderPath(path: string): boolean {
  const cleaned = cleanFolderPath(path);
  return /^\/[\w\u00C0-\u017F\/]*$/.test(cleaned);
}

// Get hashtag suggestions based on existing hashtags
export function getHashtagSuggestions(
  input: string, 
  existingHashtags: string[], 
  limit: number = 5
): string[] {
  const cleanInput = cleanHashtag(input);
  if (!cleanInput) return [];
  
  return existingHashtags
    .filter(tag => tag.toLowerCase().includes(cleanInput))
    .slice(0, limit);
}

// Get folder suggestions based on existing folders
export function getFolderSuggestions(
  input: string, 
  existingFolders: string[], 
  limit: number = 5
): string[] {
  const cleanInput = input.toLowerCase();
  if (!cleanInput) return [];
  
  return existingFolders
    .filter(folder => folder.toLowerCase().includes(cleanInput))
    .slice(0, limit);
}
