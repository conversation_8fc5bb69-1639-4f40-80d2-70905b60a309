'use client'

import { <PERSON><PERSON><PERSON>riangle, Crown, TrendingUp, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useState } from 'react'
import { cn } from '@/lib/utils'

interface LimitWarningProps {
  type: 'project' | 'prompt'
  current: number
  max: number
  planName: string
  onUpgrade?: () => void
  className?: string
}

export function LimitWarning({ 
  type, 
  current, 
  max, 
  planName, 
  onUpgrade,
  className 
}: LimitWarningProps) {
  const [dismissed, setDismissed] = useState(false)

  if (dismissed || max === -1) return null

  const percentage = (current / max) * 100
  const isNearLimit = percentage >= 80
  const isAtLimit = current >= max

  if (!isNearLimit) return null

  const getWarningLevel = () => {
    if (isAtLimit) return 'error'
    if (percentage >= 90) return 'warning'
    return 'info'
  }

  const getWarningColors = () => {
    const level = getWarningLevel()
    switch (level) {
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800'
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800'
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800'
    }
  }

  const getIconColors = () => {
    const level = getWarningLevel()
    switch (level) {
      case 'error':
        return 'text-red-500'
      case 'warning':
        return 'text-yellow-500'
      case 'info':
        return 'text-blue-500'
      default:
        return 'text-gray-500'
    }
  }

  const getTitle = () => {
    if (isAtLimit) {
      return type === 'project' ? 'Proje limiti doldu!' : 'Prompt limiti doldu!'
    }
    return type === 'project' ? 'Proje limitine yaklaşıyorsunuz' : 'Prompt limitine yaklaşıyorsunuz'
  }

  const getDescription = () => {
    if (isAtLimit) {
      return type === 'project' 
        ? 'Yeni proje oluşturmak için planınızı yükseltin.'
        : 'Bu projede yeni prompt oluşturmak için planınızı yükseltin.'
    }
    
    const remaining = max - current
    return type === 'project'
      ? `${remaining} proje hakkınız kaldı. Daha fazla proje için planınızı yükseltin.`
      : `Bu projede ${remaining} prompt hakkınız kaldı. Daha fazla prompt için planınızı yükseltin.`
  }

  const getSuggestedPlan = () => {
    if (planName === 'free') return 'Profesyonel'
    if (planName === 'professional') return 'Kurumsal'
    return null
  }

  return (
    <Card className={cn('border-l-4', getWarningColors(), className)}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <AlertTriangle className={cn('h-5 w-5 mt-0.5 flex-shrink-0', getIconColors())} />
          
          <div className="flex-1 space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-sm">
                {getTitle()}
              </h4>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-transparent"
                onClick={() => setDismissed(true)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            <p className="text-sm opacity-90">
              {getDescription()}
            </p>

            <div className="flex items-center justify-between pt-2">
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  {current}/{max} kullanıldı
                </Badge>
                <Badge variant="outline" className="text-xs">
                  %{Math.round(percentage)} dolu
                </Badge>
              </div>

              {onUpgrade && getSuggestedPlan() && (
                <Button
                  size="sm"
                  onClick={onUpgrade}
                  className="h-7 text-xs"
                >
                  <Crown className="h-3 w-3 mr-1" />
                  {getSuggestedPlan()} Plan
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Hızlı kullanım için özel bileşenler
export function ProjectLimitWarning({ 
  current, 
  max, 
  planName, 
  onUpgrade,
  className 
}: Omit<LimitWarningProps, 'type'>) {
  return (
    <LimitWarning
      type="project"
      current={current}
      max={max}
      planName={planName}
      onUpgrade={onUpgrade}
      className={className}
    />
  )
}

export function PromptLimitWarning({ 
  current, 
  max, 
  planName, 
  onUpgrade,
  className 
}: Omit<LimitWarningProps, 'type'>) {
  return (
    <LimitWarning
      type="prompt"
      current={current}
      max={max}
      planName={planName}
      onUpgrade={onUpgrade}
      className={className}
    />
  )
}

// Inline uyarı bileşeni (daha kompakt)
export function InlineLimitWarning({ 
  type, 
  current, 
  max, 
  onUpgrade 
}: Pick<LimitWarningProps, 'type' | 'current' | 'max' | 'onUpgrade'>) {
  if (max === -1 || current < max) return null

  return (
    <div className="flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded text-red-800 text-sm">
      <AlertTriangle className="h-4 w-4 text-red-500" />
      <span className="flex-1">
        {type === 'project' ? 'Proje limiti doldu' : 'Prompt limiti doldu'}
      </span>
      {onUpgrade && (
        <Button size="sm" variant="outline" onClick={onUpgrade} className="h-6 text-xs">
          <TrendingUp className="h-3 w-3 mr-1" />
          Yükselt
        </Button>
      )}
    </div>
  )
}
