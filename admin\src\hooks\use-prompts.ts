import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { promptsApi } from '@/lib/api';
import { queryKeys } from '@/lib/query-client';
import { useToast } from '@chakra-ui/react';
import { useRealtimeSubscription } from '@/components/realtime/realtime-provider';
import { useOptimisticPrompts } from './use-optimistic-updates';

// Paginated prompts list with search and filters
export function usePrompts(options: {
  page?: number;
  limit?: number;
  search?: string;
  projectId?: string;
} = {}) {
  // Subscribe to real-time updates for prompts
  useRealtimeSubscription('prompts', options.projectId ? `project_id=eq.${options.projectId}` : undefined);

  return useQuery({
    queryKey: queryKeys.prompts.list(options),
    queryFn: () => promptsApi.getPrompts(options),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000,
    // Keep previous data while loading new page
    placeholderData: (previousData) => previousData,
  });
}

// Single prompt detail
export function usePrompt(id: string) {
  return useQuery({
    queryKey: queryKeys.prompts.detail(id),
    queryFn: () => promptsApi.getPrompt(id),
    enabled: !!id,
    staleTime: 3 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
}

// Prompt mutations with optimistic updates
export function useCreatePrompt() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: promptsApi.createPrompt,
    onSuccess: () => {
      // Invalidate and refetch prompts list
      queryClient.invalidateQueries({ queryKey: queryKeys.prompts.all });
      
      toast({
        title: 'Prompt oluşturuldu',
        status: 'success',
        duration: 3000,
      });
    },
    onError: (error) => {
      toast({
        title: 'Prompt oluşturulamadı',
        description: error.message,
        status: 'error',
        duration: 5000,
      });
    },
  });
}

export function useUpdatePrompt() {
  const queryClient = useQueryClient();
  const toast = useToast();
  const { updatePromptOptimistically } = useOptimisticPrompts();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => promptsApi.updatePrompt(id, data),
    onMutate: async ({ id, data }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.prompts.detail(id) });
      await queryClient.cancelQueries({ queryKey: queryKeys.prompts.all });

      // Optimistically update the prompt
      updatePromptOptimistically(id, data);

      // Return rollback function
      return { id, data };
    },
    onSuccess: (_, { id }) => {
      toast({
        title: 'Prompt güncellendi',
        status: 'success',
        duration: 3000,
      });
    },
    onError: (error, { id }, context) => {
      // Rollback optimistic update
      queryClient.invalidateQueries({ queryKey: queryKeys.prompts.detail(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.prompts.all });

      toast({
        title: 'Prompt güncellenemedi',
        description: error.message,
        status: 'error',
        duration: 5000,
      });
    },
  });
}

export function useDeletePrompt() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: promptsApi.deletePrompt,
    onSuccess: () => {
      // Invalidate prompts list
      queryClient.invalidateQueries({ queryKey: queryKeys.prompts.all });
      
      toast({
        title: 'Prompt silindi',
        status: 'success',
        duration: 3000,
      });
    },
    onError: (error) => {
      toast({
        title: 'Prompt silinemedi',
        description: error.message,
        status: 'error',
        duration: 5000,
      });
    },
  });
}

// Bulk operations
export function useBulkUpdatePrompts() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: promptsApi.bulkUpdatePrompts,
    onSuccess: (_, updates) => {
      // Invalidate prompts list
      queryClient.invalidateQueries({ queryKey: queryKeys.prompts.all });
      
      toast({
        title: 'Prompts güncellendi',
        description: `${updates.length} prompt güncellendi`,
        status: 'success',
        duration: 3000,
      });
    },
    onError: (error) => {
      toast({
        title: 'Toplu güncelleme başarısız',
        description: error.message,
        status: 'error',
        duration: 5000,
      });
    },
  });
}

export function useBulkMarkAsUsed() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: promptsApi.bulkMarkAsUsed,
    onSuccess: (_, promptIds) => {
      // Invalidate prompts list
      queryClient.invalidateQueries({ queryKey: queryKeys.prompts.all });
      
      toast({
        title: 'Prompts işaretlendi',
        description: `${promptIds.length} prompt kullanıldı olarak işaretlendi`,
        status: 'success',
        duration: 3000,
      });
    },
    onError: (error) => {
      toast({
        title: 'Toplu işaretleme başarısız',
        description: error.message,
        status: 'error',
        duration: 5000,
      });
    },
  });
}
