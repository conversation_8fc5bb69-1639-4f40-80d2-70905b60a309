---
type: "always_apply"
---

# PromptFlow Core Architecture & Context

## Project Overview
**PromptFlow** - AI destekli geli<PERSON>rme süreçlerini optimize eden minimalist prompt yönetim platformu.

### Dual Application Architecture
#### Ana Uygulama (/promptflow)
- **Framework**: Next.js 15.4.1 (App Router)
- **React**: 19.1.0
- **Port**: 4444
- **UI**: Tailwind CSS + shadcn/ui
- **State**: TanStack Query v5 + Zustand

#### Admin Panel (/admin)
- **Framework**: Next.js 14.2.18 (App Router)
- **React**: 18.3.1
- **Port**: 4445
- **UI**: Chakra UI + Tailwind CSS
- **Features**: User management, Analytics, System settings

### Backend Infrastructure
- **Platform**: Supabase (iqehopwgrczylqliajww)
- **Database**: PostgreSQL with RLS
- **Auth**: Supabase Auth + Admin roles
- **Realtime**: Supabase Realtime subscriptions

### Core Features
- User Plans System (Free/Professional/Enterprise)
- Context Gallery with templates
- Hashtag & Category organization
- Drag & Drop prompt management
- Real-time collaboration
- Performance optimization

## Development Rules
- Always maintain dual app compatibility
- Use TypeScript strict mode
- Implement optimistic UI updates
- Follow responsive design principles
- Maintain RLS security policies
- Update this file after major changes

## Project Structure
```
PromptFlow/
├── promptflow/          # Main application
│   ├── src/
│   │   ├── app/        # Next.js App Router
│   │   ├── components/ # React components
│   │   ├── hooks/      # Custom hooks
│   │   ├── lib/        # Utilities
│   │   └── store/      # Zustand stores
│   └── package.json
├── admin/              # Admin panel
│   ├── src/
│   │   ├── app/        # Admin pages
│   │   ├── components/ # Admin components
│   │   ├── hooks/      # Admin hooks
│   │   └── lib/        # Admin utilities
│   └── package.json
└── .augment/           # AI rules system
    └── rules/
        └── imported/
            └── rules/  # This directory
```

## Technology Compatibility
- **Node.js**: 18+ required
- **npm**: 9+ required
- **TypeScript**: 5+ strict mode
- **React**: 18.3.1 (admin) / 19.1.0 (main)
- **Next.js**: 14.2.18 (admin) / 15.4.1 (main)

## Environment Setup
```bash
# Main app development
cd promptflow
npm install
npm run dev  # Port 4444

# Admin panel development
cd admin
npm install
npm run dev  # Port 4445
```

## Key Principles
1. **Dual App Harmony**: Maintain compatibility between both applications
2. **Type Safety**: Strict TypeScript throughout
3. **Performance First**: Optimize for speed and user experience
4. **Security by Design**: RLS policies and proper authentication
5. **Scalable Architecture**: Modular and maintainable code structure
6. **Real-time Features**: Live updates and collaboration
7. **Responsive Design**: Mobile-first approach
8. **Developer Experience**: Clear patterns and documentation

## Database Schema Overview
### Core Tables
- **auth.users**: Supabase managed user authentication
- **projects**: User prompt collections
- **prompts**: Individual prompts with categorization
- **plan_types**: Plan definitions (Free/Pro/Enterprise)
- **user_plans**: User plan assignments
- **usage_stats**: Usage tracking and analytics
- **context_categories**: Template categories
- **context_templates**: Pre-built prompt templates
- **admin_users**: Admin role management

### Key Relationships
- Users → Projects (1:many)
- Projects → Prompts (1:many)
- Users → User Plans (1:1 active)
- Plan Types → User Plans (1:many)
- Context Categories → Context Templates (1:many)

## API Patterns
### Authentication Flow
1. Supabase Auth handles login/signup
2. RLS policies enforce data access
3. Admin roles checked via admin_users table
4. JWT tokens manage session state

### Data Flow
1. **Frontend** → TanStack Query → **Supabase Client**
2. **Supabase** → RLS Check → **Database**
3. **Database** → **Realtime** → **All Clients**
4. **Optimistic Updates** → **UI** → **Background Sync**

## Development History

### 2025-01-29: Major Feature Implementations

#### Dynamic Height System for Prompt Text Areas
**Feature**: Responsive dynamic height management for prompt input areas
**Components Added**:
- `use-dynamic-height.ts` - Custom hook for viewport-based height calculations
- Enhanced `SmartAutocomplete` with dynamic height support
- CSS utilities for smooth height transitions and animations

**Technical Implementation**:
- Viewport-aware height calculation (1/3 viewport height max)
- Responsive breakpoint support (mobile: 1/4, tablet/desktop: 1/3)
- Hardware-accelerated animations with cubic-bezier transitions
- Visual Viewport API support for mobile browsers
- Debounced resize handling for performance

**Performance Optimizations**:
- GPU acceleration with `transform: translateZ(0)`
- Smooth transitions with `will-change` property
- Debounced viewport calculations (150ms)
- Container queries for responsive components

#### Context Gallery "Add New" Button Fix
**Feature**: Complete Context Gallery integration with project workflow
**Components Enhanced**:
- `use-context-to-prompt.ts` - Context to prompt conversion system
- Enhanced `ContextCreationModal` with "Add to Project" option
- Updated `ContextGallery` with direct project integration

**Functionality Added**:
- Context to prompt conversion with validation
- Plan limit checking during context addition
- Batch context addition support
- Comprehensive error handling and user feedback
- Usage tracking and analytics integration

**Security Measures**:
- Plan limit validation before context addition
- Project ownership verification
- Input sanitization and validation
- Rate limiting considerations

#### Profile Page Plan Management
**Feature**: Complete user plan management interface
**Components Added**:
- Enhanced `/profile` page with plan management UI
- Plan comparison and selection interface
- Plan change validation and confirmation system

**Business Logic**:
- Plan downgrade validation (usage vs limits)
- Confirmation dialogs for plan changes
- Real-time plan status display
- Usage statistics integration
- Billing cycle management

**Security & Validation**:
- Pre-change usage validation
- Data integrity checks during plan transitions
- User confirmation for downgrades
- Automatic cache invalidation after changes

### 2025-01-29: Project Name Editing Feature Implementation
**Feature**: Secure inline project name editing with comprehensive validation
**Components Added**:
- `ProjectNameEditor` - Inline editing component with security-first approach
- `project-validation.ts` - Input validation and sanitization utilities
- `useUpdateProjectNameSecure` - Secure mutation hook with optimistic updates

**Security Measures Implemented**:
- Input validation (3-50 chars, regex pattern, XSS prevention)
- Rate limiting (client + server: 10 requests/minute)
- Database constraints and RLS policies
- Secure database function `update_project_name_secure()`
- SQL injection prevention

**Performance Optimizations**:
- Debounced validation (300ms)
- React.memo for component memoization
- Optimistic updates with rollback capability
- Smart cache management with TanStack Query

**Accessibility Features**:
- ARIA labels and live regions
- Keyboard navigation (Enter/ESC)
- Screen reader support
- Touch-friendly targets (44px minimum)

**Testing Coverage**:
- Unit tests for component behavior
- Security tests for XSS/injection prevention
- Accessibility tests for WCAG compliance
- Performance tests for debouncing and memory usage
- Integration tests for API interactions

**Files Modified**:
- `promptflow/src/components/project-name-editor.tsx` (new)
- `promptflow/src/lib/project-validation.ts` (new)
- `promptflow/src/hooks/use-projects.ts` (enhanced)
- `promptflow/src/components/project-sidebar.tsx` (integrated)
- `promptflow/src/app/globals.css` (touch targets)
- Database: Added constraints, RLS policies, secure functions

## Automatic Rules System

### How the System Works
1. **Core Reading**: This file (PROJECT_CORE.md) serves as the master guide
2. **Auto-Discovery**: System automatically finds related files via `auto_read_order` and `dependencies`
3. **History Loading**: Loads `development_history` from all related files
4. **Context Awareness**: Understands previous work and current state
5. **Auto-Updates**: Changes are automatically written to `auto_update_targets`

### Example Workflow (Context Gallery Text Area)
```yaml
Step 1: Read PROJECT_CORE.md (master guide)
Step 2: Auto-discover related files:
  - features/CONTEXT_GALLERY.md (main feature)
  - features/CONTEXT_GALLERY_ADMIN.md (admin integration)
  - fixes/CONTEXT_GALLERY_ISSUES.md (troubleshooting)
  - frontend/ADMIN_PANEL.md (admin UI)
  - backend/SUPABASE.md (database)

Step 3: Load development history from all files
Step 4: Execute development with full context
Step 5: Auto-update all related files with changes
```

### File Type Hierarchy
- **core**: Master guide (this file)
- **feature**: Main feature documentation
- **admin_feature**: Admin panel specific features
- **fixes**: Troubleshooting and error resolution
- **frontend_guide**: Frontend development rules
- **backend_guide**: Backend development rules
- **deployment_guide**: Production deployment rules

## Update Requirements
- This file auto-updates when major architectural changes occur
- All related files auto-update via the dependency system
- Development history is automatically tracked across all files
- Security patterns documented in security/SECURITY.md
- Performance optimizations tracked in performance/PERFORMANCE.md
- Testing patterns maintained in testing/TESTING.md
- TypeScript patterns updated in fixes/TYPESCRIPT_PATTERNS.md
