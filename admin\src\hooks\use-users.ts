import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { usersApi } from '@/lib/api';
import { queryKeys } from '@/lib/query-client';
import { useToast } from '@chakra-ui/react';

// Paginated users list with search and filters
export function useUsers(options: {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  status?: string;
} = {}) {
  return useQuery({
    queryKey: queryKeys.users.list(options),
    queryFn: () => usersApi.getUsers(options),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000,
    // Keep previous data while loading new page
    placeholderData: (previousData) => previousData,
  });
}

// User stats
export function useUserStats() {
  return useQuery({
    queryKey: queryKeys.users.stats,
    queryFn: usersApi.getUserStats,
    staleTime: 10 * 60 * 1000, // 10 minutes - stats change less frequently
    gcTime: 20 * 60 * 1000, // 20 minutes cache
    refetchOnWindowFocus: false,
    refetchInterval: 15 * 60 * 1000, // Refetch every 15 minutes
  });
}

// Single user detail
export function useUser(id: string) {
  return useQuery({
    queryKey: queryKeys.users.detail(id),
    queryFn: () => usersApi.getUser(id),
    enabled: !!id,
    staleTime: 3 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
}

// User mutations with optimistic updates
export function useCreateUser() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: usersApi.createUser,
    onSuccess: () => {
      // Invalidate and refetch users list
      queryClient.invalidateQueries({ queryKey: queryKeys.users.all });
      queryClient.invalidateQueries({ queryKey: queryKeys.users.stats });
      
      toast({
        title: 'User created successfully',
        status: 'success',
        duration: 3000,
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to create user',
        description: error.message,
        status: 'error',
        duration: 5000,
      });
    },
  });
}

export function useUpdateUser() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => usersApi.updateUser(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate specific user and lists
      queryClient.invalidateQueries({ queryKey: queryKeys.users.detail(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.users.all });
      
      toast({
        title: 'User updated successfully',
        status: 'success',
        duration: 3000,
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to update user',
        description: error.message,
        status: 'error',
        duration: 5000,
      });
    },
  });
}

export function useDeleteUser() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: usersApi.deleteUser,
    onSuccess: () => {
      // Invalidate users list and stats
      queryClient.invalidateQueries({ queryKey: queryKeys.users.all });
      queryClient.invalidateQueries({ queryKey: queryKeys.users.stats });
      
      toast({
        title: 'User deleted successfully',
        status: 'success',
        duration: 3000,
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to delete user',
        description: error.message,
        status: 'error',
        duration: 5000,
      });
    },
  });
}
