'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Crown, Zap, Shield, TrendingUp, AlertTriangle } from 'lucide-react'
import { useUserActivePlan, useUserLimits, usePlanTypes } from '@/hooks/use-plans'
import { PlanUpgradeModal } from './plan-upgrade-modal'
import { cn } from '@/lib/utils'

export function PlanDisplay() {
  const [showUpgradeModal, setShowUpgradeModal] = useState(false)
  const { data: activePlan, isLoading: planLoading } = useUserActivePlan()
  const { data: limits, isLoading: limitsLoading } = useUserLimits()
  const { data: planTypes } = usePlanTypes()

  if (planLoading || limitsLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="h-6 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 bg-gray-200 rounded animate-pulse w-2/3" />
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
          </div>
        </CardContent>
      </Card>
    )
  }

  const getPlanIcon = (planName: string) => {
    switch (planName) {
      case 'free':
        return <Zap className="h-5 w-5 text-blue-500" />
      case 'professional':
        return <Crown className="h-5 w-5 text-yellow-500" />
      case 'enterprise':
        return <Shield className="h-5 w-5 text-purple-500" />
      default:
        return <Zap className="h-5 w-5 text-gray-500" />
    }
  }

  const getPlanColor = (planName: string) => {
    switch (planName) {
      case 'free':
        return 'bg-blue-50 text-blue-700 border-blue-200'
      case 'professional':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200'
      case 'enterprise':
        return 'bg-purple-50 text-purple-700 border-purple-200'
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  const getUsageColor = (current: number, max: number) => {
    if (max === -1) return 'text-green-600' // Sınırsız
    const percentage = (current / max) * 100
    if (percentage >= 90) return 'text-red-600'
    if (percentage >= 70) return 'text-yellow-600'
    return 'text-green-600'
  }

  const getProgressColor = (current: number, max: number) => {
    if (max === -1) return 'bg-green-500' // Sınırsız
    const percentage = (current / max) * 100
    if (percentage >= 90) return 'bg-red-500'
    if (percentage >= 70) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  const shouldShowUpgradeWarning = () => {
    if (!limits || !activePlan) return false
    if (activePlan.plan_name === 'enterprise') return false
    
    const projectUsage = limits.max_projects === -1 ? 0 : (limits.current_projects / limits.max_projects) * 100
    const promptUsage = limits.max_prompts_per_project === -1 ? 0 : (limits.current_prompts / limits.max_prompts_per_project) * 100
    
    return projectUsage >= 80 || promptUsage >= 80
  }

  return (
    <>
      <Card className="w-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {activePlan && getPlanIcon(activePlan.plan_name)}
              <div>
                <CardTitle className="text-lg">
                  {activePlan?.display_name || 'Plan Yükleniyor...'}
                </CardTitle>
                <CardDescription>
                  Mevcut planınız ve kullanım durumunuz
                </CardDescription>
              </div>
            </div>
            {activePlan && (
              <Badge 
                variant="outline" 
                className={cn('font-medium', getPlanColor(activePlan.plan_name))}
              >
                {activePlan.status === 'active' ? 'Aktif' : activePlan.status}
              </Badge>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Kullanım İstatistikleri */}
          {limits && (
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">Projeler</span>
                  <span className={getUsageColor(limits.current_projects, limits.max_projects)}>
                    {limits.current_projects} / {limits.max_projects === -1 ? '∞' : limits.max_projects}
                  </span>
                </div>
                <Progress 
                  value={limits.max_projects === -1 ? 0 : (limits.current_projects / limits.max_projects) * 100}
                  className="h-2"
                  style={{
                    '--progress-background': getProgressColor(limits.current_projects, limits.max_projects)
                  } as React.CSSProperties}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">Prompt&apos;lar (Proje başına max)</span>
                  <span className={getUsageColor(limits.current_prompts, limits.max_prompts_per_project)}>
                    {limits.current_prompts} / {limits.max_prompts_per_project === -1 ? '∞' : limits.max_prompts_per_project}
                  </span>
                </div>
                <Progress 
                  value={limits.max_prompts_per_project === -1 ? 0 : (limits.current_prompts / limits.max_prompts_per_project) * 100}
                  className="h-2"
                  style={{
                    '--progress-background': getProgressColor(limits.current_prompts, limits.max_prompts_per_project)
                  } as React.CSSProperties}
                />
              </div>
            </div>
          )}

          {/* Upgrade Uyarısı */}
          {shouldShowUpgradeWarning() && (
            <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <div className="flex-1">
                <p className="text-sm font-medium text-yellow-800">
                  Limitinize yaklaşıyorsunuz
                </p>
                <p className="text-xs text-yellow-700">
                  Daha fazla özellik için planınızı yükseltin
                </p>
              </div>
            </div>
          )}

          <Separator />

          {/* Plan Özellikleri */}
          {activePlan?.features && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-900">Plan Özellikleri</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                {activePlan.features.context_gallery && (
                  <div className="flex items-center gap-1">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                    <span>Context Gallery</span>
                  </div>
                )}
                {activePlan.features.api_access && (
                  <div className="flex items-center gap-1">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                    <span>API Erişimi</span>
                  </div>
                )}
                {activePlan.features.team_features && (
                  <div className="flex items-center gap-1">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                    <span>Takım Özellikleri</span>
                  </div>
                )}
                {activePlan.features.advanced_analytics && (
                  <div className="flex items-center gap-1">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                    <span>Gelişmiş Analitik</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Upgrade Butonu */}
          {activePlan?.plan_name !== 'enterprise' && (
            <Button 
              onClick={() => setShowUpgradeModal(true)}
              className="w-full"
              variant={shouldShowUpgradeWarning() ? "default" : "outline"}
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              Planı Yükselt
            </Button>
          )}
        </CardContent>
      </Card>

      <PlanUpgradeModal 
        open={showUpgradeModal}
        onOpenChange={setShowUpgradeModal}
        currentPlan={activePlan}
        planTypes={planTypes || []}
      />
    </>
  )
}
