// Tree-shaken Chakra UI imports to reduce bundle size
// Import only the components we actually use

// Layout components
export {
  Box,
  Container,
  Flex,
  Grid,
  GridItem,
  HStack,
  VStack,
  Stack,
  Spacer,
  Center,
  Square,
  Circle,
} from '@chakra-ui/react';

// Typography
export {
  Heading,
  Text,
} from '@chakra-ui/react';

// Form components
export {
  Button,
  IconButton,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  Select,
  Textarea,
  FormControl,
  FormLabel,
  FormErrorMessage,
  FormHelperText,
} from '@chakra-ui/react';

// Data display
export {
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Badge,
  Avatar,
  AvatarGroup,
  Card,
  CardHeader,
  CardBody,
  CardFooter,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Tag,
  TagLabel,
  TagCloseButton,
} from '@chakra-ui/react';

// Feedback
export {
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Spinner,
  Progress,
  CircularProgress,
  CircularProgressLabel,
  Skeleton,
  SkeletonText,
  SkeletonCircle,
  useToast,
} from '@chakra-ui/react';

// Overlay
export {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  Drawer,
  DrawerBody,
  DrawerFooter,
  DrawerHeader,
  DrawerOverlay,
  DrawerContent,
  DrawerCloseButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  MenuItemOption,
  MenuGroup,
  MenuOptionGroup,
  MenuDivider,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverHeader,
  PopoverBody,
  PopoverFooter,
  PopoverArrow,
  PopoverCloseButton,
  Tooltip,
} from '@chakra-ui/react';

// Navigation
export {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
  Link,
} from '@chakra-ui/react';

// Media and icons
export {
  Image,
  Icon,
} from '@chakra-ui/react';

// Layout utilities
export {
  SimpleGrid,
  Wrap,
  WrapItem,
  AspectRatio,
  Divider,
} from '@chakra-ui/react';

// Hooks
export {
  useColorModeValue,
  useDisclosure,
  useBreakpointValue,
  useMediaQuery,
  useClipboard,
  useBoolean,
  useCounter,
  useLocalStorage,
} from '@chakra-ui/react';
