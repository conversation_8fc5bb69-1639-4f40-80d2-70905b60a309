'use client';

import { useEffect, useState, memo } from 'react';
import {
  Box,
  Badge,
  HStack,
  Text,
  Tooltip,
  useColorModeValue,
  VStack,
  Progress,
} from '@chakra-ui/react';
import { Activity, Clock, Zap } from 'lucide-react';

interface PerformanceMetrics {
  renderTime: number;
  queryTime: number;
  bundleSize: number;
  memoryUsage: number;
  cacheHitRate: number;
}

interface PerformanceMonitorProps {
  showDetails?: boolean;
  position?: 'top-right' | 'bottom-right' | 'bottom-left';
}

export const PerformanceMonitor = memo(function PerformanceMonitor({
  showDetails = false,
  position = 'bottom-right',
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    queryTime: 0,
    bundleSize: 0,
    memoryUsage: 0,
    cacheHitRate: 0,
  });

  const [isVisible, setIsVisible] = useState(false);

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV === 'development') {
      setIsVisible(true);
      startMonitoring();
    }
  }, []);

  const startMonitoring = () => {
    const updateMetrics = () => {
      // Measure render performance
      const renderStart = performance.now();
      
      // Simulate component render measurement
      requestAnimationFrame(() => {
        const renderEnd = performance.now();
        const renderTime = renderEnd - renderStart;

        // Get memory usage (if available)
        const memoryInfo = (performance as any).memory;
        const memoryUsage = memoryInfo 
          ? (memoryInfo.usedJSHeapSize / memoryInfo.totalJSHeapSize) * 100
          : 0;

        // Calculate cache hit rate from TanStack Query
        const queryClient = (window as any).__REACT_QUERY_CLIENT__;
        const cacheHitRate = queryClient 
          ? calculateCacheHitRate(queryClient)
          : 0;

        setMetrics(prev => ({
          ...prev,
          renderTime: Math.round(renderTime * 100) / 100,
          memoryUsage: Math.round(memoryUsage),
          cacheHitRate: Math.round(cacheHitRate),
        }));
      });
    };

    // Update metrics every 2 seconds
    const interval = setInterval(updateMetrics, 2000);
    updateMetrics(); // Initial measurement

    return () => clearInterval(interval);
  };

  const calculateCacheHitRate = (queryClient: any): number => {
    try {
      const cache = queryClient.getQueryCache();
      const queries = cache.getAll();
      
      if (queries.length === 0) return 0;
      
      const cachedQueries = queries.filter((query: any) => 
        query.state.data !== undefined && !query.state.isLoading
      );
      
      return (cachedQueries.length / queries.length) * 100;
    } catch {
      return 0;
    }
  };

  const getPerformanceStatus = (value: number, thresholds: [number, number]) => {
    if (value <= thresholds[0]) return 'green';
    if (value <= thresholds[1]) return 'yellow';
    return 'red';
  };

  const positionStyles = {
    'top-right': { top: 4, right: 4 },
    'bottom-right': { bottom: 4, right: 4 },
    'bottom-left': { bottom: 4, left: 4 },
  };

  if (!isVisible) return null;

  return (
    <Box
      position="fixed"
      {...positionStyles[position]}
      bg={bgColor}
      border="1px"
      borderColor={borderColor}
      borderRadius="md"
      p={3}
      shadow="lg"
      zIndex={9999}
      minW="200px"
    >
      <VStack spacing={2} align="stretch">
        <HStack justify="space-between">
          <HStack spacing={1}>
            <Activity size={14} />
            <Text fontSize="xs" fontWeight="bold">
              Performance
            </Text>
          </HStack>
          <Badge
            colorScheme={getPerformanceStatus(metrics.renderTime, [16, 33])}
            size="sm"
          >
            {metrics.renderTime < 16 ? 'Good' : metrics.renderTime < 33 ? 'Fair' : 'Poor'}
          </Badge>
        </HStack>

        {showDetails && (
          <>
            <VStack spacing={1} align="stretch">
              <HStack justify="space-between">
                <Text fontSize="xs">Render Time:</Text>
                <Text fontSize="xs" fontWeight="bold">
                  {metrics.renderTime}ms
                </Text>
              </HStack>
              
              <HStack justify="space-between">
                <Text fontSize="xs">Memory:</Text>
                <Text fontSize="xs" fontWeight="bold">
                  {metrics.memoryUsage}%
                </Text>
              </HStack>
              
              <HStack justify="space-between">
                <Text fontSize="xs">Cache Hit:</Text>
                <Text fontSize="xs" fontWeight="bold">
                  {metrics.cacheHitRate}%
                </Text>
              </HStack>
            </VStack>

            <Progress
              value={metrics.cacheHitRate}
              size="xs"
              colorScheme="green"
              bg="gray.200"
            />
          </>
        )}

        <HStack spacing={2} justify="center">
          <Tooltip label={`Render: ${metrics.renderTime}ms`}>
            <Badge
              colorScheme={getPerformanceStatus(metrics.renderTime, [16, 33])}
              size="xs"
            >
              <Clock size={10} />
            </Badge>
          </Tooltip>
          
          <Tooltip label={`Memory: ${metrics.memoryUsage}%`}>
            <Badge
              colorScheme={getPerformanceStatus(metrics.memoryUsage, [70, 85])}
              size="xs"
            >
              <Zap size={10} />
            </Badge>
          </Tooltip>
          
          <Tooltip label={`Cache: ${metrics.cacheHitRate}%`}>
            <Badge
              colorScheme={metrics.cacheHitRate > 80 ? 'green' : 'yellow'}
              size="xs"
            >
              {metrics.cacheHitRate}%
            </Badge>
          </Tooltip>
        </HStack>
      </VStack>
    </Box>
  );
});
