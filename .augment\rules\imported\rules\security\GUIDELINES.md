---
type: "manual"
---

# PromptFlow Security Guidelines

## Authentication & Authorization
### Supabase Auth Integration
```typescript
// Secure auth patterns
const { data: { user }, error } = await supabase.auth.getUser()

// Auth guards
const AuthGuard = ({ children }: { children: ReactNode }) => {
  const { user, loading } = useAuth()
  
  if (loading) return <LoadingSpinner />
  if (!user) return <LoginForm />
  
  return <>{children}</>
}

// Admin protection
const AdminGuard = ({ children }: { children: ReactNode }) => {
  const { isAdmin, loading } = useAdminAuth()
  
  if (loading) return <LoadingSpinner />
  if (!isAdmin) return <AccessDenied />
  
  return <>{children}</>
}
```

### Row Level Security (RLS)
```sql
-- User data isolation
CREATE POLICY "Users own data" ON projects
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users own prompts" ON prompts
  FOR ALL USING (auth.uid() = user_id);

-- Admin access
CREATE POLICY "Admins full access" ON projects
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM admin_users 
      WHERE user_id = auth.uid() 
      AND role IN ('admin', 'super_admin')
    )
  );

-- Plan access control
CREATE POLICY "Users view own plans" ON user_plans
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users update own plans" ON user_plans
  FOR UPDATE USING (auth.uid() = user_id);
```

## Data Protection
### Input Validation
```typescript
// Sanitize user inputs
import DOMPurify from 'dompurify'

const sanitizeInput = (input: string): string => {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  })
}

// Validate data schemas
import { z } from 'zod'

const promptSchema = z.object({
  prompt_text: z.string().min(1).max(10000),
  title: z.string().max(200).optional(),
  tags: z.array(z.string()).max(10),
  category: z.string().max(100).optional()
})

const validatePrompt = (data: unknown) => {
  return promptSchema.parse(data)
}
```

### SQL Injection Prevention
```typescript
// Always use parameterized queries
const { data, error } = await supabase
  .from('prompts')
  .select('*')
  .eq('project_id', projectId)  // Safe parameter
  .ilike('prompt_text', `%${searchTerm}%`)  // Safe parameter

// Never use string concatenation
// BAD: .select(`* FROM prompts WHERE id = ${id}`)
// GOOD: .eq('id', id)
```

## API Security
### Rate Limiting
```typescript
// Implement rate limiting
const rateLimiter = new Map()

const checkRateLimit = (userId: string, action: string) => {
  const key = `${userId}:${action}`
  const now = Date.now()
  const windowMs = 60 * 1000 // 1 minute
  const maxRequests = 100

  const requests = rateLimiter.get(key) || []
  const validRequests = requests.filter(
    (time: number) => now - time < windowMs
  )

  if (validRequests.length >= maxRequests) {
    throw new Error('Rate limit exceeded')
  }

  validRequests.push(now)
  rateLimiter.set(key, validRequests)
}
```

### CORS Configuration
```typescript
// Next.js API routes security headers
export async function GET(request: Request) {
  const response = new Response(data)

  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')

  return response
}
```

## Environment Security
### Environment Variables
```bash
# Production environment
NEXT_PUBLIC_SUPABASE_URL=https://iqehopwgrczylqliajww.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIs...

# Admin-only (never expose to client)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIs...
ADMIN_SECRET_KEY=your-secret-key

# Database encryption
DATABASE_ENCRYPTION_KEY=your-encryption-key
```

### Secrets Management
```typescript
// Secure secret handling
const getSecret = (key: string): string => {
  const secret = process.env[key]
  if (!secret) {
    throw new Error(`Missing required secret: ${key}`)
  }
  return secret
}

// Client-side security
const isClientSide = typeof window !== 'undefined'
if (isClientSide && process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.error('Service role key exposed to client!')
}
```

## Content Security
### XSS Prevention
```typescript
// Escape user content
const escapeHtml = (text: string): string => {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

// Safe HTML rendering
const SafeContent = ({ content }: { content: string }) => {
  const sanitized = DOMPurify.sanitize(content)
  return <div dangerouslySetInnerHTML={{ __html: sanitized }} />
}
```

### File Upload Security
```typescript
// Validate file types and sizes
const validateFile = (file: File): boolean => {
  const allowedTypes = ['image/jpeg', 'image/png', 'text/plain']
  const maxSize = 5 * 1024 * 1024 // 5MB

  if (!allowedTypes.includes(file.type)) {
    throw new Error('Invalid file type')
  }

  if (file.size > maxSize) {
    throw new Error('File too large')
  }

  return true
}
```

## Audit & Monitoring
### Security Logging
```typescript
// Log security events
const logSecurityEvent = async (event: {
  type: 'auth_failure' | 'unauthorized_access' | 'data_breach'
  userId?: string
  ip: string
  userAgent: string
  details: Record<string, any>
}) => {
  await supabase.from('security_logs').insert({
    event_type: event.type,
    user_id: event.userId,
    ip_address: event.ip,
    user_agent: event.userAgent,
    event_details: event.details,
    created_at: new Date().toISOString()
  })
}
```

### Vulnerability Scanning
```bash
# Regular security audits
npm audit
npm audit fix

# Dependency scanning
npx audit-ci --moderate
```

## Update Requirements
- Update this file when implementing new security measures
- Document security incidents and responses
- Maintain compliance requirements
- Keep vulnerability assessments current
- Update rate limiting rules when scaling
- Document new audit procedures
