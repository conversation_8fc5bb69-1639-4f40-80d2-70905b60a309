'use client'

import { useState, useRef, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { X, Hash, Plus } from 'lucide-react'
import { cn } from '@/lib/utils'
import { 
  cleanHashtag, 
  formatHashtag, 
  isValidHashtag,
  getHashtagSuggestions 
} from '@/lib/hashtag-utils'

interface HashtagInputProps {
  hashtags: string[]
  onHashtagsChange: (hashtags: string[]) => void
  suggestions?: string[]
  placeholder?: string
  className?: string
  maxTags?: number
  disabled?: boolean
}

export function HashtagInput({
  hashtags,
  onHashtagsChange,
  suggestions = [],
  placeholder = "Etiket ekleyin... (örn: #frontend, #api)",
  className,
  maxTags = 10,
  disabled = false
}: HashtagInputProps) {
  const [inputValue, setInputValue] = useState('')
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1)
  const inputRef = useRef<HTMLInputElement>(null)
  const suggestionsRef = useRef<HTMLDivElement>(null)

  // Filter suggestions based on input
  const filteredSuggestions = getHashtagSuggestions(inputValue, suggestions, 5)
    .filter(suggestion => !hashtags.includes(suggestion))

  // Add hashtag
  const addHashtag = (hashtag: string) => {
    const cleaned = cleanHashtag(hashtag)
    if (!cleaned || !isValidHashtag(cleaned)) return
    
    const formatted = formatHashtag(cleaned)
    if (hashtags.includes(formatted) || hashtags.length >= maxTags) return
    
    onHashtagsChange([...hashtags, formatted])
    setInputValue('')
    setShowSuggestions(false)
    setSelectedSuggestionIndex(-1)
  }

  // Remove hashtag
  const removeHashtag = (index: number) => {
    const newHashtags = hashtags.filter((_, i) => i !== index)
    onHashtagsChange(newHashtags)
  }

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setInputValue(value)
    setShowSuggestions(value.length > 0)
    setSelectedSuggestionIndex(-1)
  }

  // Handle key down
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      if (selectedSuggestionIndex >= 0 && filteredSuggestions[selectedSuggestionIndex]) {
        addHashtag(filteredSuggestions[selectedSuggestionIndex])
      } else if (inputValue.trim()) {
        addHashtag(inputValue.trim())
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault()
      setSelectedSuggestionIndex(prev => 
        prev < filteredSuggestions.length - 1 ? prev + 1 : prev
      )
    } else if (e.key === 'ArrowUp') {
      e.preventDefault()
      setSelectedSuggestionIndex(prev => prev > 0 ? prev - 1 : -1)
    } else if (e.key === 'Escape') {
      setShowSuggestions(false)
      setSelectedSuggestionIndex(-1)
    } else if (e.key === 'Backspace' && !inputValue && hashtags.length > 0) {
      // Remove last hashtag if input is empty
      removeHashtag(hashtags.length - 1)
    }
  }

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: string) => {
    addHashtag(suggestion)
    inputRef.current?.focus()
  }

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current && 
        !suggestionsRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div className={cn("space-y-2", className)}>
      {/* Hashtag Display */}
      {hashtags.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {hashtags.map((hashtag, index) => (
            <Badge
              key={index}
              variant="secondary"
              className="flex items-center gap-1 text-xs bg-blue-100 text-blue-800 hover:bg-blue-200"
            >
              <Hash className="w-3 h-3" />
              {hashtag.replace('#', '')}
              {!disabled && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 w-3 h-3 hover:bg-transparent"
                  onClick={() => removeHashtag(index)}
                >
                  <X className="w-2 h-2" />
                </Button>
              )}
            </Badge>
          ))}
        </div>
      )}

      {/* Input Field */}
      <div className="relative">
        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onFocus={() => setShowSuggestions(inputValue.length > 0)}
              placeholder={hashtags.length >= maxTags ? `Maksimum ${maxTags} etiket` : placeholder}
              disabled={disabled || hashtags.length >= maxTags}
              className="pl-10"
            />
          </div>
          {inputValue.trim() && (
            <Button
              type="button"
              size="sm"
              onClick={() => addHashtag(inputValue.trim())}
              disabled={disabled || hashtags.length >= maxTags}
              className="shrink-0"
            >
              <Plus className="w-4 h-4" />
            </Button>
          )}
        </div>

        {/* Suggestions Dropdown */}
        {showSuggestions && filteredSuggestions.length > 0 && (
          <div
            ref={suggestionsRef}
            className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-40 overflow-y-auto"
          >
            {filteredSuggestions.map((suggestion, index) => (
              <button
                key={suggestion}
                type="button"
                className={cn(
                  "w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2",
                  selectedSuggestionIndex === index && "bg-blue-50"
                )}
                onClick={() => handleSuggestionClick(suggestion)}
              >
                <Hash className="w-3 h-3 text-gray-400" />
                {suggestion.replace('#', '')}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Helper Text */}
      <div className="text-xs text-gray-500">
        {hashtags.length}/{maxTags} etiket • Eklemek için Enter&apos;a basın • # öneki kullanın
      </div>
    </div>
  )
}
