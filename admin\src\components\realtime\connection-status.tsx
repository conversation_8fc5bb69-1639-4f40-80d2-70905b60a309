'use client';

import { Box, HStack, Text, Icon, Tooltip, useColorModeValue } from '@chakra-ui/react';
import { Wifi, WifiOff, AlertCircle, Loader } from 'lucide-react';
import { useConnectionStatus } from './realtime-provider';

export function ConnectionStatus() {
  const { isConnected, connectionStatus } = useConnectionStatus();

  // Don't render during SSR or if context is not available
  if (typeof window === 'undefined') {
    return null;
  }

  const getStatusConfig = () => {
    switch (connectionStatus) {
      case 'connected':
        return {
          icon: Wifi,
          color: 'green.500',
          text: 'Bağlı',
          description: 'Gerçek zamanlı güncellemeler aktif',
        };
      case 'connecting':
        return {
          icon: Loader,
          color: 'yellow.500',
          text: 'Bağlanıyor',
          description: 'Gerçek zamanlı bağlantı kuruluyor',
        };
      case 'disconnected':
        return {
          icon: WifiOff,
          color: 'red.500',
          text: '<PERSON>ğlantı Yok',
          description: 'Gerçek zamanlı güncellemeler devre dışı',
        };
      case 'error':
        return {
          icon: AlertCircle,
          color: 'red.500',
          text: 'Hata',
          description: 'Bağlantı hatası oluştu',
        };
      default:
        return {
          icon: WifiOff,
          color: 'gray.500',
          text: 'Bilinmiyor',
          description: 'Bağlantı durumu bilinmiyor',
        };
    }
  };

  const config = getStatusConfig();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  return (
    <Tooltip label={config.description} placement="top">
      <Box
        position="fixed"
        bottom={4}
        right={4}
        bg={bgColor}
        border="1px solid"
        borderColor={borderColor}
        borderRadius="lg"
        shadow="md"
        p={3}
        zIndex={1000}
        cursor="pointer"
      >
        <HStack spacing={2}>
          <Icon
            as={config.icon}
            w={4}
            h={4}
            color={config.color}
            className={connectionStatus === 'connecting' ? 'animate-spin' : ''}
          />
          <Text fontSize="sm" fontWeight="medium" color={config.color}>
            {config.text}
          </Text>
        </HStack>
      </Box>
    </Tooltip>
  );
}
