'use client';

import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Badge,
  HStack,
  VStack,
  Icon,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Card,
  CardHeader,
  CardBody,
  useToast,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Flex,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton,
  Textarea,
  Switch,
  Tag,
  TagLabel,
  TagCloseButton,
  Wrap,
  WrapItem,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Progress,
} from '@chakra-ui/react';
import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { AdminLayout } from '@/components/admin-layout';
import { 
  Search, 
  Filter, 
  Plus, 
  MoreVertical, 
  Edit, 
  Trash2, 
  BarChart3, 
  Calendar,
  ArrowLeft,
  Eye,
  Users,
  Activity,
  Star,
  Copy,
  Heart,
  TrendingUp,
  FileText,
  Code
} from 'lucide-react';

interface Prompt {
  id: string;
  project_id: string;
  user_id: string;
  prompt_text: string;
  title: string | null;
  description: string | null;
  category: string | null;
  tags: string[];
  order_index: number;
  is_used: boolean;
  is_favorite: boolean;
  usage_count: number;
  last_used_at: string | null;
  task_code: string | null;
  created_at: string;
  updated_at: string;
  project_name?: string;
  user_email?: string;
}

interface PromptStats {
  totalPrompts: number;
  usedPrompts: number;
  favoritePrompts: number;
  averageUsage: number;
}

export default function PromptsPage() {
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [filteredPrompts, setFilteredPrompts] = useState<Prompt[]>([]);
  const [stats, setStats] = useState<PromptStats>({
    totalPrompts: 0,
    usedPrompts: 0,
    favoritePrompts: 0,
    averageUsage: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterUsage, setFilterUsage] = useState('all');
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | null>(null);
  const [editingPrompt, setEditingPrompt] = useState<Prompt | null>(null);
  const [categories, setCategories] = useState<string[]>([]);
  
  const router = useRouter();
  const toast = useToast();
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure();
  const { isOpen: isViewOpen, onOpen: onViewOpen, onClose: onViewClose } = useDisclosure();
  const cancelRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    fetchPrompts();
    fetchStats();
  }, []);

  useEffect(() => {
    filterPrompts();
  }, [prompts, searchTerm, filterCategory, filterUsage]);

  const fetchPrompts = async () => {
    try {
      // Prompt'ları proje ve kullanıcı bilgileri ile birlikte al
      const { data: promptsData, error } = await supabase
        .from('prompts')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      // Her prompt için proje adı ve kullanıcı email'i al
      const promptsWithDetails = await Promise.all(
        promptsData.map(async (prompt) => {
          // Proje adını al
          const { data: projectData } = await supabase
            .from('projects')
            .select('name')
            .eq('id', prompt.project_id)
            .single();

          // Kullanıcı email'ini al (user_id null değilse)
          let userData = null;
          if (prompt.user_id) {
            try {
              const result = await supabase.auth.admin.getUserById(prompt.user_id);
              userData = result.data;
            } catch (error) {
              console.warn(`User data alınamadı (user_id: ${prompt.user_id}):`, error);
            }
          }

          return {
            ...prompt,
            project_name: projectData?.name || 'Bilinmeyen Proje',
            user_email: userData?.user?.email || 'Bilinmeyen Kullanıcı',
          };
        })
      );

      setPrompts(promptsWithDetails);

      // Kategorileri çıkar
      const uniqueCategories = [...new Set(
        promptsWithDetails
          .map(p => p.category)
          .filter(Boolean)
      )] as string[];
      setCategories(uniqueCategories);

    } catch (error) {
      console.error('Prompts fetch error:', error);
      toast({
        title: 'Hata',
        description: 'Prompt\'lar yüklenirken bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const { count: totalPrompts } = await supabase
        .from('prompts')
        .select('*', { count: 'exact', head: true });

      const { count: usedPrompts } = await supabase
        .from('prompts')
        .select('*', { count: 'exact', head: true })
        .eq('is_used', true);

      const { count: favoritePrompts } = await supabase
        .from('prompts')
        .select('*', { count: 'exact', head: true })
        .eq('is_favorite', true);

      // Ortalama kullanım sayısı
      const { data: usageData } = await supabase
        .from('prompts')
        .select('usage_count');

      const totalUsage = usageData?.reduce((sum, item) => sum + item.usage_count, 0) || 0;
      const averageUsage = totalPrompts ? totalUsage / totalPrompts : 0;

      setStats({
        totalPrompts: totalPrompts || 0,
        usedPrompts: usedPrompts || 0,
        favoritePrompts: favoritePrompts || 0,
        averageUsage: Math.round(averageUsage * 10) / 10,
      });
    } catch (error) {
      console.error('Stats fetch error:', error);
    }
  };

  const filterPrompts = () => {
    let filtered = prompts;

    // Arama filtresi
    if (searchTerm) {
      filtered = filtered.filter(prompt =>
        prompt.prompt_text.toLowerCase().includes(searchTerm.toLowerCase()) ||
        prompt.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        prompt.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        prompt.project_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        prompt.user_email?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Kategori filtresi
    if (filterCategory !== 'all') {
      filtered = filtered.filter(prompt => prompt.category === filterCategory);
    }

    // Kullanım filtresi
    if (filterUsage !== 'all') {
      if (filterUsage === 'used') {
        filtered = filtered.filter(prompt => prompt.is_used);
      } else if (filterUsage === 'unused') {
        filtered = filtered.filter(prompt => !prompt.is_used);
      } else if (filterUsage === 'favorite') {
        filtered = filtered.filter(prompt => prompt.is_favorite);
      }
    }

    setFilteredPrompts(filtered);
  };

  const handleDeletePrompt = async () => {
    if (!selectedPrompt) return;

    try {
      const { error } = await supabase
        .from('prompts')
        .delete()
        .eq('id', selectedPrompt.id);

      if (error) {
        throw error;
      }

      toast({
        title: 'Başarılı',
        description: 'Prompt başarıyla silindi.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      fetchPrompts();
      fetchStats();
      onDeleteClose();
    } catch (error) {
      console.error('Delete prompt error:', error);
      toast({
        title: 'Hata',
        description: 'Prompt silinirken bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleUpdatePrompt = async () => {
    if (!editingPrompt) return;

    try {
      const { error } = await supabase
        .from('prompts')
        .update({
          title: editingPrompt.title,
          description: editingPrompt.description,
          prompt_text: editingPrompt.prompt_text,
          category: editingPrompt.category,
          tags: editingPrompt.tags,
          order_index: editingPrompt.order_index,
          is_used: editingPrompt.is_used,
          is_favorite: editingPrompt.is_favorite,
          task_code: editingPrompt.task_code,
        })
        .eq('id', editingPrompt.id);

      if (error) {
        throw error;
      }

      toast({
        title: 'Başarılı',
        description: 'Prompt başarıyla güncellendi.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      fetchPrompts();
      fetchStats();
      onEditClose();
    } catch (error) {
      console.error('Update prompt error:', error);
      toast({
        title: 'Hata',
        description: 'Prompt güncellenirken bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleCopyPrompt = (prompt: Prompt) => {
    navigator.clipboard.writeText(prompt.prompt_text);
    toast({
      title: 'Kopyalandı',
      description: 'Prompt metni panoya kopyalandı.',
      status: 'success',
      duration: 2000,
      isClosable: true,
    });
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Hiç';
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getUsageColor = (count: number) => {
    if (count === 0) return 'gray';
    if (count < 5) return 'yellow';
    if (count < 10) return 'orange';
    return 'green';
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <Box minH="100vh" bg="gray.50" display="flex" alignItems="center" justifyContent="center">
          <Text>Yükleniyor...</Text>
        </Box>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Container maxW="7xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <Flex align="center" justify="space-between">
            <VStack align="start" spacing={0}>
              <Heading size="lg">Prompt Yönetimi</Heading>
              <Text color="gray.600">Sistem promptlarını yönetin</Text>
            </VStack>
          </Flex>

          {/* Stats */}
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={BarChart3} w={4} h={4} />
                      <Text>Toplam Prompt</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber color="blue.600">{stats.totalPrompts}</StatNumber>
                </Stat>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={Activity} w={4} h={4} />
                      <Text>Kullanılan</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber color="green.600">{stats.usedPrompts}</StatNumber>
                  <StatHelpText>
                    {stats.totalPrompts > 0 
                      ? `${Math.round((stats.usedPrompts / stats.totalPrompts) * 100)}%`
                      : '0%'
                    }
                  </StatHelpText>
                </Stat>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={Heart} w={4} h={4} />
                      <Text>Favoriler</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber color="red.600">{stats.favoritePrompts}</StatNumber>
                </Stat>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={TrendingUp} w={4} h={4} />
                      <Text>Ort. Kullanım</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber color="purple.600">{stats.averageUsage}</StatNumber>
                  <StatHelpText>Per prompt</StatHelpText>
                </Stat>
              </CardBody>
            </Card>
          </SimpleGrid>

          {/* Filters */}
          <Card>
            <CardHeader>
              <Heading size="md">Prompt Listesi</Heading>
            </CardHeader>
            <CardBody>
              <HStack spacing={4} mb={6}>
                <InputGroup maxW="300px">
                  <InputLeftElement>
                    <Icon as={Search} color="gray.400" />
                  </InputLeftElement>
                  <Input
                    placeholder="Prompt ara..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </InputGroup>
                <Select
                  maxW="200px"
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                >
                  <option value="all">Tüm Kategoriler</option>
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </Select>
                <Select
                  maxW="200px"
                  value={filterUsage}
                  onChange={(e) => setFilterUsage(e.target.value)}
                >
                  <option value="all">Tüm Durumlar</option>
                  <option value="used">Kullanılan</option>
                  <option value="unused">Kullanılmayan</option>
                  <option value="favorite">Favoriler</option>
                </Select>
              </HStack>

              <TableContainer>
                <Table variant="simple">
                  <Thead>
                    <Tr>
                      <Th>Prompt</Th>
                      <Th>Proje</Th>
                      <Th>Kategori</Th>
                      <Th>Kullanım</Th>
                      <Th>Durum</Th>
                      <Th>Oluşturulma</Th>
                      <Th>İşlemler</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {filteredPrompts.map((prompt) => (
                      <Tr key={prompt.id}>
                        <Td maxW="300px">
                          <VStack align="start" spacing={1}>
                            <Text fontWeight="medium" noOfLines={1}>
                              {prompt.title || 'Başlıksız Prompt'}
                            </Text>
                            <Text fontSize="sm" color="gray.600" noOfLines={2}>
                              {prompt.prompt_text}
                            </Text>
                            {prompt.tags.length > 0 && (
                              <Wrap>
                                {prompt.tags.slice(0, 2).map((tag, index) => (
                                  <WrapItem key={index}>
                                    <Tag size="sm" colorScheme="blue">
                                      <TagLabel>{tag}</TagLabel>
                                    </Tag>
                                  </WrapItem>
                                ))}
                                {prompt.tags.length > 2 && (
                                  <WrapItem>
                                    <Tag size="sm" colorScheme="gray">
                                      <TagLabel>+{prompt.tags.length - 2}</TagLabel>
                                    </Tag>
                                  </WrapItem>
                                )}
                              </Wrap>
                            )}
                          </VStack>
                        </Td>
                        <Td>
                          <Text fontSize="sm">{prompt.project_name}</Text>
                          <Text fontSize="xs" color="gray.500">{prompt.user_email}</Text>
                        </Td>
                        <Td>
                          {prompt.category ? (
                            <Badge colorScheme="purple" variant="subtle">
                              {prompt.category}
                            </Badge>
                          ) : (
                            <Text fontSize="sm" color="gray.500">-</Text>
                          )}
                        </Td>
                        <Td>
                          <VStack align="start" spacing={1}>
                            <Badge colorScheme={getUsageColor(prompt.usage_count)}>
                              {prompt.usage_count} kez
                            </Badge>
                            <Text fontSize="xs" color="gray.500">
                              Son: {formatDate(prompt.last_used_at)}
                            </Text>
                          </VStack>
                        </Td>
                        <Td>
                          <HStack>
                            {prompt.is_used && (
                              <Badge colorScheme="green" size="sm">Kullanılmış</Badge>
                            )}
                            {prompt.is_favorite && (
                              <Icon as={Heart} w={4} h={4} color="red.500" />
                            )}
                          </HStack>
                        </Td>
                        <Td>
                          <Text fontSize="sm" color="gray.600">
                            {formatDate(prompt.created_at)}
                          </Text>
                        </Td>
                        <Td>
                          <Menu>
                            <MenuButton
                              as={IconButton}
                              icon={<Icon as={MoreVertical} />}
                              variant="ghost"
                              size="sm"
                            />
                            <MenuList>
                              <MenuItem
                                icon={<Icon as={Eye} />}
                                onClick={() => {
                                  setSelectedPrompt(prompt);
                                  onViewOpen();
                                }}
                              >
                                Görüntüle
                              </MenuItem>
                              <MenuItem
                                icon={<Icon as={Copy} />}
                                onClick={() => handleCopyPrompt(prompt)}
                              >
                                Kopyala
                              </MenuItem>
                              <MenuItem
                                icon={<Icon as={Edit} />}
                                onClick={() => {
                                  setEditingPrompt(prompt);
                                  onEditOpen();
                                }}
                              >
                                Düzenle
                              </MenuItem>
                              <MenuItem
                                icon={<Icon as={Trash2} />}
                                color="red.500"
                                onClick={() => {
                                  setSelectedPrompt(prompt);
                                  onDeleteOpen();
                                }}
                              >
                                Sil
                              </MenuItem>
                            </MenuList>
                          </Menu>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </TableContainer>
            </CardBody>
          </Card>
        </VStack>
      </Container>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        isOpen={isDeleteOpen}
        leastDestructiveRef={cancelRef}
        onClose={onDeleteClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              Promptu Sil
            </AlertDialogHeader>
            <AlertDialogBody>
              <Text>
                Bu promptu silmek istediğinizden emin misiniz?
              </Text>
              <Text color="red.500" fontSize="sm" mt={2}>
                Bu işlem geri alınamaz.
              </Text>
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onDeleteClose}>
                İptal
              </Button>
              <Button colorScheme="red" onClick={handleDeletePrompt} ml={3}>
                Sil
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>

      {/* Edit Prompt Modal */}
      <Modal isOpen={isEditOpen} onClose={onEditClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Prompt Düzenle</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {editingPrompt && (
              <VStack spacing={4}>
                <FormControl>
                  <FormLabel>Başlık</FormLabel>
                  <Input
                    value={editingPrompt.title || ''}
                    onChange={(e) => setEditingPrompt({
                      ...editingPrompt,
                      title: e.target.value
                    })}
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>Açıklama</FormLabel>
                  <Textarea
                    value={editingPrompt.description || ''}
                    onChange={(e) => setEditingPrompt({
                      ...editingPrompt,
                      description: e.target.value
                    })}
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>Prompt Metni</FormLabel>
                  <Textarea
                    value={editingPrompt.prompt_text}
                    onChange={(e) => setEditingPrompt({
                      ...editingPrompt,
                      prompt_text: e.target.value
                    })}
                    rows={6}
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>Kategori</FormLabel>
                  <Input
                    value={editingPrompt.category || ''}
                    onChange={(e) => setEditingPrompt({
                      ...editingPrompt,
                      category: e.target.value
                    })}
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>Sıra İndeksi</FormLabel>
                  <NumberInput
                    value={editingPrompt.order_index}
                    onChange={(value) => setEditingPrompt({
                      ...editingPrompt,
                      order_index: parseInt(value) || 0
                    })}
                  >
                    <NumberInputField />
                    <NumberInputStepper>
                      <NumberIncrementStepper />
                      <NumberDecrementStepper />
                    </NumberInputStepper>
                  </NumberInput>
                </FormControl>
                <FormControl>
                  <FormLabel>Task Code</FormLabel>
                  <Input
                    value={editingPrompt.task_code || ''}
                    onChange={(e) => setEditingPrompt({
                      ...editingPrompt,
                      task_code: e.target.value
                    })}
                  />
                </FormControl>
                <HStack w="100%" justify="space-between">
                  <FormControl>
                    <HStack>
                      <FormLabel mb={0}>Kullanılmış</FormLabel>
                      <Switch
                        isChecked={editingPrompt.is_used}
                        onChange={(e) => setEditingPrompt({
                          ...editingPrompt,
                          is_used: e.target.checked
                        })}
                      />
                    </HStack>
                  </FormControl>
                  <FormControl>
                    <HStack>
                      <FormLabel mb={0}>Favori</FormLabel>
                      <Switch
                        isChecked={editingPrompt.is_favorite}
                        onChange={(e) => setEditingPrompt({
                          ...editingPrompt,
                          is_favorite: e.target.checked
                        })}
                      />
                    </HStack>
                  </FormControl>
                </HStack>
              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onEditClose}>
              İptal
            </Button>
            <Button colorScheme="blue" onClick={handleUpdatePrompt}>
              Güncelle
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* View Prompt Modal */}
      <Modal isOpen={isViewOpen} onClose={onViewClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Prompt Detayları</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedPrompt && (
              <VStack spacing={4} align="stretch">
                <Box>
                  <Text fontWeight="bold" mb={2}>Başlık:</Text>
                  <Text>{selectedPrompt.title || 'Başlıksız'}</Text>
                </Box>
                {selectedPrompt.description && (
                  <Box>
                    <Text fontWeight="bold" mb={2}>Açıklama:</Text>
                    <Text>{selectedPrompt.description}</Text>
                  </Box>
                )}
                <Box>
                  <Text fontWeight="bold" mb={2}>Prompt Metni:</Text>
                  <Text 
                    bg="gray.50" 
                    p={3} 
                    rounded="md" 
                    whiteSpace="pre-wrap"
                    maxH="300px"
                    overflowY="auto"
                  >
                    {selectedPrompt.prompt_text}
                  </Text>
                </Box>
                <SimpleGrid columns={2} spacing={4}>
                  <Box>
                    <Text fontWeight="bold" mb={2}>Proje:</Text>
                    <Text>{selectedPrompt.project_name}</Text>
                  </Box>
                  <Box>
                    <Text fontWeight="bold" mb={2}>Sahibi:</Text>
                    <Text>{selectedPrompt.user_email}</Text>
                  </Box>
                  <Box>
                    <Text fontWeight="bold" mb={2}>Kategori:</Text>
                    <Text>{selectedPrompt.category || 'Belirtilmemiş'}</Text>
                  </Box>
                  <Box>
                    <Text fontWeight="bold" mb={2}>Kullanım Sayısı:</Text>
                    <Badge colorScheme={getUsageColor(selectedPrompt.usage_count)}>
                      {selectedPrompt.usage_count} kez
                    </Badge>
                  </Box>
                </SimpleGrid>
                {selectedPrompt.tags.length > 0 && (
                  <Box>
                    <Text fontWeight="bold" mb={2}>Etiketler:</Text>
                    <Wrap>
                      {selectedPrompt.tags.map((tag, index) => (
                        <WrapItem key={index}>
                          <Tag colorScheme="blue">
                            <TagLabel>{tag}</TagLabel>
                          </Tag>
                        </WrapItem>
                      ))}
                    </Wrap>
                  </Box>
                )}
                <SimpleGrid columns={2} spacing={4}>
                  <Box>
                    <Text fontWeight="bold" mb={2}>Durum:</Text>
                    <HStack>
                      {selectedPrompt.is_used && (
                        <Badge colorScheme="green">Kullanılmış</Badge>
                      )}
                      {selectedPrompt.is_favorite && (
                        <Badge colorScheme="red">Favori</Badge>
                      )}
                      {!selectedPrompt.is_used && !selectedPrompt.is_favorite && (
                        <Badge colorScheme="gray">Normal</Badge>
                      )}
                    </HStack>
                  </Box>
                  <Box>
                    <Text fontWeight="bold" mb={2}>Sıra İndeksi:</Text>
                    <Text>{selectedPrompt.order_index}</Text>
                  </Box>
                </SimpleGrid>
                {selectedPrompt.task_code && (
                  <Box>
                    <Text fontWeight="bold" mb={2}>Task Code:</Text>
                    <Text 
                      bg="gray.50" 
                      p={2} 
                      rounded="md" 
                      fontFamily="mono"
                      fontSize="sm"
                    >
                      {selectedPrompt.task_code}
                    </Text>
                  </Box>
                )}
                <SimpleGrid columns={2} spacing={4}>
                  <Box>
                    <Text fontWeight="bold" mb={2}>Oluşturulma:</Text>
                    <Text fontSize="sm">{formatDate(selectedPrompt.created_at)}</Text>
                  </Box>
                  <Box>
                    <Text fontWeight="bold" mb={2}>Son Kullanım:</Text>
                    <Text fontSize="sm">{formatDate(selectedPrompt.last_used_at)}</Text>
                  </Box>
                </SimpleGrid>
              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <Button onClick={onViewClose}>Kapat</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </AdminLayout>
  );
}