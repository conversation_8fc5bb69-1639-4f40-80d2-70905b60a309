/**
 * Integration test utilities for the categorization system
 * This file contains test functions to verify the hashtag and folder categorization features
 */

import {
  extractHashtags,
  extractFolderPaths,
  parsePromptCategories,
  parsePromptCategoriesPreserveText,
  extractCategoriesOnly,
  cleanHashtag,
  formatHashtag,
  isValidHashtag,
  isValidFolderPath,
  mergeHashtags,
  getFolderHierarchy,
  getHashtagSuggestions,
  getFolderSuggestions
} from './hashtag-utils'

// Test data
const testPrompts = [
  {
    text: "Create a React component for user authentication #frontend #react #auth",
    expectedHashtags: ["#frontend", "#react", "#auth"],
    expectedFolders: [],
    expectedCleanText: "Create a React component for user authentication"
  },
  {
    text: "/frontend/components Create a button component #ui #component",
    expectedHashtags: ["#ui", "#component"],
    expectedFolders: ["/frontend/components"],
    expectedCleanText: "Create a button component"
  },
  {
    text: "Setup database connection /backend/database #backend #database #setup",
    expectedHashtags: ["#backend", "#database", "#setup"],
    expectedFolders: ["/backend/database"],
    expectedCleanText: "Setup database connection"
  },
  {
    text: "Regular prompt without any categories",
    expectedHashtags: [],
    expectedFolders: [],
    expectedCleanText: "Regular prompt without any categories"
  }
]

// Test functions
export function testHashtagExtraction(): boolean {
  console.log('Testing hashtag extraction...')
  
  for (const test of testPrompts) {
    const extracted = extractHashtags(test.text)
    const expected = test.expectedHashtags
    
    if (JSON.stringify(extracted.sort()) !== JSON.stringify(expected.sort())) {
      console.error(`Hashtag extraction failed for: "${test.text}"`)
      console.error(`Expected: ${JSON.stringify(expected)}`)
      console.error(`Got: ${JSON.stringify(extracted)}`)
      return false
    }
  }
  
  console.log('✅ Hashtag extraction tests passed')
  return true
}

export function testFolderExtraction(): boolean {
  console.log('Testing folder extraction...')
  
  for (const test of testPrompts) {
    const extracted = extractFolderPaths(test.text)
    const expected = test.expectedFolders
    
    if (JSON.stringify(extracted.sort()) !== JSON.stringify(expected.sort())) {
      console.error(`Folder extraction failed for: "${test.text}"`)
      console.error(`Expected: ${JSON.stringify(expected)}`)
      console.error(`Got: ${JSON.stringify(extracted)}`)
      return false
    }
  }
  
  console.log('✅ Folder extraction tests passed')
  return true
}

export function testPromptParsing(): boolean {
  console.log('Testing prompt parsing...')
  
  for (const test of testPrompts) {
    const result = parsePromptCategories(test.text)
    
    // Check hashtags
    if (JSON.stringify(result.hashtags.sort()) !== JSON.stringify(test.expectedHashtags.map(h => h.replace('#', '')).sort())) {
      console.error(`Prompt parsing hashtags failed for: "${test.text}"`)
      console.error(`Expected hashtags: ${JSON.stringify(test.expectedHashtags)}`)
      console.error(`Got hashtags: ${JSON.stringify(result.hashtags)}`)
      return false
    }
    
    // Check folders
    if (JSON.stringify(result.folderPaths.sort()) !== JSON.stringify(test.expectedFolders.sort())) {
      console.error(`Prompt parsing folders failed for: "${test.text}"`)
      console.error(`Expected folders: ${JSON.stringify(test.expectedFolders)}`)
      console.error(`Got folders: ${JSON.stringify(result.folderPaths)}`)
      return false
    }
    
    // Check clean text
    if (result.cleanText !== test.expectedCleanText) {
      console.error(`Prompt parsing clean text failed for: "${test.text}"`)
      console.error(`Expected: "${test.expectedCleanText}"`)
      console.error(`Got: "${result.cleanText}"`)
      return false
    }
  }
  
  console.log('✅ Prompt parsing tests passed')
  return true
}

export function testValidation(): boolean {
  console.log('Testing validation functions...')
  
  // Test hashtag validation
  const validHashtags = ['frontend', 'react', 'ui-component', 'test123']
  const invalidHashtags = ['', ' ', '123', 'test space', 'test@symbol']
  
  for (const hashtag of validHashtags) {
    if (!isValidHashtag(hashtag)) {
      console.error(`Valid hashtag "${hashtag}" was marked as invalid`)
      return false
    }
  }
  
  for (const hashtag of invalidHashtags) {
    if (isValidHashtag(hashtag)) {
      console.error(`Invalid hashtag "${hashtag}" was marked as valid`)
      return false
    }
  }
  
  // Test folder validation
  const validFolders = ['/frontend', '/backend/api', '/admin/users', '/']
  const invalidFolders = ['frontend', 'backend/api', '', ' ']
  
  for (const folder of validFolders) {
    if (!isValidFolderPath(folder)) {
      console.error(`Valid folder "${folder}" was marked as invalid`)
      return false
    }
  }
  
  for (const folder of invalidFolders) {
    if (isValidFolderPath(folder)) {
      console.error(`Invalid folder "${folder}" was marked as valid`)
      return false
    }
  }
  
  console.log('✅ Validation tests passed')
  return true
}

export function testUtilityFunctions(): boolean {
  console.log('Testing utility functions...')
  
  // Test hashtag cleaning and formatting
  const hashtagTests = [
    { input: '#frontend', expected: 'frontend', formatted: '#frontend' },
    { input: 'backend', expected: 'backend', formatted: '#backend' },
    { input: ' #ui ', expected: 'ui', formatted: '#ui' },
  ]
  
  for (const test of hashtagTests) {
    const cleaned = cleanHashtag(test.input)
    const formatted = formatHashtag(test.input)
    
    if (cleaned !== test.expected) {
      console.error(`Hashtag cleaning failed for "${test.input}": expected "${test.expected}", got "${cleaned}"`)
      return false
    }
    
    if (formatted !== test.formatted) {
      console.error(`Hashtag formatting failed for "${test.input}": expected "${test.formatted}", got "${formatted}"`)
      return false
    }
  }
  
  // Test folder hierarchy
  const hierarchyTests = [
    { input: '/frontend/components/ui', expected: ['/', '/frontend', '/frontend/components', '/frontend/components/ui'] },
    { input: '/admin', expected: ['/', '/admin'] },
    { input: '/', expected: ['/'] },
  ]
  
  for (const test of hierarchyTests) {
    const hierarchy = getFolderHierarchy(test.input)
    
    if (JSON.stringify(hierarchy) !== JSON.stringify(test.expected)) {
      console.error(`Folder hierarchy failed for "${test.input}": expected ${JSON.stringify(test.expected)}, got ${JSON.stringify(hierarchy)}`)
      return false
    }
  }
  
  // Test hashtag merging
  const mergeTest = mergeHashtags(['#frontend', '#react'], ['#ui', '#frontend', '#component'])
  const expectedMerge = ['#frontend', '#react', '#ui', '#component']
  
  if (JSON.stringify(mergeTest.sort()) !== JSON.stringify(expectedMerge.sort())) {
    console.error(`Hashtag merging failed: expected ${JSON.stringify(expectedMerge)}, got ${JSON.stringify(mergeTest)}`)
    return false
  }
  
  console.log('✅ Utility function tests passed')
  return true
}

export function testSuggestions(): boolean {
  console.log('Testing suggestion functions...')
  
  const existingHashtags = ['#frontend', '#backend', '#ui', '#component', '#react', '#vue']
  const existingFolders = ['/frontend', '/backend', '/admin', '/frontend/components', '/backend/api']
  
  // Test hashtag suggestions
  const hashtagSuggestions = getHashtagSuggestions('fron', existingHashtags, 3)
  if (!hashtagSuggestions.includes('#frontend')) {
    console.error('Hashtag suggestions should include "#frontend" for input "fron"')
    return false
  }
  
  // Test folder suggestions
  const folderSuggestions = getFolderSuggestions('front', existingFolders, 3)
  if (!folderSuggestions.some(f => f.includes('frontend'))) {
    console.error('Folder suggestions should include folders with "frontend" for input "front"')
    return false
  }
  
  console.log('✅ Suggestion tests passed')
  return true
}

// Run all tests
export function runAllCategorizationTests(): boolean {
  console.log('🧪 Running categorization system tests...\n')
  
  const tests = [
    testHashtagExtraction,
    testFolderExtraction,
    testPromptParsing,
    testValidation,
    testUtilityFunctions,
    testSuggestions
  ]
  
  for (const test of tests) {
    if (!test()) {
      console.log('\n❌ Categorization tests failed!')
      return false
    }
  }
  
  console.log('\n🎉 All categorization tests passed!')
  return true
}

// NEW: Test the preserve text functionality
export function testPreserveTextFunctionality(): boolean {
  console.log('Testing preserve text functionality...')

  const testCases = [
    {
      input: "Create a /frontend #react component",
      expectedOriginal: "Create a /frontend #react component",
      expectedCategory: "/frontend",
      expectedTags: ["react"]
    },
    {
      input: "Setup /backend/database #backend #database connection",
      expectedOriginal: "Setup /backend/database #backend #database connection",
      expectedCategory: "/backend/database",
      expectedTags: ["backend", "database"]
    },
    {
      input: "Regular prompt without categories",
      expectedOriginal: "Regular prompt without categories",
      expectedCategory: null,
      expectedTags: []
    }
  ]

  for (const test of testCases) {
    // Test parsePromptCategoriesPreserveText
    const preserveResult = parsePromptCategoriesPreserveText(test.input)
    if (preserveResult.originalText !== test.expectedOriginal) {
      console.error(`Preserve text failed for: "${test.input}"`)
      console.error(`Expected: "${test.expectedOriginal}"`)
      console.error(`Got: "${preserveResult.originalText}"`)
      return false
    }

    // Test extractCategoriesOnly
    const extractResult = extractCategoriesOnly(test.input)
    if (extractResult.category !== test.expectedCategory) {
      console.error(`Extract category failed for: "${test.input}"`)
      console.error(`Expected category: "${test.expectedCategory}"`)
      console.error(`Got category: "${extractResult.category}"`)
      return false
    }

    if (JSON.stringify(extractResult.tags.sort()) !== JSON.stringify(test.expectedTags.sort())) {
      console.error(`Extract tags failed for: "${test.input}"`)
      console.error(`Expected tags: ${JSON.stringify(test.expectedTags)}`)
      console.error(`Got tags: ${JSON.stringify(extractResult.tags)}`)
      return false
    }
  }

  console.log('✅ Preserve text functionality tests passed!')
  return true
}

// NEW: Test performance and caching
export function testPerformanceAndCaching(): boolean {
  console.log('Testing performance and caching...')

  const testText = "Create a /frontend #react #typescript component"

  // First call (should cache)
  const start1 = performance.now()
  const result1 = extractCategoriesOnly(testText)
  const end1 = performance.now()

  // Second call (should use cache)
  const start2 = performance.now()
  const result2 = extractCategoriesOnly(testText)
  const end2 = performance.now()

  // Results should be identical
  if (JSON.stringify(result1) !== JSON.stringify(result2)) {
    console.error('Cached results differ from original')
    return false
  }

  // Second call should be faster (cached)
  const time1 = end1 - start1
  const time2 = end2 - start2

  console.log(`First call: ${time1.toFixed(3)}ms`)
  console.log(`Second call (cached): ${time2.toFixed(3)}ms`)

  console.log('✅ Performance and caching tests passed!')
  return true
}

// Updated test runner
export function runAllCategorizationTestsV2(): boolean {
  console.log('🧪 Running all categorization tests (V2)...\n')

  const tests = [
    testHashtagExtraction,
    testFolderExtraction,
    testPromptParsing,
    testPreserveTextFunctionality, // NEW
    testPerformanceAndCaching,     // NEW
    testValidation,
    testSuggestions
  ]

  for (const test of tests) {
    try {
      if (!test()) {
        console.log('\n❌ Categorization tests V2 failed!')
        return false
      }
    } catch (error) {
      console.error(`Test failed with error:`, error)
      console.log('\n❌ Categorization tests V2 failed!')
      return false
    }
  }

  console.log('\n🎉 All categorization tests V2 passed!')
  return true
}

// Export test runner for use in development
if (typeof window !== 'undefined') {
  (window as typeof window & {
    testCategorization: typeof runAllCategorizationTests,
    testCategorizationV2: typeof runAllCategorizationTestsV2,
    testPreserveText: typeof testPreserveTextFunctionality,
    testPerformance: typeof testPerformanceAndCaching
  }).testCategorization = runAllCategorizationTests;

  (window as typeof window & {
    testCategorization: typeof runAllCategorizationTests,
    testCategorizationV2: typeof runAllCategorizationTestsV2,
    testPreserveText: typeof testPreserveTextFunctionality,
    testPerformance: typeof testPerformanceAndCaching
  }).testCategorizationV2 = runAllCategorizationTestsV2;

  (window as typeof window & {
    testCategorization: typeof runAllCategorizationTests,
    testCategorizationV2: typeof runAllCategorizationTestsV2,
    testPreserveText: typeof testPreserveTextFunctionality,
    testPerformance: typeof testPerformanceAndCaching
  }).testPreserveText = testPreserveTextFunctionality;

  (window as typeof window & {
    testCategorization: typeof runAllCategorizationTests,
    testCategorizationV2: typeof runAllCategorizationTestsV2,
    testPreserveText: typeof testPreserveTextFunctionality,
    testPerformance: typeof testPerformanceAndCaching
  }).testPerformance = testPerformanceAndCaching;
}
