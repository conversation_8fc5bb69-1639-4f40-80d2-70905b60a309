'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Crown, Zap, Shield, Check, X } from 'lucide-react'
import { useChangePlan } from '@/hooks/use-plans'
import { Database } from '@/lib/supabase'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

type PlanType = Database['public']['Tables']['plan_types']['Row']
type UserPlan = {
  plan_id: string
  plan_name: string
  display_name: string
  max_projects: number
  max_prompts_per_project: number
  features: Record<string, boolean | string | number>
  status: string
  expires_at: string | null
}

interface PlanUpgradeModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentPlan: UserPlan | null
  planTypes: PlanType[]
}

export function PlanUpgradeModal({ 
  open, 
  onOpenChange, 
  currentPlan, 
  planTypes 
}: PlanUpgradeModalProps) {
  const [selectedPlan, setSelectedPlan] = useState<string>('')
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly')
  const changePlanMutation = useChangePlan()

  const getPlanIcon = (planName: string) => {
    switch (planName) {
      case 'free':
        return <Zap className="h-6 w-6 text-blue-500" />
      case 'professional':
        return <Crown className="h-6 w-6 text-yellow-500" />
      case 'enterprise':
        return <Shield className="h-6 w-6 text-purple-500" />
      default:
        return <Zap className="h-6 w-6 text-gray-500" />
    }
  }

  const getPlanColor = (planName: string) => {
    switch (planName) {
      case 'free':
        return 'border-blue-200 bg-blue-50'
      case 'professional':
        return 'border-yellow-200 bg-yellow-50'
      case 'enterprise':
        return 'border-purple-200 bg-purple-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  const getPrice = (plan: PlanType) => {
    if (plan.name === 'enterprise') return 'Özel Fiyat'
    if (plan.name === 'free') return 'Ücretsiz'
    
    const price = billingCycle === 'monthly' ? plan.price_monthly : plan.price_yearly
    const period = billingCycle === 'monthly' ? 'ay' : 'yıl'
    
    return `₺${price}/${period}`
  }

  const getYearlySavings = (plan: PlanType) => {
    if (plan.name === 'free' || plan.name === 'enterprise') return null
    
    const monthlyTotal = plan.price_monthly * 12
    const yearlySavings = monthlyTotal - plan.price_yearly
    const savingsPercentage = Math.round((yearlySavings / monthlyTotal) * 100)
    
    return { amount: yearlySavings, percentage: savingsPercentage }
  }

  const handleUpgrade = async () => {
    if (!selectedPlan) {
      toast.error('Lütfen bir plan seçin')
      return
    }

    try {
      await changePlanMutation.mutateAsync({
        planName: selectedPlan,
        billingCycle: billingCycle
      })
      
      toast.success('Planınız başarıyla güncellendi!')
      onOpenChange(false)
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Plan güncellenirken hata oluştu')
    }
  }

  const isCurrentPlan = (planName: string) => {
    return currentPlan?.plan_name === planName
  }

  const canSelectPlan = (planName: string) => {
    if (planName === 'enterprise') return false // Kurumsal plan manuel atanır
    return !isCurrentPlan(planName)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">Planınızı Yükseltin</DialogTitle>
          <DialogDescription>
            İhtiyaçlarınıza en uygun planı seçin ve daha fazla özellikten yararlanın
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Billing Cycle Seçimi */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Faturalama Döngüsü</Label>
            <RadioGroup 
              value={billingCycle} 
              onValueChange={(value) => setBillingCycle(value as 'monthly' | 'yearly')}
              className="flex gap-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="monthly" id="monthly" />
                <Label htmlFor="monthly">Aylık</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yearly" id="yearly" />
                <Label htmlFor="yearly" className="flex items-center gap-2">
                  Yıllık
                  <Badge variant="secondary" className="text-xs">%17 İndirim</Badge>
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Plan Kartları */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {planTypes.map((plan) => {
              const savings = getYearlySavings(plan)
              const isCurrent = isCurrentPlan(plan.name)
              const canSelect = canSelectPlan(plan.name)
              
              return (
                <Card 
                  key={plan.id}
                  className={cn(
                    'relative cursor-pointer transition-all duration-200',
                    getPlanColor(plan.name),
                    selectedPlan === plan.name && 'ring-2 ring-blue-500',
                    isCurrent && 'ring-2 ring-green-500',
                    !canSelect && 'opacity-60 cursor-not-allowed'
                  )}
                  onClick={() => canSelect && setSelectedPlan(plan.name)}
                >
                  {isCurrent && (
                    <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-green-500">
                      Mevcut Plan
                    </Badge>
                  )}
                  
                  {plan.name === 'professional' && (
                    <Badge className="absolute -top-2 right-4 bg-blue-500">
                      Popüler
                    </Badge>
                  )}

                  <CardHeader className="text-center pb-2">
                    <div className="flex justify-center mb-2">
                      {getPlanIcon(plan.name)}
                    </div>
                    <CardTitle className="text-xl">{plan.display_name}</CardTitle>
                    <CardDescription className="text-sm">
                      {plan.description}
                    </CardDescription>
                    
                    <div className="pt-2">
                      <div className="text-3xl font-bold text-gray-900">
                        {getPrice(plan)}
                      </div>
                      {billingCycle === 'yearly' && savings && (
                        <div className="text-sm text-green-600 font-medium">
                          Yıllık ₺{savings.amount} tasarruf
                        </div>
                      )}
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <Separator />
                    
                    {/* Limitler */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Proje Limiti</span>
                        <span className="font-medium">
                          {plan.max_projects === -1 ? 'Sınırsız' : plan.max_projects}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Prompt Limiti</span>
                        <span className="font-medium">
                          {plan.max_prompts_per_project === -1 ? 'Sınırsız' : `${plan.max_prompts_per_project}/proje`}
                        </span>
                      </div>
                    </div>

                    <Separator />

                    {/* Özellikler */}
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Özellikler</h4>
                      <div className="space-y-1">
                        {Object.entries(plan.features).map(([key, value]) => (
                          <div key={key} className="flex items-center gap-2 text-xs">
                            {value ? (
                              <Check className="h-3 w-3 text-green-500" />
                            ) : (
                              <X className="h-3 w-3 text-gray-400" />
                            )}
                            <span className={value ? 'text-gray-900' : 'text-gray-500'}>
                              {key === 'context_gallery' && 'Context Gallery'}
                              {key === 'api_access' && 'API Erişimi'}
                              {key === 'support' && `${value} Destek`}
                              {key === 'team_features' && 'Takım Özellikleri'}
                              {key === 'advanced_analytics' && 'Gelişmiş Analitik'}
                              {key === 'sso' && 'SSO Entegrasyonu'}
                              {key === 'custom_deployment' && 'Özel Deployment'}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {plan.name === 'enterprise' && (
                      <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                        Kurumsal plan için satış ekibimizle iletişime geçin
                      </div>
                    )}
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              İptal
            </Button>
            <Button 
              onClick={handleUpgrade}
              disabled={!selectedPlan || changePlanMutation.isPending}
            >
              {changePlanMutation.isPending ? 'Güncelleniyor...' : 'Planı Güncelle'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
