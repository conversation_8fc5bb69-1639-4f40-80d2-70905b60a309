#!/usr/bin/env node

/**
 * Verification script for authentication fixes
 * Tests both admin access and token refresh mechanisms
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  console.log('Required:');
  console.log('- NEXT_PUBLIC_SUPABASE_URL');
  console.log('- NEXT_PUBLIC_SUPABASE_ANON_KEY');
  console.log('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create clients
const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔍 Starting Authentication Verification...\n');

/**
 * Test 1: Verify Service Role Key
 */
async function testServiceRoleKey() {
  console.log('1️⃣ Testing Service Role Key...');
  
  try {
    // Test admin_users table access with service role
    const { data, error } = await supabaseAdmin
      .from('admin_users')
      .select('id, user_id, role, is_active')
      .limit(1);

    if (error) {
      console.error('❌ Service role key test failed:', error.message);
      return false;
    }

    console.log('✅ Service role key working correctly');
    console.log(`   Found ${data?.length || 0} admin users`);
    return true;
  } catch (error) {
    console.error('❌ Service role key test error:', error.message);
    return false;
  }
}

/**
 * Test 2: Verify Admin Functions
 */
async function testAdminFunctions() {
  console.log('\n2️⃣ Testing Admin Functions...');
  
  try {
    // Test check_admin_status function
    const { data, error } = await supabaseAdmin
      .rpc('check_admin_status', { 
        user_uuid: '00000000-0000-0000-0000-000000000000' // Test UUID
      });

    if (error) {
      console.error('❌ Admin function test failed:', error.message);
      return false;
    }

    console.log('✅ check_admin_status function working');
    console.log('   Function response:', data);
    return true;
  } catch (error) {
    console.error('❌ Admin function test error:', error.message);
    return false;
  }
}

/**
 * Test 3: Verify RLS Policies
 */
async function testRLSPolicies() {
  console.log('\n3️⃣ Testing RLS Policies...');
  
  try {
    // Test that anon client cannot access admin_users without proper auth
    const { data, error } = await supabaseAnon
      .from('admin_users')
      .select('*')
      .limit(1);

    // Check if RLS is working - either error or empty data means RLS is working
    if (error || (data && data.length === 0)) {
      console.log('✅ RLS policies working correctly');
      if (error) {
        console.log('   Anon access blocked with error:', error.message);
      } else {
        console.log('   Anon access returns empty result (RLS filtering)');
      }
      return true;
    }

    // If we get actual data, RLS is not working
    console.warn('⚠️  Warning: Anon client can access admin_users (RLS may be misconfigured)');
    console.log('   Data returned:', data);
    return false;
  } catch (error) {
    console.error('❌ RLS policy test error:', error.message);
    return false;
  }
}

/**
 * Test 4: Verify Environment Configuration
 */
async function testEnvironmentConfig() {
  console.log('\n4️⃣ Testing Environment Configuration...');
  
  const checks = [
    { name: 'Supabase URL', value: supabaseUrl, valid: supabaseUrl?.includes('supabase.co') },
    { name: 'Anon Key', value: supabaseAnonKey?.substring(0, 20) + '...', valid: supabaseAnonKey?.startsWith('eyJ') },
    { name: 'Service Key', value: supabaseServiceKey?.substring(0, 20) + '...', valid: supabaseServiceKey?.startsWith('eyJ') },
  ];

  let allValid = true;
  checks.forEach(check => {
    if (check.valid) {
      console.log(`✅ ${check.name}: ${check.value}`);
    } else {
      console.log(`❌ ${check.name}: Invalid or missing`);
      allValid = false;
    }
  });

  return allValid;
}

/**
 * Test 5: Verify Database Connection
 */
async function testDatabaseConnection() {
  console.log('\n5️⃣ Testing Database Connection...');
  
  try {
    const { data, error } = await supabaseAdmin
      .from('admin_users')
      .select('count', { count: 'exact', head: true });

    if (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }

    console.log('✅ Database connection successful');
    console.log(`   Total admin users: ${data || 0}`);
    return true;
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    return false;
  }
}

/**
 * Run all tests
 */
async function runVerification() {
  const tests = [
    testEnvironmentConfig,
    testDatabaseConnection,
    testServiceRoleKey,
    testAdminFunctions,
    testRLSPolicies,
  ];

  let passedTests = 0;
  const totalTests = tests.length;

  for (const test of tests) {
    const result = await test();
    if (result) passedTests++;
  }

  console.log('\n📊 Verification Results:');
  console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All authentication fixes verified successfully!');
    console.log('\n📋 Next Steps:');
    console.log('1. Restart the admin panel development server');
    console.log('2. Test admin login functionality');
    console.log('3. Verify admin_users table access');
    console.log('4. Monitor for token refresh errors');
  } else {
    console.log('⚠️  Some tests failed. Please review the errors above.');
    process.exit(1);
  }
}

// Run verification
runVerification().catch(error => {
  console.error('💥 Verification script failed:', error);
  process.exit(1);
});
