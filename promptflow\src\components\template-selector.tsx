'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog'
import { 
  Search, 
  Sparkles, 
  Code, 
  FileText, 
  CheckCircle, 
  RefreshCw, 
  Palette, 
  TrendingUp,
  Clock,
  X
} from 'lucide-react'
import { 
  usePromptCategories, 
  usePromptTemplates, 
  usePopularTemplates, 
  useSearchTemplates,
  useIncrementTemplateUsage,
  type PromptTemplateWithCategory 
} from '@/hooks/use-templates'

interface TemplateSelectorProps {
  onSelectTemplate: (template: PromptTemplateWithCategory) => void
  trigger?: React.ReactNode
}

const categoryIcons: Record<string, React.ReactNode> = {
  'Code': <Code className="h-4 w-4" />,
  'Search': <Search className="h-4 w-4" />,
  'FileText': <FileText className="h-4 w-4" />,
  'CheckCircle': <CheckCircle className="h-4 w-4" />,
  'RefreshCw': <RefreshCw className="h-4 w-4" />,
  'Palette': <Palette className="h-4 w-4" />,
}

export function TemplateSelector({ onSelectTemplate, trigger }: TemplateSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [activeTab, setActiveTab] = useState<'popular' | 'categories' | 'search'>('popular')

  const { data: categories = [] } = usePromptCategories()
  const { data: templates = [] } = usePromptTemplates(selectedCategory || undefined)
  const { data: popularTemplates = [] } = usePopularTemplates(6)
  const { data: searchResults = [] } = useSearchTemplates(searchTerm)
  const incrementUsageMutation = useIncrementTemplateUsage()

  const handleSelectTemplate = async (template: PromptTemplateWithCategory) => {
    try {
      await incrementUsageMutation.mutateAsync(template.id)
      onSelectTemplate(template)
      setIsOpen(false)
      setSearchTerm('')
      setSelectedCategory(null)
      setActiveTab('popular')
    } catch (error) {
      console.error('Template seçim hatası:', error)
    }
  }

  const handleSearch = (value: string) => {
    setSearchTerm(value)
    if (value.trim()) {
      setActiveTab('search')
    } else {
      setActiveTab('popular')
    }
  }

  const renderTemplateCard = (template: PromptTemplateWithCategory) => (
    <Card 
      key={template.id}
      className="cursor-pointer hover:shadow-md transition-all border-gray-200 hover:border-blue-300"
      onClick={() => handleSelectTemplate(template)}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-sm font-medium text-gray-900 mb-1">
              {template.name}
            </CardTitle>
            <CardDescription className="text-xs text-gray-500 line-clamp-2">
              {template.description}
            </CardDescription>
          </div>
          <div className="flex items-center gap-1 text-xs text-gray-400">
            <TrendingUp className="h-3 w-3" />
            {template.usage_count}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {template.category && (
              <Badge 
                variant="secondary" 
                className="text-xs"
                style={{ 
                  backgroundColor: `${template.category.color}20`,
                  color: template.category.color,
                  borderColor: `${template.category.color}40`
                }}
              >
                {categoryIcons[template.category.icon || 'Code']}
                <span className="ml-1">{template.category.name}</span>
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-1">
            {template.tags?.slice(0, 2).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <Sparkles className="h-4 w-4 mr-2" />
            Şablon Seç
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-blue-600" />
            Prompt Şablonları
          </DialogTitle>
          <DialogDescription>
            Hazır prompt şablonlarından seçim yapın ve hızlıca kullanmaya başlayın
          </DialogDescription>
        </DialogHeader>

        {/* Search */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Şablon ara..."
            value={searchTerm}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10 pr-10"
          />
          {searchTerm && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
              onClick={() => handleSearch('')}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Tabs */}
        <div className="flex gap-2 mb-4">
          <Button
            variant={activeTab === 'popular' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setActiveTab('popular')}
          >
            <TrendingUp className="h-4 w-4 mr-2" />
            Popüler
          </Button>
          <Button
            variant={activeTab === 'categories' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setActiveTab('categories')}
          >
            <FileText className="h-4 w-4 mr-2" />
            Kategoriler
          </Button>
          {searchTerm && (
            <Button
              variant={activeTab === 'search' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveTab('search')}
            >
              <Search className="h-4 w-4 mr-2" />
              Arama Sonuçları ({searchResults.length})
            </Button>
          )}
        </div>

        <div className="flex-1 flex gap-4 min-h-0">
          {/* Categories Sidebar */}
          {activeTab === 'categories' && (
            <div className="w-64 flex-shrink-0">
              <ScrollArea className="h-full">
                <div className="space-y-2">
                  <Button
                    variant={selectedCategory === null ? 'default' : 'ghost'}
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => setSelectedCategory(null)}
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Tüm Kategoriler
                  </Button>
                  <Separator />
                  {categories.map((category) => (
                    <Button
                      key={category.id}
                      variant={selectedCategory === category.id ? 'default' : 'ghost'}
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => setSelectedCategory(category.id)}
                    >
                      {categoryIcons[category.icon || 'Code']}
                      <span className="ml-2">{category.name}</span>
                    </Button>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}

          {/* Templates Grid */}
          <div className="flex-1">
            <ScrollArea className="h-full">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {activeTab === 'popular' && popularTemplates.map(renderTemplateCard)}
                {activeTab === 'categories' && templates.map(renderTemplateCard)}
                {activeTab === 'search' && searchResults.map(renderTemplateCard)}
              </div>
              
              {/* Empty States */}
              {activeTab === 'popular' && popularTemplates.length === 0 && (
                <div className="text-center py-12">
                  <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Henüz Popüler Şablon Yok</h3>
                  <p className="text-gray-500">Şablonlar kullanıldıkça burada görünecek</p>
                </div>
              )}
              
              {activeTab === 'categories' && templates.length === 0 && (
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Bu Kategoride Şablon Yok</h3>
                  <p className="text-gray-500">Başka bir kategori seçin veya tüm kategorileri görüntüleyin</p>
                </div>
              )}
              
              {activeTab === 'search' && searchResults.length === 0 && searchTerm && (
                <div className="text-center py-12">
                  <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Sonuç Bulunamadı</h3>
                                     <p className="text-gray-500">&quot;{searchTerm}&quot; için eşleşen şablon bulunamadı</p>
                </div>
              )}
            </ScrollArea>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 