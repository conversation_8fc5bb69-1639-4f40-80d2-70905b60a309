import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import {
  createMiddlewareSupabaseClient,
  verifySessionWithFallbacks,
  waitForSession,
  debugSessionState
} from '@/lib/session-sync';

export async function middleware(req: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: req.headers,
    },
  });

  // Debug session state
  const debugInfo = debugSessionState(req);
  console.log('🔍 Middleware debug info:', debugInfo);

  const supabase = createMiddlewareSupabaseClient(req, response);

  const pathname = req.nextUrl.pathname;

  // Skip middleware for login page to prevent loops
  if (pathname === '/') {
    return response;
  }

  try {
    console.log('🔍 Middleware: Checking authentication for path:', pathname);

    // Use enhanced session verification with fallbacks
    const sessionResult = await waitForSession(supabase, 3, 100);

    if (!sessionResult.user) {
      console.log('❌ Middleware: No user found after enhanced verification, redirecting to login');
      return NextResponse.redirect(new URL('/', req.url));
    }

    const user = sessionResult.user;
    console.log('✅ Middleware: User verified via', sessionResult.method);

    console.log('🔐 Middleware: Checking admin status for user:', user.id);

    // Admin yetkisi kontrolü - Use function to avoid RLS issues
    const { data: adminCheckResult, error: adminError } = await supabase
      .rpc('check_admin_status', { user_uuid: user.id });

    console.log('🛡️ Middleware admin check result:', {
      hasResult: !!adminCheckResult,
      resultLength: adminCheckResult?.length || 0,
      isAdmin: adminCheckResult?.[0]?.is_admin,
      error: adminError?.message
    });

    if (adminError || !adminCheckResult || adminCheckResult.length === 0 || !adminCheckResult[0].is_admin) {
      console.log('❌ Middleware: Admin check failed for user:', user.id);
      console.log('Admin error details:', adminError);
      console.log('Admin result details:', adminCheckResult);
      // Admin yetkisi yok, logout yap ve login sayfasına yönlendir
      await supabase.auth.signOut();
      return NextResponse.redirect(new URL('/?error=unauthorized', req.url));
    }

    console.log('✅ Middleware: Admin access granted for user:', user.id);
    // Admin yetkisi var, devam et
    return response;
  } catch (error) {
    console.error('Middleware error:', error);
    return NextResponse.redirect(new URL('/?error=server', req.url));
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - api routes (optional)
     */
    '/((?!_next/static|_next/image|favicon.ico|public|api).*)',
  ],
}; 