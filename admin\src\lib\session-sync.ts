/**
 * Session Synchronization Utilities
 * Handles the sync between client-side localStorage and server-side cookies
 */

import { createServerClient } from '@supabase/ssr';
import { NextRequest, NextResponse } from 'next/server';
import type { CookieOptions } from '@supabase/ssr';

/**
 * Create a Supabase client for middleware with enhanced session handling
 */
export function createMiddlewareSupabaseClient(req: NextRequest, res: NextResponse) {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          const cookie = req.cookies.get(name)?.value;
          console.log(`🍪 Getting cookie: ${name} = ${cookie ? 'present' : 'missing'}`);
          return cookie;
        },
        set(name: string, value: string, options: CookieOptions) {
          console.log(`🍪 Setting cookie: ${name} = ${value ? 'present' : 'empty'}`);
          req.cookies.set({
            name,
            value,
            ...options,
          });
          res.cookies.set({
            name,
            value,
            ...options,
          });
        },
        remove(name: string, options: CookieOptions) {
          console.log(`🍪 Removing cookie: ${name}`);
          req.cookies.set({
            name,
            value: '',
            ...options,
          });
          res.cookies.set({
            name,
            value: '',
            ...options,
          });
        },
      },
    }
  );
}

/**
 * Enhanced session verification with multiple fallback methods
 */
export async function verifySessionWithFallbacks(supabase: any) {
  console.log('🔍 Starting enhanced session verification...');
  
  // Method 1: Try getSession first
  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    console.log('📦 getSession result:', {
      hasSession: !!session,
      error: sessionError?.message,
      userId: session?.user?.id
    });
    
    if (session && !sessionError) {
      console.log('✅ Session found via getSession');
      return { user: session.user, session, method: 'getSession' };
    }
  } catch (error) {
    console.log('❌ getSession failed:', error);
  }
  
  // Method 2: Try getUser as fallback
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    console.log('👤 getUser result:', {
      hasUser: !!user,
      error: userError?.message,
      userId: user?.id
    });
    
    if (user && !userError) {
      console.log('✅ User found via getUser');
      return { user, session: null, method: 'getUser' };
    }
  } catch (error) {
    console.log('❌ getUser failed:', error);
  }
  
  console.log('❌ No valid session found with any method');
  return { user: null, session: null, method: 'none' };
}

/**
 * Wait for session to be available with timeout
 */
export async function waitForSession(supabase: any, maxAttempts = 5, delayMs = 200) {
  console.log(`⏳ Waiting for session (max ${maxAttempts} attempts)...`);
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    const result = await verifySessionWithFallbacks(supabase);
    
    if (result.user) {
      console.log(`✅ Session found on attempt ${attempt} via ${result.method}`);
      return result;
    }
    
    console.log(`⏳ Attempt ${attempt}/${maxAttempts} - no session found`);
    
    if (attempt < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  }
  
  console.log('❌ Session wait timeout');
  return { user: null, session: null, method: 'timeout' };
}

/**
 * Debug session state for troubleshooting
 */
export function debugSessionState(req: NextRequest) {
  console.log('🔍 Debug session state:');
  console.log('Request URL:', req.url);
  console.log('Request method:', req.method);
  console.log('Pathname:', req.nextUrl.pathname);
  
  // Log all cookies
  const cookies = req.cookies.getAll();
  console.log('🍪 All cookies:', cookies.map(c => `${c.name}=${c.value ? 'present' : 'empty'}`));
  
  // Look for Supabase-related cookies
  const supabaseCookies = cookies.filter(c => 
    c.name.includes('supabase') || 
    c.name.includes('sb-') ||
    c.name.includes('auth')
  );
  console.log('🔐 Auth-related cookies:', supabaseCookies);
  
  // Check headers
  const authHeader = req.headers.get('authorization');
  console.log('🔑 Authorization header:', authHeader ? 'present' : 'missing');
  
  return {
    totalCookies: cookies.length,
    authCookies: supabaseCookies.length,
    hasAuthHeader: !!authHeader
  };
}
