import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Admin panel state interface
interface AdminState {
  // Multi-select state
  selectedItems: Set<string>;
  isMultiSelectMode: boolean;
  
  // Drag & drop state
  isDragging: boolean;
  draggedItemId: string | null;
  
  // UI state
  activeView: 'dashboard' | 'users' | 'projects' | 'prompts' | 'contexts' | 'settings';
  sidebarCollapsed: boolean;
  
  // Filter and search state
  searchTerm: string;
  activeFilters: Record<string, any>;
  
  // Context management
  isContextEnabled: boolean;
  
  // Actions
  setSelectedItems: (items: Set<string>) => void;
  addSelectedItem: (id: string) => void;
  removeSelectedItem: (id: string) => void;
  clearSelectedItems: () => void;
  toggleMultiSelectMode: () => void;
  
  setIsDragging: (dragging: boolean) => void;
  setDraggedItemId: (id: string | null) => void;
  
  setActiveView: (view: AdminState['activeView']) => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  
  setSearchTerm: (term: string) => void;
  setActiveFilters: (filters: Record<string, any>) => void;
  updateFilter: (key: string, value: any) => void;
  clearFilters: () => void;
  
  setIsContextEnabled: (enabled: boolean) => void;
}

// Create the admin store with persistence
export const useAdminStore = create<AdminState>()(
  persist(
    (set, get) => ({
      // Initial state
      selectedItems: new Set<string>(),
      isMultiSelectMode: false,
      
      isDragging: false,
      draggedItemId: null,
      
      activeView: 'dashboard',
      sidebarCollapsed: false,
      
      searchTerm: '',
      activeFilters: {},
      
      isContextEnabled: true,
      
      // Multi-select actions
      setSelectedItems: (items) => set({ selectedItems: items }),
      
      addSelectedItem: (id) => set((state) => {
        const newSelected = new Set(state.selectedItems);
        newSelected.add(id);
        return { selectedItems: newSelected };
      }),
      
      removeSelectedItem: (id) => set((state) => {
        const newSelected = new Set(state.selectedItems);
        newSelected.delete(id);
        return { selectedItems: newSelected };
      }),
      
      clearSelectedItems: () => set({ selectedItems: new Set<string>() }),
      
      toggleMultiSelectMode: () => set((state) => ({
        isMultiSelectMode: !state.isMultiSelectMode,
        selectedItems: !state.isMultiSelectMode ? state.selectedItems : new Set<string>(),
      })),
      
      // Drag & drop actions
      setIsDragging: (dragging) => set({ isDragging: dragging }),
      setDraggedItemId: (id) => set({ draggedItemId: id }),
      
      // UI actions
      setActiveView: (view) => set({ activeView: view }),
      setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
      
      // Search and filter actions
      setSearchTerm: (term) => set({ searchTerm: term }),
      setActiveFilters: (filters) => set({ activeFilters: filters }),
      
      updateFilter: (key, value) => set((state) => ({
        activeFilters: { ...state.activeFilters, [key]: value },
      })),
      
      clearFilters: () => set({ activeFilters: {}, searchTerm: '' }),
      
      // Context actions
      setIsContextEnabled: (enabled) => set({ isContextEnabled: enabled }),
    }),
    {
      name: 'promptflow-admin-store',
      // Only persist certain state
      partialize: (state) => ({
        activeView: state.activeView,
        sidebarCollapsed: state.sidebarCollapsed,
        isContextEnabled: state.isContextEnabled,
      }),
    }
  )
);

// Selector hooks for better performance
export const useSelectedItems = () => useAdminStore((state) => state.selectedItems);
export const useIsMultiSelectMode = () => useAdminStore((state) => state.isMultiSelectMode);
export const useIsDragging = () => useAdminStore((state) => state.isDragging);
export const useActiveView = () => useAdminStore((state) => state.activeView);
export const useSearchTerm = () => useAdminStore((state) => state.searchTerm);
export const useActiveFilters = () => useAdminStore((state) => state.activeFilters);

// Action hooks
export const useMultiSelectActions = () => useAdminStore((state) => ({
  addSelectedItem: state.addSelectedItem,
  removeSelectedItem: state.removeSelectedItem,
  clearSelectedItems: state.clearSelectedItems,
  toggleMultiSelectMode: state.toggleMultiSelectMode,
}));

export const useDragDropActions = () => useAdminStore((state) => ({
  setIsDragging: state.setIsDragging,
  setDraggedItemId: state.setDraggedItemId,
}));

export const useUIActions = () => useAdminStore((state) => ({
  setActiveView: state.setActiveView,
  setSidebarCollapsed: state.setSidebarCollapsed,
}));

export const useSearchActions = () => useAdminStore((state) => ({
  setSearchTerm: state.setSearchTerm,
  updateFilter: state.updateFilter,
  clearFilters: state.clearFilters,
}));
