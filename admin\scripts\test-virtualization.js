#!/usr/bin/env node

/**
 * Performance test script for virtualized user table
 * Tests database queries and frontend rendering performance
 */

import { performance } from 'perf_hooks';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Test database query performance
 */
async function testDatabasePerformance() {
  console.log('🔍 Testing Database Performance...\n');

  const tests = [
    {
      name: 'Basic user list query',
      query: () => supabase
        .from('admin_users_view')
        .select('*')
        .range(0, 49)
    },
    {
      name: 'User list with search filter',
      query: () => supabase
        .from('admin_users_view')
        .select('*')
        .ilike('email', '%test%')
        .range(0, 49)
    },
    {
      name: 'User list with role filter',
      query: () => supabase
        .from('admin_users_view')
        .select('*')
        .eq('role', 'admin')
        .range(0, 49)
    },
    {
      name: 'User list with status filter',
      query: () => supabase
        .from('admin_users_view')
        .select('*')
        .eq('is_active', true)
        .range(0, 49)
    },
    {
      name: 'Admin logs recent query',
      query: () => supabase
        .from('admin_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .range(0, 49)
    },
    {
      name: 'Admin logs with user filter',
      query: () => supabase
        .from('admin_logs')
        .select('*')
        .not('admin_id', 'is', null)
        .order('created_at', { ascending: false })
        .range(0, 49)
    },
    {
      name: 'Admin logs with date range',
      query: () => supabase
        .from('admin_logs')
        .select('*')
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false })
        .range(0, 49)
    }
  ];

  const results = [];

  for (const test of tests) {
    try {
      const startTime = performance.now();
      const { data, error, count } = await test.query();
      const endTime = performance.now();
      
      const duration = endTime - startTime;
      
      if (error) {
        console.log(`❌ ${test.name}: ${error.message}`);
        results.push({ name: test.name, duration: null, error: error.message });
      } else {
        const status = duration < 100 ? '✅' : duration < 500 ? '⚠️' : '❌';
        console.log(`${status} ${test.name}: ${duration.toFixed(2)}ms (${data?.length || 0} records)`);
        results.push({ name: test.name, duration, recordCount: data?.length || 0, totalCount: count });
      }
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.message}`);
      results.push({ name: test.name, duration: null, error: error.message });
    }
  }

  return results;
}

/**
 * Test pagination performance
 */
async function testPaginationPerformance() {
  console.log('\n📄 Testing Pagination Performance...\n');

  const pageSize = 50;
  const totalPages = 5; // Test first 5 pages
  const results = [];

  for (let page = 0; page < totalPages; page++) {
    const offset = page * pageSize;
    
    try {
      const startTime = performance.now();
      const { data, error } = await supabase
        .from('admin_users_view')
        .select('*')
        .order('created_at', { ascending: false })
        .range(offset, offset + pageSize - 1);
      const endTime = performance.now();
      
      const duration = endTime - startTime;
      
      if (error) {
        console.log(`❌ Page ${page + 1}: ${error.message}`);
        results.push({ page: page + 1, duration: null, error: error.message });
      } else {
        const status = duration < 100 ? '✅' : duration < 500 ? '⚠️' : '❌';
        console.log(`${status} Page ${page + 1}: ${duration.toFixed(2)}ms (${data?.length || 0} records)`);
        results.push({ page: page + 1, duration, recordCount: data?.length || 0 });
      }
    } catch (error) {
      console.log(`❌ Page ${page + 1}: ${error.message}`);
      results.push({ page: page + 1, duration: null, error: error.message });
    }
  }

  return results;
}

/**
 * Test index effectiveness
 */
async function testIndexEffectiveness() {
  console.log('\n🗂️ Testing Index Effectiveness...\n');

  const indexTests = [
    {
      name: 'admin_logs created_at index',
      query: `
        EXPLAIN (ANALYZE, BUFFERS) 
        SELECT * FROM admin_logs 
        WHERE created_at > NOW() - INTERVAL '7 days' 
        ORDER BY created_at DESC 
        LIMIT 50;
      `
    },
    {
      name: 'admin_logs admin_id + created_at index',
      query: `
        EXPLAIN (ANALYZE, BUFFERS) 
        SELECT * FROM admin_logs 
        WHERE admin_id IS NOT NULL 
        AND created_at > NOW() - INTERVAL '30 days' 
        ORDER BY created_at DESC 
        LIMIT 50;
      `
    },
    {
      name: 'admin_users_view performance',
      query: `
        EXPLAIN (ANALYZE, BUFFERS) 
        SELECT * FROM admin_users_view 
        WHERE is_active = true 
        ORDER BY created_at DESC 
        LIMIT 50;
      `
    }
  ];

  for (const test of indexTests) {
    try {
      const { data, error } = await supabase.rpc('execute_sql', { sql: test.query });
      
      if (error) {
        console.log(`❌ ${test.name}: ${error.message}`);
      } else {
        console.log(`✅ ${test.name}:`);
        if (data && Array.isArray(data)) {
          data.forEach(row => {
            if (row['QUERY PLAN']) {
              console.log(`   ${row['QUERY PLAN']}`);
            }
          });
        }
        console.log('');
      }
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.message}`);
    }
  }
}

/**
 * Generate performance report
 */
function generateReport(dbResults, paginationResults) {
  console.log('\n📊 Performance Report\n');
  console.log('='.repeat(50));
  
  // Database performance summary
  const successfulDbTests = dbResults.filter(r => r.duration !== null);
  const avgDbTime = successfulDbTests.reduce((sum, r) => sum + r.duration, 0) / successfulDbTests.length;
  
  console.log(`\n📈 Database Performance:`);
  console.log(`   Average query time: ${avgDbTime.toFixed(2)}ms`);
  console.log(`   Successful queries: ${successfulDbTests.length}/${dbResults.length}`);
  console.log(`   Performance rating: ${avgDbTime < 100 ? 'Excellent ✅' : avgDbTime < 500 ? 'Good ⚠️' : 'Needs Improvement ❌'}`);
  
  // Pagination performance summary
  const successfulPageTests = paginationResults.filter(r => r.duration !== null);
  const avgPageTime = successfulPageTests.reduce((sum, r) => sum + r.duration, 0) / successfulPageTests.length;
  
  console.log(`\n📄 Pagination Performance:`);
  console.log(`   Average page load: ${avgPageTime.toFixed(2)}ms`);
  console.log(`   Successful pages: ${successfulPageTests.length}/${paginationResults.length}`);
  console.log(`   Performance rating: ${avgPageTime < 100 ? 'Excellent ✅' : avgPageTime < 500 ? 'Good ⚠️' : 'Needs Improvement ❌'}`);
  
  // Recommendations
  console.log(`\n💡 Recommendations:`);
  if (avgDbTime > 100) {
    console.log(`   - Consider adding more specific indexes`);
    console.log(`   - Review query complexity`);
    console.log(`   - Check database connection pool`);
  }
  if (avgPageTime > 100) {
    console.log(`   - Optimize pagination queries`);
    console.log(`   - Consider reducing page size`);
    console.log(`   - Review network latency`);
  }
  if (avgDbTime < 100 && avgPageTime < 100) {
    console.log(`   - Performance is excellent! 🎉`);
    console.log(`   - Ready for production workloads`);
  }
  
  console.log('\n' + '='.repeat(50));
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🚀 PromptFlow Admin Panel - Virtualization Performance Test\n');
  
  try {
    // Test database connection
    const { data, error } = await supabase.from('admin_users').select('count').limit(1);
    if (error) {
      console.error('❌ Database connection failed:', error.message);
      process.exit(1);
    }
    console.log('✅ Database connection successful\n');
    
    // Run performance tests
    const dbResults = await testDatabasePerformance();
    const paginationResults = await testPaginationPerformance();
    
    // Test indexes (optional - requires special permissions)
    try {
      await testIndexEffectiveness();
    } catch (error) {
      console.log('⚠️ Index analysis skipped (requires elevated permissions)\n');
    }
    
    // Generate report
    generateReport(dbResults, paginationResults);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests();
}
