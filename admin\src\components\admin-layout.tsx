'use client';

import {
  Box,
  Flex,
  useColorModeValue,
  useDisclosure,
} from '@chakra-ui/react';
import { ReactNode, useEffect, useState, Suspense, memo, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { AdminSidebar, AdminSidebarToggle } from './admin-sidebar';
import { RouteLoadingSpinner } from './lazy-routes';
import { ConnectionStatus } from './realtime/connection-status';
import { PerformanceMonitor } from './performance/performance-monitor';
import { useAuthGuard } from '@/hooks/use-admin-auth';

interface AdminLayoutProps {
  children: ReactNode;
}

const AdminLayout = memo(function AdminLayout({ children }: AdminLayoutProps) {
  const [isMounted, setIsMounted] = useState(false);
  const { isOpen: isMobileSidebarOpen, onOpen: onMobileSidebarOpen, onClose: onMobileSidebarClose } = useDisclosure();

  // Use optimized auth guard hook
  const { isLoading, canAccess } = useAuthGuard();

  // Move hooks to top level - fix React hooks violation
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const headerBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // Memoize color values to prevent unnecessary recalculations
  const colors = useMemo(() => ({
    bgColor,
    headerBg,
    borderColor,
  }), [bgColor, headerBg, borderColor]);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Prevent hydration mismatch
  if (!isMounted) {
    return null;
  }

  if (isLoading) {
    return (
      <Box minH="100vh" bg={colors.bgColor} display="flex" alignItems="center" justifyContent="center">
        <RouteLoadingSpinner />
      </Box>
    );
  }

  // Auth guard will handle redirects
  if (!canAccess) {
    return null;
  }

  return (
    <Box minH="100vh" bg={colors.bgColor}>
      {/* Desktop Sidebar */}
      <Box display={{ base: 'none', lg: 'block' }}>
        <AdminSidebar />
      </Box>

      {/* Mobile Sidebar */}
      <AdminSidebar
        isOpen={isMobileSidebarOpen}
        onClose={onMobileSidebarClose}
        isMobile={true}
      />

      {/* Main Content */}
      <Box ml={{ base: 0, lg: '280px' }} minH="100vh">
        {/* Mobile Header */}
        <Box
          display={{ base: 'block', lg: 'none' }}
          bg={colors.headerBg}
          borderBottom="1px"
          borderColor={colors.borderColor}
          p={4}
        >
          <Flex align="center">
            <AdminSidebarToggle onToggle={onMobileSidebarOpen} />
          </Flex>
        </Box>

        {/* Page Content with Suspense for lazy loading */}
        <Box>
          <Suspense fallback={<RouteLoadingSpinner />}>
            {children}
          </Suspense>
        </Box>
      </Box>

      {/* Connection Status Indicator */}
      <ConnectionStatus />

      {/* Performance Monitor (Development only) */}
      <PerformanceMonitor showDetails={false} position="bottom-right" />
    </Box>
  );
});

export { AdminLayout };
