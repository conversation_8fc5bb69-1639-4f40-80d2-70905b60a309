'use client';

import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Avatar,
  Badge,
  Icon,
  Divider,
  useColorModeValue,
  Flex,
  Spacer,
  IconButton,
  useDisclosure,
  Drawer,
  Drawer<PERSON>ody,
  Drawer<PERSON>eader,
  Drawer<PERSON><PERSON>lay,
  <PERSON>er<PERSON>ontent,
  DrawerCloseButton,
} from '@chakra-ui/react';
import { useRouter, usePathname } from 'next/navigation';
import { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { useCurrentUser } from '@/hooks/use-admin-auth';
import {
  LayoutDashboard,
  Users,
  FolderOpen,
  FileText,
  MessageSquare,
  Settings,
  LogOut,
  Menu,
  X,
  Crown,
} from 'lucide-react';

interface AdminSidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
  isMobile?: boolean;
}

interface CurrentAdmin {
  id: string;
  user_id: string;
  role: string;
  permissions: Record<string, unknown>;
  email: string;
  created_at: string;
  updated_at: string;
}

const navigationItems = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    href: '/users',
    icon: Users,
  },
  {
    name: 'Projeler',
    href: '/projects',
    icon: FolderOpen,
  },
  {
    name: 'Prompt\'lar',
    href: '/prompts',
    icon: FileText,
  },
  {
    name: 'Context\'ler',
    href: '/contexts',
    icon: MessageSquare,
  },
  {
    name: 'Kullanıcı Planları',
    href: '/plans',
    icon: Crown,
  },
  {
    name: 'Ayarlar',
    href: '/settings',
    icon: Settings,
  },
];

export const AdminSidebar = memo(function AdminSidebar({ isOpen = true, onClose, isMobile = false }: AdminSidebarProps) {
  const router = useRouter();
  const pathname = usePathname();

  // Use optimized auth hook
  const { user, adminUser, isLoading, logout, isLoggingOut } = useCurrentUser();

  // Move hooks to top level - fix React hooks violation
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');
  const activeBg = useColorModeValue('blue.50', 'blue.900');
  const activeColor = useColorModeValue('blue.600', 'blue.200');

  // Memoize color values to prevent unnecessary recalculations
  const colors = useMemo(() => ({
    bgColor,
    borderColor,
    hoverBg,
    activeBg,
    activeColor,
  }), [bgColor, borderColor, hoverBg, activeBg, activeColor]);

  // Memoize logout handler
  const handleLogout = useCallback(() => {
    logout();
  }, [logout]);

  // Memoize navigation handler
  const handleNavigation = useCallback((href: string) => {
    router.push(href);
    if (isMobile && onClose) {
      onClose();
    }
  }, [router, isMobile, onClose]);

  const SidebarContent = useMemo(() => (
    <Box h="full" bg={colors.bgColor} borderRight="1px" borderColor={colors.borderColor}>
      <VStack h="full" spacing={0} align="stretch">
        {/* Header */}
        <Box p={6} borderBottom="1px" borderColor={colors.borderColor}>
          <HStack justify="space-between" align="center">
            <HStack spacing={2}>
              <Box
                as="img"
                src="/logo.png"
                alt="Promptbir Logo"
                h="6"
                w="auto"
              />
              <Text fontSize="xl" fontWeight="bold" color="blue.600">
                Promptbir Admin
              </Text>
            </HStack>
            {isMobile && onClose && (
              <IconButton
                aria-label="Kapat"
                icon={<X />}
                variant="ghost"
                size="sm"
                onClick={onClose}
              />
            )}
          </HStack>
        </Box>

        {/* Navigation */}
        <Box flex="1" p={4}>
          <VStack spacing={2} align="stretch">
            {navigationItems.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Button
                  key={item.href}
                  leftIcon={<Icon as={item.icon} />}
                  variant="ghost"
                  justifyContent="flex-start"
                  bg={isActive ? colors.activeBg : 'transparent'}
                  color={isActive ? colors.activeColor : 'inherit'}
                  _hover={{
                    bg: isActive ? colors.activeBg : colors.hoverBg,
                  }}
                  onClick={() => handleNavigation(item.href)}
                  size="md"
                  fontWeight={isActive ? 'semibold' : 'normal'}
                >
                  {item.name}
                </Button>
              );
            })}
          </VStack>
        </Box>

        {/* User Info & Logout */}
        <Box p={4} borderTop="1px" borderColor={colors.borderColor}>
          {!isLoading && adminUser && (
            <VStack spacing={4} align="stretch">
              <HStack spacing={3}>
                <Avatar size="sm" name={user?.email || ''} />
                <Box flex="1" minW="0">
                  <Text fontSize="sm" fontWeight="medium" isTruncated>
                    {user?.email}
                  </Text>
                  <Badge colorScheme="blue" size="sm">
                    {adminUser.role}
                  </Badge>
                </Box>
              </HStack>
              
              <Button
                leftIcon={<Icon as={LogOut} />}
                variant="outline"
                size="sm"
                onClick={handleLogout}
                colorScheme="red"
                isLoading={isLoggingOut}
                loadingText="Çıkış yapılıyor..."
              >
                Çıkış Yap
              </Button>
            </VStack>
          )}
        </Box>
      </VStack>
    </Box>
  ), [colors, adminUser, user, isLoading, isLoggingOut, pathname, handleNavigation, handleLogout, isMobile, onClose]);

  if (isMobile) {
    return (
      <Drawer isOpen={isOpen} placement="left" onClose={onClose || (() => {})}>
        <DrawerOverlay />
        <DrawerContent maxW="280px">
          {SidebarContent}
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Box
      w="280px"
      h="100vh"
      position="fixed"
      left={0}
      top={0}
      zIndex={1000}
      transform={isOpen ? 'translateX(0)' : 'translateX(-100%)'}
      transition="transform 0.3s ease-in-out"
    >
      {SidebarContent}
    </Box>
  );
});

// Mobile toggle button component - memoized to prevent unnecessary re-renders
export const AdminSidebarToggle = memo(function AdminSidebarToggle({ onToggle }: { onToggle: () => void }) {
  return (
    <IconButton
      aria-label="Menüyü aç"
      icon={<Menu />}
      variant="ghost"
      size="md"
      onClick={onToggle}
      display={{ base: 'flex', lg: 'none' }}
    />
  );
});
