'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabaseBrowser as supabase } from '@/lib/supabase-browser'

// Get popular hashtags with usage count
export function usePopularHashtags(projectId: string | null, limit: number = 20) {
  return useQuery({
    queryKey: ['popular-hashtags', projectId, limit],
    queryFn: async (): Promise<{ hashtag: string; count: number }[]> => {
      if (!projectId) return []

      // Get all prompts for the project with their tags
      const { data: prompts, error } = await supabase
        .from('prompts')
        .select('tags')
        .eq('project_id', projectId)

      if (error) {
        throw new Error(error.message)
      }

      // Count hashtag occurrences
      const hashtagCounts: Record<string, number> = {}

      prompts?.forEach(prompt => {
        if (prompt.tags && Array.isArray(prompt.tags)) {
          prompt.tags.forEach(tag => {
            if (typeof tag === 'string' && tag.trim()) {
              // Add # prefix if not present, then normalize to lowercase
              const hashtag = tag.startsWith('#') ? tag.toLowerCase() : `#${tag.toLowerCase()}`
              hashtagCounts[hashtag] = (hashtagCounts[hashtag] || 0) + 1
            }
          })
        }
      })

      // Convert to array and sort by count
      return Object.entries(hashtagCounts)
        .map(([hashtag, count]) => ({ hashtag, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, limit)
    },
    enabled: !!projectId,
  })
}

// Get popular categories with usage count
export function usePopularCategories(projectId: string | null, limit: number = 10) {
  return useQuery({
    queryKey: ['popular-categories', projectId, limit],
    queryFn: async (): Promise<{ category: string; count: number }[]> => {
      if (!projectId) return []

      // Get category counts using SQL aggregation
      const { data, error } = await supabase
        .from('prompts')
        .select('category')
        .eq('project_id', projectId)
        .not('category', 'is', null)

      if (error) {
        throw new Error(error.message)
      }

      // Count category occurrences
      const categoryCounts: Record<string, number> = {}
      
      data?.forEach(prompt => {
        if (prompt.category) {
          const category = prompt.category.toLowerCase()
          categoryCounts[category] = (categoryCounts[category] || 0) + 1
        }
      })

      // Convert to array and sort by count
      return Object.entries(categoryCounts)
        .map(([category, count]) => ({ category, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, limit)
    },
    enabled: !!projectId,
  })
}

// Get all unique hashtags for a project (for autocomplete)
export function useAllHashtags(projectId: string | null) {
  return useQuery({
    queryKey: ['all-hashtags', projectId],
    queryFn: async (): Promise<string[]> => {
      if (!projectId) return []

      const { data: prompts, error } = await supabase
        .from('prompts')
        .select('tags')
        .eq('project_id', projectId)

      if (error) {
        throw new Error(error.message)
      }

      // Extract all unique hashtags
      const allHashtags = new Set<string>()
      
      prompts?.forEach(prompt => {
        if (prompt.tags && Array.isArray(prompt.tags)) {
          prompt.tags.forEach(tag => {
            if (typeof tag === 'string' && tag.trim()) {
              // Add # prefix if not present, then normalize to lowercase
              const hashtag = tag.startsWith('#') ? tag.toLowerCase() : `#${tag.toLowerCase()}`
              allHashtags.add(hashtag)
            }
          })
        }
      })

      return Array.from(allHashtags).sort()
    },
    enabled: !!projectId,
  })
}

// Get all unique categories for a project (for autocomplete)
export function useAllCategories(projectId: string | null) {
  return useQuery({
    queryKey: ['all-categories', projectId],
    queryFn: async (): Promise<string[]> => {
      if (!projectId) return []

      const { data, error } = await supabase
        .from('prompts')
        .select('category')
        .eq('project_id', projectId)
        .not('category', 'is', null)

      if (error) {
        throw new Error(error.message)
      }

      // Extract all unique categories
      const allCategories = new Set<string>()
      
      data?.forEach(prompt => {
        if (prompt.category) {
          allCategories.add(prompt.category.toLowerCase())
        }
      })

      return Array.from(allCategories).sort()
    },
    enabled: !!projectId,
  })
}

// Get prompts filtered by hashtags and/or category
export function useFilteredPrompts(
  projectId: string | null,
  filters: {
    hashtags?: string[]
    category?: string
    searchQuery?: string
  }
) {
  return useQuery({
    queryKey: ['filtered-prompts', projectId, filters],
    queryFn: async () => {
      if (!projectId) return []

      let query = supabase
        .from('prompts')
        .select('*')
        .eq('project_id', projectId)
        .order('order_index', { ascending: true })

      // Filter by category
      if (filters.category) {
        query = query.eq('category', filters.category)
      }

      // Filter by search query
      if (filters.searchQuery) {
        query = query.ilike('prompt_text', `%${filters.searchQuery}%`)
      }

      const { data: prompts, error } = await query

      if (error) {
        throw new Error(error.message)
      }

      // Filter by hashtags (client-side since we need to check array contents)
      let filteredPrompts = prompts || []
      
      if (filters.hashtags && filters.hashtags.length > 0) {
        filteredPrompts = filteredPrompts.filter(prompt => {
          if (!prompt.tags || !Array.isArray(prompt.tags)) return false
          
          return filters.hashtags!.some(hashtag => 
            prompt.tags.some((tag: string) => 
              tag.toLowerCase() === hashtag.toLowerCase()
            )
          )
        })
      }

      return filteredPrompts
    },
    enabled: !!projectId,
  })
}

// Update hashtag usage statistics
export function useUpdateHashtagStats() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ 
      projectId, 
      hashtags 
    }: { 
      projectId: string
      hashtags: string[] 
    }) => {
      // This could be implemented as a database function for better performance
      // For now, we'll rely on the query invalidation to refresh the popular hashtags
      return { projectId, hashtags }
    },
    onSuccess: (data) => {
      // Invalidate popular hashtags query to refresh counts
      queryClient.invalidateQueries({ 
        queryKey: ['popular-hashtags', data.projectId] 
      })
      queryClient.invalidateQueries({ 
        queryKey: ['all-hashtags', data.projectId] 
      })
    },
  })
}

// Update category usage statistics
export function useUpdateCategoryStats() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ 
      projectId, 
      category 
    }: { 
      projectId: string
      category: string 
    }) => {
      return { projectId, category }
    },
    onSuccess: (data) => {
      // Invalidate popular categories query to refresh counts
      queryClient.invalidateQueries({ 
        queryKey: ['popular-categories', data.projectId] 
      })
      queryClient.invalidateQueries({ 
        queryKey: ['all-categories', data.projectId] 
      })
    },
  })
}
