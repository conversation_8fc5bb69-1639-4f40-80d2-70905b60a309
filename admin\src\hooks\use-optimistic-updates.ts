import { useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { queryKeys } from '@/lib/query-client';

// Hook for optimistic prompt updates
export function useOptimisticPrompts() {
  const queryClient = useQueryClient();

  const updatePromptOptimistically = useCallback(
    (promptId: string, updates: Partial<any>) => {
      // Update all prompt list queries
      queryClient.setQueriesData(
        { queryKey: queryKeys.prompts.all },
        (oldData: any) => {
          if (!oldData?.prompts) return oldData;

          return {
            ...oldData,
            prompts: oldData.prompts.map((prompt: any) =>
              prompt.id === promptId ? { ...prompt, ...updates } : prompt
            ),
          };
        }
      );

      // Update specific prompt query
      queryClient.setQueryData(
        queryKeys.prompts.detail(promptId),
        (oldData: any) => {
          if (!oldData) return oldData;
          return { ...oldData, ...updates };
        }
      );
    },
    [queryClient]
  );

  const addPromptOptimistically = useCallback(
    (newPrompt: any) => {
      queryClient.setQueriesData(
        { queryKey: queryKeys.prompts.all },
        (oldData: any) => {
          if (!oldData?.prompts) return oldData;

          return {
            ...oldData,
            prompts: [newPrompt, ...oldData.prompts],
            total: oldData.total + 1,
          };
        }
      );
    },
    [queryClient]
  );

  const removePromptOptimistically = useCallback(
    (promptId: string) => {
      queryClient.setQueriesData(
        { queryKey: queryKeys.prompts.all },
        (oldData: any) => {
          if (!oldData?.prompts) return oldData;

          return {
            ...oldData,
            prompts: oldData.prompts.filter((prompt: any) => prompt.id !== promptId),
            total: Math.max(0, oldData.total - 1),
          };
        }
      );

      // Remove specific prompt query
      queryClient.removeQueries({
        queryKey: queryKeys.prompts.detail(promptId),
      });
    },
    [queryClient]
  );

  const reorderPromptsOptimistically = useCallback(
    (reorderedPrompts: any[]) => {
      queryClient.setQueriesData(
        { queryKey: queryKeys.prompts.all },
        (oldData: any) => {
          if (!oldData?.prompts) return oldData;

          return {
            ...oldData,
            prompts: reorderedPrompts,
          };
        }
      );
    },
    [queryClient]
  );

  return {
    updatePromptOptimistically,
    addPromptOptimistically,
    removePromptOptimistically,
    reorderPromptsOptimistically,
  };
}

// Hook for optimistic project updates
export function useOptimisticProjects() {
  const queryClient = useQueryClient();

  const updateProjectOptimistically = useCallback(
    (projectId: string, updates: Partial<any>) => {
      queryClient.setQueriesData(
        { queryKey: queryKeys.projects.all },
        (oldData: any) => {
          if (!oldData?.projects) return oldData;

          return {
            ...oldData,
            projects: oldData.projects.map((project: any) =>
              project.id === projectId ? { ...project, ...updates } : project
            ),
          };
        }
      );

      queryClient.setQueryData(
        queryKeys.projects.detail(projectId),
        (oldData: any) => {
          if (!oldData) return oldData;
          return { ...oldData, ...updates };
        }
      );
    },
    [queryClient]
  );

  const addProjectOptimistically = useCallback(
    (newProject: any) => {
      queryClient.setQueriesData(
        { queryKey: queryKeys.projects.all },
        (oldData: any) => {
          if (!oldData?.projects) return oldData;

          return {
            ...oldData,
            projects: [newProject, ...oldData.projects],
            total: oldData.total + 1,
          };
        }
      );
    },
    [queryClient]
  );

  const removeProjectOptimistically = useCallback(
    (projectId: string) => {
      queryClient.setQueriesData(
        { queryKey: queryKeys.projects.all },
        (oldData: any) => {
          if (!oldData?.projects) return oldData;

          return {
            ...oldData,
            projects: oldData.projects.filter((project: any) => project.id !== projectId),
            total: Math.max(0, oldData.total - 1),
          };
        }
      );

      queryClient.removeQueries({
        queryKey: queryKeys.projects.detail(projectId),
      });
    },
    [queryClient]
  );

  return {
    updateProjectOptimistically,
    addProjectOptimistically,
    removeProjectOptimistically,
  };
}

// Hook for optimistic user updates
export function useOptimisticUsers() {
  const queryClient = useQueryClient();

  const updateUserOptimistically = useCallback(
    (userId: string, updates: Partial<any>) => {
      queryClient.setQueriesData(
        { queryKey: queryKeys.users.all },
        (oldData: any) => {
          if (!oldData?.users) return oldData;

          return {
            ...oldData,
            users: oldData.users.map((user: any) =>
              user.id === userId ? { ...user, ...updates } : user
            ),
          };
        }
      );

      queryClient.setQueryData(
        queryKeys.users.detail(userId),
        (oldData: any) => {
          if (!oldData) return oldData;
          return { ...oldData, ...updates };
        }
      );
    },
    [queryClient]
  );

  const addUserOptimistically = useCallback(
    (newUser: any) => {
      queryClient.setQueriesData(
        { queryKey: queryKeys.users.all },
        (oldData: any) => {
          if (!oldData?.users) return oldData;

          return {
            ...oldData,
            users: [newUser, ...oldData.users],
            total: oldData.total + 1,
          };
        }
      );

      // Update user stats
      queryClient.setQueryData(
        queryKeys.users.stats,
        (oldData: any) => {
          if (!oldData) return oldData;
          return {
            ...oldData,
            totalUsers: oldData.totalUsers + 1,
          };
        }
      );
    },
    [queryClient]
  );

  const removeUserOptimistically = useCallback(
    (userId: string) => {
      queryClient.setQueriesData(
        { queryKey: queryKeys.users.all },
        (oldData: any) => {
          if (!oldData?.users) return oldData;

          return {
            ...oldData,
            users: oldData.users.filter((user: any) => user.id !== userId),
            total: Math.max(0, oldData.total - 1),
          };
        }
      );

      queryClient.removeQueries({
        queryKey: queryKeys.users.detail(userId),
      });

      // Update user stats
      queryClient.setQueryData(
        queryKeys.users.stats,
        (oldData: any) => {
          if (!oldData) return oldData;
          return {
            ...oldData,
            totalUsers: Math.max(0, oldData.totalUsers - 1),
          };
        }
      );
    },
    [queryClient]
  );

  return {
    updateUserOptimistically,
    addUserOptimistically,
    removeUserOptimistically,
  };
}

// Generic optimistic update hook
export function useOptimisticUpdate() {
  const queryClient = useQueryClient();

  const performOptimisticUpdate = useCallback(
    <T>(
      queryKey: any[],
      updater: (oldData: T) => T,
      rollbackData?: T
    ) => {
      // Store the previous data for potential rollback
      const previousData = queryClient.getQueryData<T>(queryKey);

      // Perform optimistic update
      queryClient.setQueryData<T>(queryKey, updater);

      // Return rollback function
      return () => {
        if (rollbackData !== undefined) {
          queryClient.setQueryData<T>(queryKey, rollbackData);
        } else if (previousData !== undefined) {
          queryClient.setQueryData<T>(queryKey, previousData);
        }
      };
    },
    [queryClient]
  );

  return { performOptimisticUpdate };
}
