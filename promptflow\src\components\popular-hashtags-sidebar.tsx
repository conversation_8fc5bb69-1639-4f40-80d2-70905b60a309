'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import {
  Hash,
  Search,
  TrendingUp,
  Plus,
  Folder,
  X
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { usePopularHashtags, usePopularCategories } from '@/hooks/use-hashtags'
import { CategorizationAnalytics } from '@/components/categorization-analytics'

interface PopularHashtagsSidebarProps {
  projectId: string | null
  onHashtagClick?: (hashtag: string) => void
  onCategoryClick?: (category: string) => void
  selectedHashtags?: string[]
  selectedCategory?: string | null
  className?: string
}

export function PopularHashtagsSidebar({
  projectId,
  onHashtagClick,
  onCategoryClick,
  selectedHashtags = [],
  selectedCategory = null,
  className
}: PopularHashtagsSidebarProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [showCategories, setShowCategories] = useState(true)
  const [showHashtags, setShowHashtags] = useState(true)

  const { data: popularHashtags = [], isLoading: hashtagsLoading } = usePopularHashtags(projectId)
  const { data: popularCategories = [], isLoading: categoriesLoading } = usePopularCategories(projectId)

  // Filter hashtags based on search
  const filteredHashtags = popularHashtags.filter(({ hashtag }) =>
    hashtag.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Filter categories based on search
  const filteredCategories = popularCategories.filter(({ category }) =>
    category.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleHashtagClick = (hashtag: string) => {
    onHashtagClick?.(hashtag)
  }

  const handleCategoryClick = (category: string) => {
    onCategoryClick?.(category)
  }

  const clearFilters = () => {
    setSearchQuery('')
  }

  return (
    <div className={cn("w-64 bg-white border-l border-gray-200 flex flex-col relative z-50", className)}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center gap-2 mb-3">
          <TrendingUp className="w-4 h-4 text-blue-600" />
          <h3 className="font-medium text-sm">AI Tags</h3>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            type="text"
            placeholder="Etiket ve klasör ara..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 text-sm"
          />
          {searchQuery && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              onClick={clearFilters}
            >
              <X className="w-3 h-3" />
            </Button>
          )}
        </div>
      </div>

      <ScrollArea className="flex-1 overflow-hidden">
        <div className="p-4 space-y-4 max-w-full">
          {/* Categories Section */}
          {showCategories && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Folder className="w-3 h-3 text-gray-500" />
                  <span className="text-xs font-medium text-gray-700 uppercase tracking-wide">
                    Klasörler
                  </span>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 text-xs"
                  onClick={() => setShowCategories(!showCategories)}
                >
                  {showCategories ? 'Gizle' : 'Göster'}
                </Button>
              </div>

              {categoriesLoading ? (
                <div className="text-xs text-gray-500">Klasörler yükleniyor...</div>
              ) : filteredCategories.length > 0 ? (
                <div className="space-y-1">
                  {filteredCategories.map(({ category, count }) => (
                    <Button
                      key={category}
                      type="button"
                      variant="ghost"
                      size="sm"
                      className={cn(
                        "w-full justify-between h-auto p-2 text-xs hover:bg-gray-100",
                        selectedCategory === category && "bg-blue-50 text-blue-700"
                      )}
                      onClick={() => handleCategoryClick(category)}
                    >
                      <div className="flex items-center gap-2 min-w-0">
                        <Folder className="w-3 h-3 shrink-0" />
                        <span className="truncate">{category}</span>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {count}
                      </Badge>
                    </Button>
                  ))}
                </div>
              ) : (
                <div className="text-xs text-gray-500">
                  {searchQuery ? 'Eşleşen klasör bulunamadı' : 'Henüz klasör yok'}
                </div>
              )}
            </div>
          )}

          {showCategories && showHashtags && (
            <Separator />
          )}

          {/* Hashtags Section */}
          {showHashtags && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Hash className="w-3 h-3 text-gray-500" />
                  <span className="text-xs font-medium text-gray-700 uppercase tracking-wide">
                    Popüler Etiketler
                  </span>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 text-xs"
                  onClick={() => setShowHashtags(!showHashtags)}
                >
                  {showHashtags ? 'Gizle' : 'Göster'}
                </Button>
              </div>

              {hashtagsLoading ? (
                <div className="text-xs text-gray-500">Etiketler yükleniyor...</div>
              ) : filteredHashtags.length > 0 ? (
                <div className="space-y-1">
                  {filteredHashtags.map(({ hashtag, count }) => (
                    <Button
                      key={hashtag}
                      type="button"
                      variant="ghost"
                      size="sm"
                      className={cn(
                        "w-full justify-between h-auto p-2 text-xs hover:bg-gray-100",
                        selectedHashtags.includes(hashtag) && "bg-blue-50 text-blue-700"
                      )}
                      onClick={() => handleHashtagClick(hashtag)}
                    >
                      <div className="flex items-center gap-2 min-w-0">
                        <Hash className="w-3 h-3 shrink-0" />
                        <span className="truncate">{hashtag.replace('#', '')}</span>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {count}
                      </Badge>
                    </Button>
                  ))}
                </div>
              ) : (
                <div className="text-xs text-gray-500">
                  {searchQuery ? 'Eşleşen etiket bulunamadı' : 'Henüz etiket yok'}
                </div>
              )}
            </div>
          )}

          {/* Quick Actions */}
          <Separator />
          <div className="space-y-2">
            <span className="text-xs font-medium text-gray-700 uppercase tracking-wide">
              Hızlı İşlemler
            </span>
            <div className="space-y-1">
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="w-full justify-start text-xs"
                onClick={() => onHashtagClick?.('#new')}
              >
                <Plus className="w-3 h-3 mr-2" />
                #yeni etiketi ekle
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="w-full justify-start text-xs"
                onClick={() => onCategoryClick?.('/new')}
              >
                <Folder className="w-3 h-3 mr-2" />
                /yeni klasörü oluştur
              </Button>
            </div>
          </div>

          {/* Analytics */}
          {projectId && (
            <>
              <Separator />
              <div className="max-w-full overflow-hidden">
                <CategorizationAnalytics
                  projectId={projectId}
                  className="px-0 w-full"
                />
              </div>
            </>
          )}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="text-xs text-gray-500 text-center">
          Filtrelemek için tıklayın • Sayılar kullanım sayısını gösterir
        </div>
      </div>
    </div>
  )
}
