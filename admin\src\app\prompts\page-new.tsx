'use client';

import { useState, useCallback } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  HStack,
  VStack,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  useToast,
  useColorModeValue,
  Flex,
  Badge,
  Card,
  CardHeader,
  CardBody,
} from '@chakra-ui/react';
import { Search, Plus, Filter, Users } from 'lucide-react';
import { AdminLayout } from '@/components/admin-layout';
import { usePrompts } from '@/hooks/use-prompts';
import { SortablePromptList } from '@/components/drag-drop/sortable-prompt-list';
import { MultiSelectToolbar } from '@/components/multi-select/multi-select-toolbar';
import { useMultiSelectActions, useIsMultiSelectMode, useSearchActions } from '@/store/admin-store';

interface Prompt {
  id: string;
  prompt_text: string;
  order_index: number;
  is_used: boolean;
  created_at: string;
  updated_at: string;
  project_id: string;
  user_id: string;
}

export default function PromptsPageNew() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterProject, setFilterProject] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);

  const toast = useToast();
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');

  // Store actions
  const { toggleMultiSelectMode } = useMultiSelectActions();
  const isMultiSelectMode = useIsMultiSelectMode();

  // Data fetching with optimized filters
  const { data: promptsData, isLoading } = usePrompts({
    page: currentPage,
    limit: 20,
    search: searchTerm || undefined,
    projectId: filterProject !== 'all' ? filterProject : undefined,
  });

  const prompts = promptsData?.prompts || [];
  const totalPrompts = promptsData?.total || 0;

  // Handle prompt reordering
  const handleReorder = useCallback(async (reorderedPrompts: Prompt[]) => {
    try {
      // Update order_index for all prompts
      const updates = reorderedPrompts.map(prompt => ({
        id: prompt.id,
        order_index: prompt.order_index,
      }));

      // Call bulk update API
      // await bulkUpdatePrompts(updates);

      toast({
        title: 'Başarılı',
        description: 'Prompt sıralaması güncellendi',
        status: 'success',
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: 'Hata',
        description: 'Sıralama güncellenirken bir hata oluştu',
        status: 'error',
        duration: 5000,
      });
    }
  }, [toast]);

  // Handle prompt editing
  const handleEdit = useCallback(async (prompt: Prompt) => {
    try {
      // Call update API
      // await updatePrompt(prompt.id, { prompt_text: prompt.prompt_text });

      toast({
        title: 'Başarılı',
        description: 'Prompt güncellendi',
        status: 'success',
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: 'Hata',
        description: 'Prompt güncellenirken bir hata oluştu',
        status: 'error',
        duration: 5000,
      });
    }
  }, [toast]);

  // Handle prompt deletion
  const handleDelete = useCallback(async (promptId: string) => {
    try {
      // Call delete API
      // await deletePrompt(promptId);

      toast({
        title: 'Başarılı',
        description: 'Prompt silindi',
        status: 'success',
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: 'Hata',
        description: 'Prompt silinirken bir hata oluştu',
        status: 'error',
        duration: 5000,
      });
    }
  }, [toast]);

  // Handle toggle used status
  const handleToggleUsed = useCallback(async (promptId: string, isUsed: boolean) => {
    try {
      // Call update API
      // await updatePrompt(promptId, { is_used: isUsed });

      toast({
        title: 'Başarılı',
        description: `Prompt ${isUsed ? 'kullanıldı' : 'sıfırlandı'}`,
        status: 'success',
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: 'Hata',
        description: 'Durum güncellenirken bir hata oluştu',
        status: 'error',
        duration: 5000,
      });
    }
  }, [toast]);

  // Bulk operations
  const handleBulkDelete = useCallback(async (ids: string[]) => {
    try {
      // Call bulk delete API
      // await bulkDeletePrompts(ids);

      toast({
        title: 'Başarılı',
        description: `${ids.length} prompt silindi`,
        status: 'success',
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: 'Hata',
        description: 'Toplu silme işlemi başarısız',
        status: 'error',
        duration: 5000,
      });
    }
  }, [toast]);

  const handleBulkMarkAsUsed = useCallback(async (ids: string[]) => {
    try {
      // Call bulk update API
      // await bulkMarkAsUsed(ids);

      toast({
        title: 'Başarılı',
        description: `${ids.length} prompt kullanıldı olarak işaretlendi`,
        status: 'success',
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: 'Hata',
        description: 'Toplu güncelleme işlemi başarısız',
        status: 'error',
        duration: 5000,
      });
    }
  }, [toast]);

  const handleBulkCopy = useCallback(async (ids: string[]) => {
    try {
      const selectedPrompts = prompts.filter(p => ids.includes(p.id));
      const combinedText = selectedPrompts.map(p => p.prompt_text).join('\n\n');
      
      await navigator.clipboard.writeText(combinedText);
      
      toast({
        title: 'Başarılı',
        description: `${ids.length} prompt kopyalandı`,
        status: 'success',
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: 'Hata',
        description: 'Kopyalama işlemi başarısız',
        status: 'error',
        duration: 5000,
      });
    }
  }, [prompts, toast]);

  return (
    <AdminLayout>
      <Container maxW="7xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <Box>
            <Heading size="xl" mb={2}>
              Prompt Yönetimi
            </Heading>
            <Text color="gray.600">
              Tüm prompt'ları görüntüleyin, düzenleyin ve yönetin
            </Text>
          </Box>

          {/* Stats Cards */}
          <HStack spacing={6}>
            <Card bg={cardBg} shadow="sm" flex={1}>
              <CardBody>
                <HStack>
                  <Box>
                    <Text fontSize="2xl" fontWeight="bold" color="blue.600">
                      {totalPrompts}
                    </Text>
                    <Text fontSize="sm" color="gray.600">
                      Toplam Prompt
                    </Text>
                  </Box>
                </HStack>
              </CardBody>
            </Card>

            <Card bg={cardBg} shadow="sm" flex={1}>
              <CardBody>
                <HStack>
                  <Box>
                    <Text fontSize="2xl" fontWeight="bold" color="green.600">
                      {prompts.filter(p => p.is_used).length}
                    </Text>
                    <Text fontSize="sm" color="gray.600">
                      Kullanılan
                    </Text>
                  </Box>
                </HStack>
              </CardBody>
            </Card>

            <Card bg={cardBg} shadow="sm" flex={1}>
              <CardBody>
                <HStack>
                  <Box>
                    <Text fontSize="2xl" fontWeight="bold" color="orange.600">
                      {prompts.filter(p => !p.is_used).length}
                    </Text>
                    <Text fontSize="sm" color="gray.600">
                      Bekleyen
                    </Text>
                  </Box>
                </HStack>
              </CardBody>
            </Card>
          </HStack>

          {/* Controls */}
          <Card bg={cardBg} shadow="sm">
            <CardHeader>
              <Flex justify="space-between" align="center" wrap="wrap" gap={4}>
                <HStack spacing={4} flex={1}>
                  <InputGroup maxW="300px">
                    <InputLeftElement>
                      <Search size={16} />
                    </InputLeftElement>
                    <Input
                      placeholder="Prompt ara..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </InputGroup>

                  <Select
                    maxW="200px"
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                  >
                    <option value="all">Tüm Durumlar</option>
                    <option value="used">Kullanılan</option>
                    <option value="unused">Bekleyen</option>
                  </Select>
                </HStack>

                <HStack spacing={2}>
                  <Button
                    leftIcon={<Users size={16} />}
                    variant={isMultiSelectMode ? 'solid' : 'outline'}
                    colorScheme={isMultiSelectMode ? 'blue' : 'gray'}
                    onClick={toggleMultiSelectMode}
                    size="sm"
                  >
                    {isMultiSelectMode ? 'Çoklu Seçim Aktif' : 'Çoklu Seçim'}
                  </Button>

                  <Button
                    leftIcon={<Plus size={16} />}
                    colorScheme="blue"
                    size="sm"
                  >
                    Yeni Prompt
                  </Button>
                </HStack>
              </Flex>
            </CardHeader>
          </Card>

          {/* Prompts List */}
          <Card bg={cardBg} shadow="sm">
            <CardBody>
              <SortablePromptList
                prompts={prompts}
                onReorder={handleReorder}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onToggleUsed={handleToggleUsed}
                isLoading={isLoading}
              />
            </CardBody>
          </Card>
        </VStack>
      </Container>

      {/* Multi-select toolbar */}
      <MultiSelectToolbar
        onBulkDelete={handleBulkDelete}
        onBulkMarkAsUsed={handleBulkMarkAsUsed}
        onBulkCopy={handleBulkCopy}
        itemType="prompts"
      />
    </AdminLayout>
  );
}
