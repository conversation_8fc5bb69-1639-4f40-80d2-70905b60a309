'use client';

import {
  Box,
  Button,
  Container,
  FormControl,
  FormLabel,
  Input,
  Stack,
  Text,
  useToast,
  VStack,
  HStack,
  Icon,
  Heading,
  FormErrorMessage,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { supabase, signOut, isAdmin as checkIsAdminFunction, waitForSessionEstablishment } from '@/lib/supabase';
import { authApi } from '@/lib/api';
import { navigateWithSession, debugSessionStorage, initializeSessionPersistence } from '@/lib/client-session';
import { useCurrentUser } from '@/hooks/use-admin-auth';
import { Shield, Lock, User, LogOut } from 'lucide-react';

export default function AdminLogin() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{ email?: string; password?: string }>({});
  const toast = useToast();
  const router = useRouter();

  // Use the proper auth hook instead of manual auth checking
  const { user, isAdmin, isLoading: isAuthLoading, logout } = useCurrentUser();

  // Initialize session persistence system
  useEffect(() => {
    initializeSessionPersistence();
  }, []);

  // Handle authenticated admin users
  useEffect(() => {
    if (!isAuthLoading && user && isAdmin) {
      // Redirect to dashboard if user is already authenticated and is admin
      router.push('/dashboard');
    }
  }, [user, isAdmin, isAuthLoading, router]);

  // Logout handler
  const handleLogout = async () => {
    try {
      await logout();
      toast({
        title: 'Çıkış Başarılı',
        description: 'Başarıyla çıkış yapıldı.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Logout error:', error);
      toast({
        title: 'Çıkış Hatası',
        description: 'Çıkış yapılırken bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const validateForm = () => {
    const newErrors: { email?: string; password?: string } = {};
    
    if (!email) {
      newErrors.email = 'E-posta adresi gereklidir';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Geçerli bir e-posta adresi giriniz';
    }
    
    if (!password) {
      newErrors.password = 'Şifre gereklidir';
    } else if (password.length < 6) {
      newErrors.password = 'Şifre en az 6 karakter olmalıdır';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);
    setErrors({});

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        toast({
          title: 'Giriş Hatası',
          description: error.message === 'Invalid login credentials'
            ? 'E-posta veya şifre hatalı'
            : error.message,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
        return;
      }

      if (data.user && data.session) {
        console.log('🎯 Login successful, processing session...');

        try {
          // Wait for session to be properly established
          const sessionEstablished = await waitForSessionEstablishment();

          if (!sessionEstablished) {
            console.error('❌ Session establishment failed');
            toast({
              title: 'Oturum Hatası',
              description: 'Oturum kurulurken bir hata oluştu. Lütfen tekrar deneyin.',
              status: 'error',
              duration: 5000,
              isClosable: true,
            });
            return;
          }

          // Check admin status with fallback
          console.log('🔍 Checking admin status for user:', data.user.id);
          let adminStatus = false;

          try {
            // Try primary method first
            if (typeof checkIsAdminFunction === 'function') {
              adminStatus = await checkIsAdminFunction(data.user.id);
            } else {
              // Fallback to authApi method
              console.log('Using fallback admin check method');
              adminStatus = await authApi.checkAdminStatus(data.user.id);
            }
          } catch (adminCheckErr) {
            console.error('Primary admin check failed, trying fallback:', adminCheckErr);
            // Try fallback method
            adminStatus = await authApi.checkAdminStatus(data.user.id);
          }

          console.log('✅ Admin status result:', adminStatus);

          if (!adminStatus) {
            // Sign out if not admin
            await signOut();
            toast({
              title: 'Erişim Reddedildi',
              description: 'Bu alana erişim için admin yetkileriniz bulunmuyor.',
              status: 'error',
              duration: 5000,
              isClosable: true,
            });
            return;
          }

          toast({
            title: 'Giriş Başarılı',
            description: 'Admin paneline hoş geldiniz!',
            status: 'success',
            duration: 3000,
            isClosable: true,
          });

          console.log('🚀 Redirecting to dashboard with session persistence...');

          // Debug session state before navigation
          debugSessionStorage();

          // Use enhanced navigation with session persistence
          await navigateWithSession('/dashboard');
        } catch (adminCheckError) {
          console.error('Admin check error:', adminCheckError);
          toast({
            title: 'Yetki Kontrolü Hatası',
            description: 'Admin yetkisi kontrol edilirken bir hata oluştu. Lütfen tekrar deneyin.',
            status: 'error',
            duration: 5000,
            isClosable: true,
          });
          // Sign out on admin check error
          await signOut();
          return;
        }
      }
    } catch (error) {
      console.error('Login error:', error);

      // Provide more specific error messages
      let errorMessage = 'Giriş sırasında bir hata oluştu. Lütfen tekrar deneyin.';
      let errorTitle = 'Beklenmeyen Hata';

      if (error instanceof Error) {
        if (error.message.includes('Admin check function is not available')) {
          errorTitle = 'Sistem Hatası';
          errorMessage = 'Admin kontrol sistemi kullanılamıyor. Lütfen sistem yöneticisine başvurun.';
        } else if (error.message.includes('Admin access required')) {
          errorTitle = 'Erişim Reddedildi';
          errorMessage = 'Bu alana erişim için admin yetkileriniz bulunmuyor.';
        } else if (error.message.includes('Failed to fetch')) {
          errorTitle = 'Bağlantı Hatası';
          errorMessage = 'Sunucuya bağlanılamıyor. İnternet bağlantınızı kontrol edin.';
        }
      }

      toast({
        title: errorTitle,
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };



  // Loading state
  if (isAuthLoading) {
    return (
      <Container maxW="md" centerContent py={20}>
        <VStack spacing={4}>
          <Icon as={Shield} boxSize={12} color="blue.500" />
          <Text fontSize="lg">Yükleniyor...</Text>
        </VStack>
      </Container>
    );
  }

  // If user is logged in but not admin
  if (user && !isAdmin) {
    return (
      <Container maxW="md" centerContent py={20}>
        <VStack spacing={6}>
          <Icon as={Shield} boxSize={16} color="red.500" />
          <Heading size="lg" color="red.600">Erişim Reddedildi</Heading>

          <Alert status="error" borderRadius="md">
            <AlertIcon />
            Bu alana erişim için admin yetkileriniz bulunmuyor.
          </Alert>

          <VStack spacing={4} w="full">
            <Text textAlign="center" color="gray.600">
              Mevcut kullanıcı: <strong>{user.email}</strong>
            </Text>

            <Button
              leftIcon={<LogOut size={20} />}
              onClick={handleLogout}
              colorScheme="red"
              variant="outline"
              size="lg"
              w="full"
            >
              Çıkış Yap
            </Button>
          </VStack>
        </VStack>
      </Container>
    );
  }

  // Login form
  return (
    <Container maxW="md" centerContent py={20}>
        <Box
        w="full"
        p={8}
        borderRadius="xl"
        boxShadow="2xl"
          bg="white"
        border="1px solid"
        borderColor="gray.100"
        >
        <VStack spacing={8}>
            <VStack spacing={4}>
            <Icon as={Shield} boxSize={16} color="blue.500" />
            <Heading size="xl" color="gray.800">
              Admin Paneli
                </Heading>
            <Text color="gray.600" textAlign="center">
              Yönetici hesabınızla giriş yapın
              </Text>
            </VStack>

          <Box as="form" onSubmit={handleLogin} w="full">
            <VStack spacing={6}>
                  <FormControl isInvalid={!!errors.email}>
                    <FormLabel color="gray.700">
                  <HStack spacing={2}>
                    <Icon as={User} boxSize={4} />
                    <Text>E-posta</Text>
                      </HStack>
                    </FormLabel>
                    <Input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                      size="lg"
                  borderRadius="lg"
                  focusBorderColor="blue.500"
                    />
                    <FormErrorMessage>{errors.email}</FormErrorMessage>
                  </FormControl>

                  <FormControl isInvalid={!!errors.password}>
                    <FormLabel color="gray.700">
                  <HStack spacing={2}>
                    <Icon as={Lock} boxSize={4} />
                        <Text>Şifre</Text>
                      </HStack>
                    </FormLabel>
                    <Input
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="••••••••"
                      size="lg"
                  borderRadius="lg"
                  focusBorderColor="blue.500"
                    />
                    <FormErrorMessage>{errors.password}</FormErrorMessage>
                  </FormControl>

                  <Button
                    type="submit"
                    isLoading={isLoading}
                    loadingText="Giriş yapılıyor..."
                colorScheme="blue"
                size="lg"
                w="full"
                borderRadius="lg"
                leftIcon={<Shield size={20} />}
                  >
                Admin Girişi
                  </Button>
            </VStack>
            </Box>

          <Text fontSize="sm" color="gray.500" textAlign="center">
            PromptFlow Admin Panel v2.0
            </Text>
          </VStack>
        </Box>
      </Container>
  );
}
