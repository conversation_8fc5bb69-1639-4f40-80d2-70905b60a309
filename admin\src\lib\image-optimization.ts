// Image optimization utilities for admin panel

// Optimized image component props
export interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  quality?: number;
  priority?: boolean;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  sizes?: string;
  className?: string;
}

// Generate optimized image URLs for different screen sizes
export function generateImageSizes(baseUrl: string, sizes: number[]): string {
  return sizes
    .map((size) => `${baseUrl}?w=${size}&q=75 ${size}w`)
    .join(', ');
}

// Generate blur placeholder for images
export function generateBlurDataURL(width: number = 10, height: number = 10): string {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) return '';
  
  // Create a simple gradient blur placeholder
  const gradient = ctx.createLinearGradient(0, 0, width, height);
  gradient.addColorStop(0, '#f3f4f6');
  gradient.addColorStop(1, '#e5e7eb');
  
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, width, height);
  
  return canvas.toDataURL();
}

// Image format detection and optimization
export function getOptimalImageFormat(originalFormat: string): string {
  // Prefer WebP for modern browsers, fallback to original format
  if (typeof window !== 'undefined') {
    const canvas = document.createElement('canvas');
    const supportsWebP = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    
    if (supportsWebP && ['jpeg', 'jpg', 'png'].includes(originalFormat.toLowerCase())) {
      return 'webp';
    }
  }
  
  return originalFormat;
}

// Lazy loading intersection observer for images
export function createImageObserver(callback: (entry: IntersectionObserverEntry) => void) {
  if (typeof window === 'undefined') return null;
  
  return new IntersectionObserver(
    (entries) => {
      entries.forEach(callback);
    },
    {
      rootMargin: '50px 0px', // Start loading 50px before image enters viewport
      threshold: 0.01,
    }
  );
}

// Image compression utility (client-side)
export async function compressImage(
  file: File,
  maxWidth: number = 1920,
  maxHeight: number = 1080,
  quality: number = 0.8
): Promise<Blob> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }
      
      canvas.width = width;
      canvas.height = height;
      
      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      canvas.toBlob(
        (blob) => resolve(blob!),
        'image/jpeg',
        quality
      );
    };
    
    img.src = URL.createObjectURL(file);
  });
}

// Avatar image optimization
export function getOptimizedAvatarUrl(
  email: string,
  size: number = 40,
  defaultImage: string = 'identicon'
): string {
  // Use Gravatar with optimized parameters
  const hash = btoa(email.toLowerCase().trim()).replace(/[^a-zA-Z0-9]/g, '');
  return `https://www.gravatar.com/avatar/${hash}?s=${size}&d=${defaultImage}&r=g`;
}

// Responsive image sizes for different breakpoints
export const responsiveImageSizes = {
  avatar: {
    sm: 32,
    md: 40,
    lg: 48,
    xl: 56,
  },
  thumbnail: {
    sm: 80,
    md: 120,
    lg: 160,
    xl: 200,
  },
  card: {
    sm: 200,
    md: 300,
    lg: 400,
    xl: 500,
  },
  hero: {
    sm: 400,
    md: 800,
    lg: 1200,
    xl: 1600,
  },
} as const;

// Generate sizes attribute for responsive images
export function generateSizesAttribute(breakpoints: Record<string, number>): string {
  const entries = Object.entries(breakpoints);
  const sizes = entries.map(([breakpoint, size], index) => {
    if (index === entries.length - 1) {
      return `${size}px`; // Default size for largest breakpoint
    }
    
    const mediaQuery = getMediaQuery(breakpoint);
    return `${mediaQuery} ${size}px`;
  });
  
  return sizes.join(', ');
}

// Helper to get media query for breakpoint
function getMediaQuery(breakpoint: string): string {
  const breakpoints = {
    sm: '(max-width: 640px)',
    md: '(max-width: 768px)',
    lg: '(max-width: 1024px)',
    xl: '(max-width: 1280px)',
  };
  
  return breakpoints[breakpoint as keyof typeof breakpoints] || '';
}

// Image preloading utility
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
}

// Batch image preloading
export async function preloadImages(urls: string[]): Promise<void> {
  const promises = urls.map(preloadImage);
  await Promise.all(promises);
}
