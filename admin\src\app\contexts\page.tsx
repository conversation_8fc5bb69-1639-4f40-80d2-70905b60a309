"use client";

import { useState } from "react";
import {
  Box,
  Flex,
  <PERSON>ing,
  Text,
  Button,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  IconButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Card,
  CardBody,
  Stack,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  SimpleGrid,
  Divider,
  useColorModeValue,
} from "@chakra-ui/react";
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  Globe,
  Lock,
  Star,
  Tag,
  User,
  Filter,
  MoreHorizontal,
  CheckCircle,
  Clock,
  X,
  AlertCircle
} from "lucide-react";
import { AdminLayout } from '@/components/admin-layout';
import {
  useAdminContexts,
  useAdminContextStats,
  useApproveContext,
  useToggleContextFeatured,
  useToggleContextPublic,
  useDeleteContext,
  useContextCategories
} from '@/hooks/use-admin-contexts';
import { useToast } from '@chakra-ui/react';

// Mock data - gerçek data Supabase'den gelecek
const mockContexts = [
  {
    id: "1",
    title: "React Component Geliştirme",
    description: "Modern React component'leri geliştirmek için gerekli context",
    content: "Sen bir uzman React geliştiricisisin...",
    category: { id: "1", name: "Frontend", icon: "⚛️", color: "#61DAFB" },
    author_id: "user-1",
    author_name: "Ahmet Yılmaz",
    author_email: "<EMAIL>",
    is_public: true,
    is_featured: true,
    is_template: true,
    tags: ["react", "typescript", "frontend"],
    usage_count: 245,
    like_count: 32,
    view_count: 580,
    status: "active",
    approval_status: "approved",
    approved_by: "admin-1",
    approved_at: "2024-01-15T12:00:00Z",
    approval_notes: null,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-16T14:30:00Z"
  },
  {
    id: "2",
    title: "Node.js API Geliştirme",
    description: "REST API'ları ve mikroservisler için backend context",
    content: "Sen bir deneyimli backend geliştiricisisin...",
    category: { id: "2", name: "Backend", icon: "🚀", color: "#68D391" },
    author_id: "user-2",
    author_name: "Ayşe Demir",
    author_email: "<EMAIL>",
    is_public: true,
    is_featured: false,
    is_template: true,
    tags: ["nodejs", "api", "backend"],
    usage_count: 189,
    like_count: 28,
    view_count: 432,
    status: "active",
    approval_status: "pending",
    approved_by: null,
    approved_at: null,
    approval_notes: null,
    created_at: "2024-01-14T09:00:00Z",
    updated_at: "2024-01-16T11:15:00Z"
  },
  {
    id: "3",
    title: "PostgreSQL Optimizasyonu",
    description: "Veritabanı performans optimizasyonu ve query tuning",
    content: "Sen bir PostgreSQL uzmanısın...",
    category: { id: "3", name: "Database", icon: "🗄️", color: "#4299E1" },
    author_id: "user-1",
    author_name: "Ahmet Yılmaz",
    author_email: "<EMAIL>",
    is_public: false,
    is_featured: false,
    is_template: false,
    tags: ["postgresql", "database", "performance"],
    usage_count: 67,
    like_count: 15,
    view_count: 156,
    status: "active",
    approval_status: "approved",
    approved_by: null,
    approved_at: null,
    approval_notes: null,
    created_at: "2024-01-12T16:00:00Z",
    updated_at: "2024-01-15T20:45:00Z"
  }
];

const mockCategories = [
  { id: "1", name: "Frontend", icon: "⚛️", color: "#61DAFB", count: 15 },
  { id: "2", name: "Backend", icon: "🚀", color: "#68D391", count: 12 },
  { id: "3", name: "Database", icon: "🗄️", color: "#4299E1", count: 8 },
  { id: "4", name: "DevOps", icon: "🔧", color: "#F6AD55", count: 6 },
  { id: "5", name: "AI/ML", icon: "🤖", color: "#9F7AEA", count: 4 }
];

const mockStats = {
  totalContexts: 45,
  publicContexts: 32,
  privateContexts: 13,
  featuredContexts: 8,
  templateContexts: 28,
  pendingApproval: 12,
  approvedContexts: 30,
  rejectedContexts: 3,
  totalUsage: 1240,
  totalLikes: 156,
  totalViews: 3450
};

export default function ContextsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedType, setSelectedType] = useState("all");
  const [selectedApprovalStatus, setSelectedApprovalStatus] = useState("all");

  const toast = useToast();
  const bgColor = useColorModeValue("gray.50", "gray.900");
  const cardBg = useColorModeValue("white", "gray.800");

  // Hooks for data fetching
  const { data: contexts = [], isLoading: contextsLoading } = useAdminContexts({
    search: searchTerm,
    category: selectedCategory,
    type: selectedType,
    approval_status: selectedApprovalStatus
  });

  const { data: stats } = useAdminContextStats();
  const { data: categories = [] } = useContextCategories();

  // Mutation hooks
  const approveContextMutation = useApproveContext();
  const toggleFeaturedMutation = useToggleContextFeatured();
  const togglePublicMutation = useToggleContextPublic();
  const deleteContextMutation = useDeleteContext();

  // Filtered contexts are now handled by the hook

  const handleTogglePublic = async (contextId: string) => {
    const context = contexts.find(c => c.id === contextId);
    if (!context) return;

    try {
      await togglePublicMutation.mutateAsync({
        contextId,
        isPublic: !context.is_public
      });

      toast({
        title: "Başarılı",
        description: `Context ${!context.is_public ? 'herkese açık yapıldı' : 'gizlendi'}`,
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: "İşlem başarısız oldu",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleToggleFeatured = async (contextId: string) => {
    const context = contexts.find(c => c.id === contextId);
    if (!context) return;

    try {
      await toggleFeaturedMutation.mutateAsync({
        contextId,
        featured: !context.is_featured
      });

      toast({
        title: "Başarılı",
        description: `Context ${!context.is_featured ? 'öne çıkarıldı' : 'öne çıkarmaktan kaldırıldı'}`,
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: "İşlem başarısız oldu",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleDeleteContext = async (contextId: string) => {
    if (!confirm("Bu context'i silmek istediğinizden emin misiniz?")) return;

    try {
      await deleteContextMutation.mutateAsync(contextId);

      toast({
        title: "Başarılı",
        description: "Context silindi",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: "Silme işlemi başarısız oldu",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleEditContext = (contextId: string) => {
    // TODO: Implement edit functionality - could open a modal or navigate to edit page
    console.log("Edit context:", contextId);
  };

  const handleApproveContext = async (contextId: string) => {
    try {
      await approveContextMutation.mutateAsync({
        contextId,
        status: 'approved'
      });

      toast({
        title: "Başarılı",
        description: "Context onaylandı",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: "Onaylama işlemi başarısız oldu",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleRejectContext = async (contextId: string, notes?: string) => {
    try {
      await approveContextMutation.mutateAsync({
        contextId,
        status: 'rejected',
        notes
      });

      toast({
        title: "Başarılı",
        description: "Context reddedildi",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: "Reddetme işlemi başarısız oldu",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handlePendingContext = async (contextId: string) => {
    try {
      await approveContextMutation.mutateAsync({
        contextId,
        status: 'pending'
      });

      toast({
        title: "Başarılı",
        description: "Context onay bekliyor durumuna alındı",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: "İşlem başarısız oldu",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  return (
    <AdminLayout>
      <Box bg={bgColor} minH="100vh" p={6}>
        <Box maxW="7xl" mx="auto">
        {/* Header */}
        <Flex justify="space-between" align="center" mb={8}>
          <Box>
            <Heading size="lg" mb={2}>Context Yönetimi</Heading>
            <Text color="gray.600">
              Kullanıcıların oluşturduğu context'leri yönetin
            </Text>
          </Box>
                     <Button leftIcon={<Plus />} colorScheme="blue" size="sm" variant="solid">
             Yeni Context
           </Button>
        </Flex>

        {/* Stats */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 5 }} spacing={6} mb={8}>
          <Card bg={cardBg}>
            <CardBody>
              <Stat>
                <StatLabel>Toplam Context</StatLabel>
                <StatNumber>{stats?.totalContexts || 0}</StatNumber>
                                 <StatHelpText>
                   <Badge colorScheme="green" variant="subtle">Aktif</Badge> sistem
                 </StatHelpText>
              </Stat>
            </CardBody>
          </Card>

          <Card bg={cardBg}>
            <CardBody>
              <Stat>
                <StatLabel>Herkese Açık</StatLabel>
                <StatNumber>{stats?.publicContexts || 0}</StatNumber>
                                 <StatHelpText>
                   <Badge colorScheme="blue" variant="subtle">
                     {stats?.totalContexts ? Math.round((stats.publicContexts / stats.totalContexts) * 100) : 0}%
                   </Badge> oranı
                 </StatHelpText>
              </Stat>
            </CardBody>
          </Card>

          <Card bg={cardBg}>
            <CardBody>
              <Stat>
                <StatLabel>Toplam Kullanım</StatLabel>
                <StatNumber>{stats?.totalUsage || 0}</StatNumber>
                                 <StatHelpText>
                   <Badge colorScheme="purple" variant="subtle">Toplam</Badge> kullanım
                 </StatHelpText>
              </Stat>
            </CardBody>
          </Card>

          <Card bg={cardBg}>
            <CardBody>
              <Stat>
                <StatLabel>Onay Bekleyen</StatLabel>
                <StatNumber>{stats?.pendingApproval || 0}</StatNumber>
                                 <StatHelpText>
                   <Badge colorScheme="orange" variant="subtle">
                     {(stats?.pendingApproval || 0) > 0 ? 'Acil' : 'Temiz'}
                   </Badge> {(stats?.pendingApproval || 0) > 0 ? 'inceleme gerekli' : 'durum'}
                 </StatHelpText>
              </Stat>
            </CardBody>
          </Card>

          <Card bg={cardBg}>
            <CardBody>
              <Stat>
                <StatLabel>Beğeni Sayısı</StatLabel>
                <StatNumber>{stats?.totalLikes || 0}</StatNumber>
                                 <StatHelpText>
                   <Badge colorScheme="red" variant="subtle">Toplam</Badge> beğeni
                 </StatHelpText>
              </Stat>
            </CardBody>
          </Card>
        </SimpleGrid>

        {/* Filters */}
        <Card bg={cardBg} mb={6}>
          <CardBody>
            <Stack spacing={4}>
              <Flex gap={4} wrap="wrap">
                <InputGroup flex="1" minW="300px">
                  <InputLeftElement pointerEvents="none">
                    <Search size={18} color="gray.300" />
                  </InputLeftElement>
                  <Input
                    placeholder="Context ara... (başlık, açıklama, etiketler)"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </InputGroup>

                <Select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  maxW="200px"
                >
                  <option value="all">Tüm Kategoriler</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                </Select>

                <Select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  maxW="200px"
                >
                  <option value="all">Tüm Tipler</option>
                  <option value="public">🌐 Herkese Açık</option>
                  <option value="private">🔒 Özel</option>
                  <option value="featured">⭐ Öne Çıkan</option>
                  <option value="template">📄 Şablonlar</option>
                </Select>

                <Select
                  value={selectedApprovalStatus}
                  onChange={(e) => setSelectedApprovalStatus(e.target.value)}
                  maxW="200px"
                >
                  <option value="all">Tüm Onay Durumları</option>
                  <option value="pending">⏳ Onay Bekleyen</option>
                  <option value="approved">✅ Onaylanmış</option>
                  <option value="rejected">❌ Reddedilmiş</option>
                </Select>
              </Flex>
            </Stack>
          </CardBody>
        </Card>

        {/* Contexts Table */}
        <Card bg={cardBg}>
          <CardBody>
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th>Context</Th>
                  <Th>Kategori</Th>
                  <Th>Yazar</Th>
                  <Th>Durum</Th>
                  <Th>Onay Durumu</Th>
                  <Th>İstatistikler</Th>
                  <Th>İşlemler</Th>
                </Tr>
              </Thead>
              <Tbody>
                {contextsLoading ? (
                  <Tr>
                    <Td colSpan={7} textAlign="center" py={10}>
                      <Text>Contextler yükleniyor...</Text>
                    </Td>
                  </Tr>
                ) : contexts.map((context) => (
                  <Tr key={context.id}>
                    <Td maxW="300px">
                      <Stack spacing={1}>
                        <Flex align="center" gap={2}>
                          <Text fontWeight="medium" noOfLines={1}>
                            {context.title}
                          </Text>
                          {context.is_featured && (
                            <Star size={14} color="#F6E05E" fill="#F6E05E" />
                          )}
                        </Flex>
                        <Text fontSize="sm" color="gray.500" noOfLines={2}>
                          {context.description}
                        </Text>
                        <Flex gap={1} flexWrap="wrap">
                          {context.tags.slice(0, 3).map((tag) => (
                            <Badge key={tag} size="sm" variant="outline">
                              {tag}
                            </Badge>
                          ))}
                          {context.tags.length > 3 && (
                            <Badge size="sm" variant="outline">
                              +{context.tags.length - 3}
                            </Badge>
                          )}
                        </Flex>
                      </Stack>
                    </Td>
                    <Td>
                      <Badge
                        colorScheme="blue"
                        variant="subtle"
                      >
                        {context.category.icon} {context.category.name}
                      </Badge>
                    </Td>
                    <Td>
                      <Stack spacing={0}>
                        <Text fontSize="sm" fontWeight="medium">
                          {context.author_name}
                        </Text>
                        <Text fontSize="xs" color="gray.500">
                          {context.author_email}
                        </Text>
                      </Stack>
                    </Td>
                    <Td>
                      <Stack spacing={1}>
                        <Badge
                          colorScheme={context.is_public ? "green" : "gray"}
                          variant="subtle"
                        >
                          {context.is_public ? (
                            <Globe size={12} />
                          ) : (
                            <Lock size={12} />
                          )}
                          {context.is_public ? "Herkese Açık" : "Özel"}
                        </Badge>
                        {context.is_template && (
                          <Badge colorScheme="purple" variant="subtle">
                            <Tag size={12} />
                            Şablon
                          </Badge>
                        )}
                      </Stack>
                    </Td>
                    <Td>
                      <Badge
                        colorScheme={
                          context.approval_status === "approved" ? "green" :
                          context.approval_status === "pending" ? "orange" :
                          "red"
                        }
                        variant="subtle"
                      >
                        {context.approval_status === "approved" && <CheckCircle size={12} />}
                        {context.approval_status === "pending" && <Clock size={12} />}
                        {context.approval_status === "rejected" && <X size={12} />}
                        {context.approval_status === "approved" && "Onaylandı"}
                        {context.approval_status === "pending" && "Bekliyor"}
                        {context.approval_status === "rejected" && "Reddedildi"}
                      </Badge>
                    </Td>
                    <Td>
                      <Stack spacing={1} fontSize="sm">
                        <Flex align="center" gap={1}>
                          <Eye size={12} />
                          <Text>{context.view_count}</Text>
                        </Flex>
                        <Flex align="center" gap={1}>
                          <User size={12} />
                          <Text>{context.usage_count}</Text>
                        </Flex>
                        <Flex align="center" gap={1}>
                          <Star size={12} />
                          <Text>{context.like_count}</Text>
                        </Flex>
                      </Stack>
                    </Td>
                    <Td>
                      <Menu>
                        <MenuButton
                          as={IconButton}
                          icon={<MoreHorizontal size={16} />}
                          variant="ghost"
                          size="sm"
                          aria-label="Context işlemleri"
                        />
                        <MenuList>
                          <MenuItem
                            icon={<Edit size={16} />}
                            onClick={() => handleEditContext(context.id)}
                          >
                            Düzenle
                          </MenuItem>

                          {/* Approval Actions */}
                          {context.approval_status === "pending" && (
                            <>
                              <MenuItem
                                icon={<CheckCircle size={16} />}
                                color="green.500"
                                onClick={() => handleApproveContext(context.id)}
                              >
                                Onayla
                              </MenuItem>
                              <MenuItem
                                icon={<X size={16} />}
                                color="red.500"
                                onClick={() => handleRejectContext(context.id)}
                              >
                                Reddet
                              </MenuItem>
                              <Divider />
                            </>
                          )}

                          {context.approval_status === "approved" && (
                            <MenuItem
                              icon={<Clock size={16} />}
                              color="orange.500"
                              onClick={() => handlePendingContext(context.id)}
                            >
                              Onayı Geri Al
                            </MenuItem>
                          )}

                          {context.approval_status === "rejected" && (
                            <MenuItem
                              icon={<CheckCircle size={16} />}
                              color="green.500"
                              onClick={() => handleApproveContext(context.id)}
                            >
                              Onayla
                            </MenuItem>
                          )}

                          <MenuItem
                            icon={context.is_public ? <Lock size={16} /> : <Globe size={16} />}
                            onClick={() => handleTogglePublic(context.id)}
                          >
                            {context.is_public ? "Gizle" : "Yayınla"}
                          </MenuItem>
                          <MenuItem
                            icon={<Star size={16} />}
                            onClick={() => handleToggleFeatured(context.id)}
                          >
                            {context.is_featured ? "Öne Çıkarmayı Kaldır" : "Öne Çıkar"}
                          </MenuItem>
                          <Divider />
                          <MenuItem
                            icon={<Trash2 size={16} />}
                            color="red.500"
                            onClick={() => handleDeleteContext(context.id)}
                          >
                            Sil
                          </MenuItem>
                        </MenuList>
                      </Menu>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>

            {!contextsLoading && contexts.length === 0 && (
              <Box textAlign="center" py={10}>
                <Text fontSize="lg" color="gray.500" mb={2}>
                  Context bulunamadı
                </Text>
                <Text color="gray.400">
                  Arama kriterlerinizi değiştirmeyi deneyin
                </Text>
              </Box>
            )}
          </CardBody>
        </Card>
      </Box>
    </Box>
    </AdminLayout>
  );
}