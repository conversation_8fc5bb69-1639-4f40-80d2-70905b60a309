---
type: "backend_guide"
role: "database_guide"
dependencies:
  - "core/PROJECT_CORE.md"
related_features:
  - "features/CONTEXT_GALLERY.md"
  - "features/HASHTAG_SYSTEM.md"
  - "features/USER_PLANS.md"
auto_update_targets:
  - "features/CONTEXT_GALLERY.md"
  - "fixes/CONTEXT_GALLERY_ISSUES.md"
development_history:
  - date: "2025-01-29"
    change: "RLS policies documented for Context Gallery"
    database_updates: ["Context templates table", "Admin permissions", "User access policies"]
    integration_points: ["Context Gallery", "Admin panel", "User plans"]
last_updated: "2025-01-29"
---

# PromptFlow Supabase Backend Rules

## Infrastructure
- **Project ID**: iqehopwgrczylqliajww
- **URL**: https://iqehopwgrczylqliajww.supabase.co
- **Region**: eu-central-1
- **Database**: PostgreSQL with extensions

## Database Schema
### Core Tables
```sql
-- Users (managed by Supabase Auth)
auth.users

-- Projects (prompt collections)
projects (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id uuid REFERENCES auth.users(id),
  name text NOT NULL,
  context_text text DEFAULT '',
  description text,
  status text DEFAULT 'active',
  tags text[],
  is_public boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
)

-- Prompts (individual prompts)
prompts (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id uuid REFERENCES projects(id) ON DELETE CASCADE,
  user_id uuid REFERENCES auth.users(id),
  prompt_text text NOT NULL,
  title text,
  description text,
  category text,
  tags text[],
  order_index integer DEFAULT 0,
  is_used boolean DEFAULT false,
  is_favorite boolean DEFAULT false,
  usage_count integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
)
```

## RLS Policies
### Security Rules
```sql
-- Users can only access their own data
CREATE POLICY "Users own data" ON projects
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users own prompts" ON prompts
  FOR ALL USING (auth.uid() = user_id);

-- Plan access restrictions
CREATE POLICY "Users view own plans" ON user_plans
  FOR SELECT USING (auth.uid() = user_id);

-- Admin access for management
CREATE POLICY "Admins full access" ON projects
  FOR ALL USING (
    EXISTS (SELECT 1 FROM admin_users 
             WHERE user_id = auth.uid())
  );
```

## API Patterns
### Client Configuration
```typescript
// Main app client
export const supabaseBrowser = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

// Admin client with service role
export const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)
```

### Query Patterns
```typescript
// Optimized queries with proper indexing
const { data } = await supabase
  .from('prompts')
  .select('*')
  .eq('project_id', projectId)
  .order('order_index')
  .range(0, 49) // Pagination
```

## User Plans System
```sql
-- Plan definitions
plan_types (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  name text UNIQUE NOT NULL,
  display_name text NOT NULL,
  description text,
  price_monthly numeric(10,2),
  price_yearly numeric(10,2),
  max_projects integer NOT NULL,
  max_prompts_per_project integer NOT NULL,
  features jsonb DEFAULT '{}',
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
)

-- User plan assignments
user_plans (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id uuid REFERENCES auth.users(id),
  plan_type_id uuid REFERENCES plan_types(id),
  status text DEFAULT 'active',
  billing_cycle text DEFAULT 'monthly',
  started_at timestamptz DEFAULT now(),
  expires_at timestamptz,
  created_at timestamptz DEFAULT now()
)

-- Usage tracking
usage_stats (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id uuid REFERENCES auth.users(id),
  stat_date date DEFAULT CURRENT_DATE,
  projects_count integer DEFAULT 0,
  prompts_count integer DEFAULT 0,
  last_activity_at timestamptz DEFAULT now()
)
```

## Database Functions
### Plan Management
```sql
-- Check user limits
CREATE OR REPLACE FUNCTION check_user_limits(user_uuid uuid)
RETURNS TABLE (
  current_projects integer,
  max_projects integer,
  can_create_project boolean
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COALESCE(us.projects_count, 0)::integer,
    pt.max_projects,
    (COALESCE(us.projects_count, 0) < pt.max_projects) as can_create_project
  FROM user_plans up
  JOIN plan_types pt ON up.plan_type_id = pt.id
  LEFT JOIN usage_stats us ON us.user_id = up.user_id
  WHERE up.user_id = user_uuid AND up.status = 'active';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Assign free plan to new users
CREATE OR REPLACE FUNCTION assign_free_plan_to_new_user()
RETURNS TRIGGER AS $$
DECLARE
  free_plan_id uuid;
BEGIN
  SELECT id INTO free_plan_id
  FROM public.plan_types
  WHERE name = 'free' AND is_active = true;

  IF free_plan_id IS NOT NULL THEN
    INSERT INTO public.user_plans (user_id, plan_type_id, status, billing_cycle)
    VALUES (NEW.id, free_plan_id, 'active', 'lifetime');

    INSERT INTO public.usage_stats (user_id, stat_date, projects_count, prompts_count)
    VALUES (NEW.id, CURRENT_DATE, 0, 0);
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Performance Indexes
```sql
-- Smart sorting for prompts
CREATE INDEX idx_prompts_project_smart_sort
ON prompts(project_id, is_used, order_index);

-- User project access
CREATE INDEX idx_prompts_user_project
ON prompts(user_id, project_id);

-- Hashtag search optimization
CREATE INDEX idx_prompts_tags_gin
ON prompts USING gin(tags);

-- Plan queries
CREATE INDEX idx_user_plans_active
ON user_plans(user_id, status) WHERE status = 'active';
```

## Update Requirements
- Update this file when modifying database schema
- Document new RLS policies
- Maintain function definitions
- Keep performance optimizations current
- Update indexes when adding new query patterns
- Document new triggers and functions
