// Performance monitoring utilities for admin panel

interface PerformanceMetrics {
  pageLoadTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  timeToInteractive: number;
  cumulativeLayoutShift: number;
  bundleSize: number;
  routeChangeTime: number;
}

class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {};
  private routeChangeStart: number = 0;

  // Initialize performance monitoring
  init() {
    if (typeof window === 'undefined') return;

    // Monitor Core Web Vitals
    this.monitorWebVitals();
    
    // Monitor route changes
    this.monitorRouteChanges();
    
    // Monitor bundle size
    this.monitorBundleSize();
  }

  // Monitor Core Web Vitals
  private monitorWebVitals() {
    // First Contentful Paint
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name === 'first-contentful-paint') {
          this.metrics.firstContentfulPaint = entry.startTime;
        }
      }
    });
    observer.observe({ entryTypes: ['paint'] });

    // Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      this.metrics.largestContentfulPaint = lastEntry.startTime;
    });
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

    // Cumulative Layout Shift
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
      this.metrics.cumulativeLayoutShift = clsValue;
    });
    clsObserver.observe({ entryTypes: ['layout-shift'] });

    // Time to Interactive (approximation)
    window.addEventListener('load', () => {
      setTimeout(() => {
        this.metrics.timeToInteractive = performance.now();
      }, 0);
    });
  }

  // Monitor route changes for SPA navigation
  private monitorRouteChanges() {
    // Start timing on route change
    this.startRouteChange = this.startRouteChange.bind(this);
    this.endRouteChange = this.endRouteChange.bind(this);
  }

  startRouteChange() {
    this.routeChangeStart = performance.now();
  }

  endRouteChange() {
    if (this.routeChangeStart > 0) {
      this.metrics.routeChangeTime = performance.now() - this.routeChangeStart;
      this.routeChangeStart = 0;
    }
  }

  // Monitor bundle size (approximation)
  private monitorBundleSize() {
    const resourceObserver = new PerformanceObserver((list) => {
      let totalSize = 0;
      for (const entry of list.getEntries()) {
        const resource = entry as PerformanceResourceTiming;
        if (resource.name.includes('/_next/static/')) {
          totalSize += resource.transferSize || 0;
        }
      }
      this.metrics.bundleSize = totalSize;
    });
    resourceObserver.observe({ entryTypes: ['resource'] });
  }

  // Get current metrics
  getMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics };
  }

  // Log metrics to console (development only)
  logMetrics() {
    if (process.env.NODE_ENV === 'development') {
      console.group('🚀 Performance Metrics');
      console.log('First Contentful Paint:', this.metrics.firstContentfulPaint?.toFixed(2), 'ms');
      console.log('Largest Contentful Paint:', this.metrics.largestContentfulPaint?.toFixed(2), 'ms');
      console.log('Time to Interactive:', this.metrics.timeToInteractive?.toFixed(2), 'ms');
      console.log('Cumulative Layout Shift:', this.metrics.cumulativeLayoutShift?.toFixed(4));
      console.log('Bundle Size:', (this.metrics.bundleSize || 0 / 1024).toFixed(2), 'KB');
      console.log('Route Change Time:', this.metrics.routeChangeTime?.toFixed(2), 'ms');
      console.groupEnd();
    }
  }

  // Send metrics to analytics (placeholder)
  sendMetrics() {
    // In a real app, you would send these to your analytics service
    // Example: analytics.track('performance_metrics', this.getMetrics());
    
    if (process.env.NODE_ENV === 'development') {
      this.logMetrics();
    }
  }

  // Performance budget checks
  checkPerformanceBudget(): { passed: boolean; violations: string[] } {
    const violations: string[] = [];
    const metrics = this.getMetrics();

    // Performance budget thresholds
    const budgets = {
      firstContentfulPaint: 1500, // 1.5s
      largestContentfulPaint: 2500, // 2.5s
      timeToInteractive: 3000, // 3s
      cumulativeLayoutShift: 0.1, // 0.1
      routeChangeTime: 200, // 200ms
    };

    // Check each metric against budget
    Object.entries(budgets).forEach(([metric, threshold]) => {
      const value = metrics[metric as keyof PerformanceMetrics];
      if (value && value > threshold) {
        violations.push(`${metric}: ${value.toFixed(2)} > ${threshold}`);
      }
    });

    return {
      passed: violations.length === 0,
      violations,
    };
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Hook for React components
export function usePerformanceMonitor() {
  return {
    getMetrics: () => performanceMonitor.getMetrics(),
    logMetrics: () => performanceMonitor.logMetrics(),
    sendMetrics: () => performanceMonitor.sendMetrics(),
    checkBudget: () => performanceMonitor.checkPerformanceBudget(),
    startRouteChange: () => performanceMonitor.startRouteChange(),
    endRouteChange: () => performanceMonitor.endRouteChange(),
  };
}
