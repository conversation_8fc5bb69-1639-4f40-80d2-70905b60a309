---
type: "frontend_guide"
role: "main_app_guide"
dependencies:
  - "core/PROJECT_CORE.md"
  - "backend/SUPABASE.md"
related_features:
  - "features/CONTEXT_GALLERY.md"
  - "features/HASHTAG_SYSTEM.md"
  - "features/USER_PLANS.md"
auto_update_targets:
  - "features/CONTEXT_GALLERY.md"
development_history:
  - date: "2025-01-29"
    change: "Build errors resolved - TypeScript and ESLint fixes applied"
    components_updated: ["Context Gallery components", "Plan display components"]
    fixes_applied: ["TypeScript any types", "ESLint escape characters", "Unused imports"]
last_updated: "2025-01-29"
---

# PromptFlow Main Application Frontend Rules

## Technology Stack
- **Framework**: Next.js 15.4.1 (App Router)
- **React**: 19.1.0 (Latest features)
- **TypeScript**: Strict mode enabled
- **Styling**: Tailwind CSS v4
- **UI Components**: shadcn/ui (New York style)
- **Icons**: Lucide React
- **Drag & Drop**: @dnd-kit (React 19 compatible)

## Project Structure
```
promptflow/src/
├── app/                    # Next.js App Router
│   ├── layout.tsx         # Root layout + QueryProvider
│   ├── page.tsx           # Main page + AuthGuard
│   ├── profile/           # User profile pages
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── ui/               # shadcn/ui components
│   ├── auth-guard.tsx    # Authentication wrapper
│   ├── project-sidebar.tsx
│   ├── project-name-editor.tsx # Inline project name editing
│   ├── prompt-workspace.tsx
│   ├── context-sidebar.tsx
│   ├── plan-display.tsx  # User plans UI
│   └── hashtag-input.tsx # Categorization
├── hooks/                # Custom hooks
│   ├── use-auth.ts
│   ├── use-projects.ts
│   ├── use-prompts.ts
│   ├── use-plans.ts      # Plan management
│   └── use-hashtags.ts   # Categorization
├── lib/                  # Utilities
│   ├── supabase.ts       # Supabase client
│   ├── plan-limits.ts    # Plan restrictions
│   ├── project-validation.ts # Secure input validation
│   └── hashtag-utils.ts  # Category parsing
└── store/                # Zustand stores
    └── app-store.ts      # Global state
```

## Component Patterns
### shadcn/ui Usage
```typescript
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
```

### State Management
```typescript
// Server state - TanStack Query
const { data: prompts } = usePrompts(projectId)
const { data: projects } = useProjects()
const { data: userPlan } = useUserActivePlan()

// Client state - Zustand
const { activeProjectId, setActiveProjectId } = useAppStore()
```

### Optimistic Updates
```typescript
const mutation = useMutation({
  mutationFn: updatePrompt,
  onMutate: async (newData) => {
    await queryClient.cancelQueries(['prompts'])
    const previousData = queryClient.getQueryData(['prompts'])
    queryClient.setQueryData(['prompts'], newData)
    return { previousData }
  },
  onError: (err, newData, context) => {
    queryClient.setQueryData(['prompts'], context.previousData)
  }
})
```

## Performance Rules
- Use React.memo for expensive components
- Implement useCallback for event handlers
- Use dynamic imports for heavy components
- Optimize bundle with Next.js features
- Implement proper loading states

## Plan Integration
- Check limits before actions: `canCreateProject()`, `canCreatePrompt()`
- Show upgrade prompts when limits reached
- Update usage stats after operations
- Display plan status in sidebar

## Key Components
### Core UI Components
```typescript
// Authentication
<AuthGuard>              // Protects authenticated routes
<AuthForm />             // Login/signup form

// Layout
<ProjectSidebar />       // Project navigation with inline editing
<ProjectNameEditor />    // Secure inline project name editing
<PromptWorkspace />      // Main editing area
<ContextSidebar />       // Context gallery access

// Prompt Management
<PromptList />           // Sortable prompt list
<PromptItem />           // Individual prompt card
<AddPromptForm />        // New prompt creation

// Categorization
<HashtagInput />         // Hashtag input with autocomplete
<CategorySelector />     // Folder/category selection
<PopularHashtagsSidebar /> // Popular tags display

// Plans & Limits
<PlanDisplay />          // Current plan status
<LimitWarning />         // Limit notifications
<PlanUpgradeModal />     // Upgrade interface
```

### Drag & Drop Implementation
```typescript
import { DndContext, closestCenter } from '@dnd-kit/core'
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'

<DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
  <SortableContext items={prompts} strategy={verticalListSortingStrategy}>
    {prompts.map(prompt => (
      <SortablePromptItem key={prompt.id} prompt={prompt} />
    ))}
  </SortableContext>
</DndContext>
```

### Responsive Design Patterns
```typescript
// Mobile-first approach
const isMobile = useMediaQuery('(max-width: 768px)')

// Responsive layout
<div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
  <div className="lg:col-span-1">
    <ProjectSidebar />
  </div>
  <div className="lg:col-span-2">
    <PromptWorkspace />
  </div>
</div>
```

## Project Name Editing Feature
### Implementation Pattern
```typescript
// Secure inline editing with validation
<ProjectNameEditor
  projectId={project.id}
  currentName={project.name}
  onNameUpdated={(newName) => {
    // Optimistic updates handled automatically
    console.log('Project name updated:', newName)
  }}
  className="flex-1 min-w-0"
/>
```

### Key Features
- **Security-First**: Input validation, XSS protection, rate limiting
- **Real-time Validation**: Debounced duplicate checking, character limits
- **Accessibility**: ARIA labels, keyboard navigation, screen reader support
- **Mobile-Optimized**: Touch-friendly targets, responsive design
- **Performance**: React.memo, optimistic updates, smart caching

### Integration with ProjectSidebar
```typescript
// Replace static project name display with editable component
<CardHeader className="pb-2">
  <div className="flex items-center gap-2">
    <Folder className="h-4 w-4 text-blue-600 flex-shrink-0" />
    <ProjectNameEditor
      projectId={project.id}
      currentName={project.name}
      onNameUpdated={(newName) => {
        // Cache automatically updated via TanStack Query
      }}
      className="flex-1 min-w-0"
    />
  </div>
</CardHeader>
```

## Update Requirements
- Update this file when adding new components
- Document new hooks and utilities
- Maintain component organization
- Keep performance optimizations current
- Document responsive design patterns
- Update drag & drop implementations
- Document security patterns for user input
