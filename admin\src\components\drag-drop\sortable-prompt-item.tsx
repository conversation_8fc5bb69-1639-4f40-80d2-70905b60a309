'use client';

import { useState } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  Box,
  Card,
  CardBody,
  Text,
  HStack,
  VStack,
  IconButton,
  Badge,
  Textarea,
  Button,
  useColorModeValue,
  Tooltip,
} from '@chakra-ui/react';
import { GripVertical, Edit, Trash2, Check, X, Copy } from 'lucide-react';
import { useSelectedItems, useMultiSelectActions, useIsMultiSelectMode } from '@/store/admin-store';

interface Prompt {
  id: string;
  prompt_text: string;
  order_index: number;
  is_used: boolean;
  created_at: string;
  updated_at: string;
  project_id: string;
  user_id: string;
}

interface SortablePromptItemProps {
  prompt: Prompt;
  onEdit?: (prompt: Prompt) => void;
  onDelete?: (promptId: string) => void;
  onToggleUsed?: (promptId: string, isUsed: boolean) => void;
}

export function SortablePromptItem({
  prompt,
  onEdit,
  onDelete,
  onToggleUsed,
}: SortablePromptItemProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(prompt.prompt_text);

  // Drag & drop setup
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: prompt.id });

  // Multi-select state
  const selectedItems = useSelectedItems();
  const { addSelectedItem, removeSelectedItem } = useMultiSelectActions();
  const isMultiSelectMode = useIsMultiSelectMode();
  const isSelected = selectedItems.has(prompt.id);

  // Colors
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const selectedBg = useColorModeValue('blue.50', 'blue.900');

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const handleEdit = () => {
    if (isEditing) {
      // Save changes
      const updatedPrompt = { ...prompt, prompt_text: editText };
      onEdit?.(updatedPrompt);
      setIsEditing(false);
    } else {
      setIsEditing(true);
    }
  };

  const handleCancel = () => {
    setEditText(prompt.prompt_text);
    setIsEditing(false);
  };

  const handleSelect = () => {
    if (isSelected) {
      removeSelectedItem(prompt.id);
    } else {
      addSelectedItem(prompt.id);
    }
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(prompt.prompt_text);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  return (
    <Card
      ref={setNodeRef}
      style={style}
      bg={isSelected ? selectedBg : cardBg}
      borderColor={isSelected ? 'blue.300' : borderColor}
      borderWidth={isSelected ? '2px' : '1px'}
      shadow={isDragging ? 'lg' : 'sm'}
      cursor={isDragging ? 'grabbing' : 'default'}
      _hover={{ shadow: 'md' }}
    >
      <CardBody>
        <HStack spacing={4} align="flex-start">
          {/* Drag Handle */}
          <Box
            {...attributes}
            {...listeners}
            cursor="grab"
            _active={{ cursor: 'grabbing' }}
            color="gray.400"
            _hover={{ color: 'gray.600' }}
            p={1}
          >
            <GripVertical size={16} />
          </Box>

          {/* Multi-select checkbox */}
          {isMultiSelectMode && (
            <Box
              as="button"
              onClick={handleSelect}
              w={4}
              h={4}
              borderRadius="sm"
              border="2px solid"
              borderColor={isSelected ? 'blue.500' : 'gray.300'}
              bg={isSelected ? 'blue.500' : 'transparent'}
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              {isSelected && <Check size={12} color="white" />}
            </Box>
          )}

          {/* Content */}
          <VStack flex={1} align="stretch" spacing={2}>
            <HStack justify="space-between" align="flex-start">
              <HStack spacing={2}>
                <Text fontSize="sm" color="gray.500">
                  #{prompt.order_index}
                </Text>
                <Badge
                  colorScheme={prompt.is_used ? 'green' : 'gray'}
                  size="sm"
                >
                  {prompt.is_used ? 'Kullanıldı' : 'Bekliyor'}
                </Badge>
              </HStack>

              {/* Action buttons */}
              <HStack spacing={1}>
                <Tooltip label="Kopyala">
                  <IconButton
                    aria-label="Kopyala"
                    icon={<Copy size={14} />}
                    size="sm"
                    variant="ghost"
                    onClick={handleCopy}
                  />
                </Tooltip>

                <Tooltip label={isEditing ? 'Kaydet' : 'Düzenle'}>
                  <IconButton
                    aria-label={isEditing ? 'Kaydet' : 'Düzenle'}
                    icon={<Edit size={14} />}
                    size="sm"
                    variant="ghost"
                    colorScheme={isEditing ? 'green' : 'gray'}
                    onClick={handleEdit}
                  />
                </Tooltip>

                {isEditing && (
                  <Tooltip label="İptal">
                    <IconButton
                      aria-label="İptal"
                      icon={<X size={14} />}
                      size="sm"
                      variant="ghost"
                      onClick={handleCancel}
                    />
                  </Tooltip>
                )}

                <Tooltip label="Sil">
                  <IconButton
                    aria-label="Sil"
                    icon={<Trash2 size={14} />}
                    size="sm"
                    variant="ghost"
                    colorScheme="red"
                    onClick={() => onDelete?.(prompt.id)}
                  />
                </Tooltip>

                <Button
                  size="sm"
                  variant="outline"
                  colorScheme={prompt.is_used ? 'gray' : 'green'}
                  onClick={() => onToggleUsed?.(prompt.id, !prompt.is_used)}
                >
                  {prompt.is_used ? 'Sıfırla' : 'Kullanıldı'}
                </Button>
              </HStack>
            </HStack>

            {/* Prompt text */}
            {isEditing ? (
              <Textarea
                value={editText}
                onChange={(e) => setEditText(e.target.value)}
                placeholder="Prompt metni..."
                resize="vertical"
                minH="80px"
                fontSize="sm"
              />
            ) : (
              <Text
                fontSize="sm"
                whiteSpace="pre-wrap"
                noOfLines={3}
                cursor="text"
                onDoubleClick={() => setIsEditing(true)}
              >
                {prompt.prompt_text}
              </Text>
            )}
          </VStack>
        </HStack>
      </CardBody>
    </Card>
  );
}
