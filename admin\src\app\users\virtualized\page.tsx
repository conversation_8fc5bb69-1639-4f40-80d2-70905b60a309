'use client';

import React, { useState, useCallback } from 'react';
import {
  Box,
  Container,
  Heading,
  VStack,
  HStack,
  Button,
  Text,
  Badge,
  useToast,
  Card,
  CardHeader,
  CardBody,
  Switch,
  FormControl,
  FormLabel,
  Divider,
  Alert,
  AlertIcon,
  Flex,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  SimpleGrid,
} from '@chakra-ui/react';
import {
  Users,
  Settings,
  Download,
  Upload,
  Trash2,
  UserCheck,
  UserX,
  BarChart3,
} from 'lucide-react';
import { VirtualizedUserTable } from '@/components/VirtualizedUserTable';
import { useBulkUserOperations, PaginatedUser } from '@/hooks/usePaginatedUsers';

/**
 * Virtualized Users Management Page
 * 
 * Demonstrates high-performance user management with:
 * - Virtual scrolling for 10,000+ records
 * - Infinite scroll pagination
 * - Multi-select bulk operations
 * - Real-time search and filtering
 * - Optimistic updates
 */
export default function VirtualizedUsersPage() {
  const toast = useToast();
  const bulkOperations = useBulkUserOperations();
  
  // UI State
  const [enableMultiSelect, setEnableMultiSelect] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [tableHeight, setTableHeight] = useState(600);

  // Performance monitoring state
  const [performanceStats, setPerformanceStats] = useState({
    renderTime: 0,
    scrollFPS: 60,
    memoryUsage: 0,
  });

  /**
   * Handle user selection changes
   */
  const handleSelectionChange = useCallback((userIds: string[]) => {
    setSelectedUsers(userIds);
  }, []);

  /**
   * Handle bulk operations
   */
  const handleBulkOperation = useCallback(async (
    operation: 'activate' | 'deactivate' | 'delete'
  ) => {
    if (selectedUsers.length === 0) {
      toast({
        title: 'Seçim Gerekli',
        description: 'Lütfen işlem yapmak için kullanıcı seçin.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      await bulkOperations.mutateAsync({
        userIds: selectedUsers,
        operation,
      });
      
      // Clear selection after successful operation
      setSelectedUsers([]);
    } catch (error) {
      console.error('Bulk operation failed:', error);
    }
  }, [selectedUsers, bulkOperations, toast]);

  /**
   * Custom row actions for individual users
   */
  const customRowActions = useCallback((user: PaginatedUser) => (
    <HStack spacing={1}>
      <Button
        size="xs"
        variant="ghost"
        colorScheme="blue"
        leftIcon={<BarChart3 size={12} />}
      >
        Detay
      </Button>
    </HStack>
  ), []);

  /**
   * Performance monitoring (simulated)
   */
  React.useEffect(() => {
    const interval = setInterval(() => {
      setPerformanceStats(prev => ({
        renderTime: Math.random() * 2 + 0.5, // 0.5-2.5ms
        scrollFPS: Math.floor(Math.random() * 10) + 55, // 55-65 FPS
        memoryUsage: Math.random() * 50 + 20, // 20-70MB
      }));
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <Container maxW="full" py={6}>
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <Box>
          <HStack justify="space-between" align="center" mb={4}>
            <VStack align="start" spacing={1}>
              <Heading size="lg" display="flex" alignItems="center" gap={2}>
                <Users size={24} />
                Virtualized User Management
              </Heading>
              <Text color="gray.600">
                High-performance user table with virtual scrolling and infinite pagination
              </Text>
            </VStack>
            
            <HStack spacing={2}>
              <Button
                leftIcon={<Download size={16} />}
                variant="outline"
                size="sm"
              >
                Export
              </Button>
              <Button
                leftIcon={<Upload size={16} />}
                variant="outline"
                size="sm"
              >
                Import
              </Button>
              <Button
                leftIcon={<Settings size={16} />}
                colorScheme="blue"
                size="sm"
              >
                Settings
              </Button>
            </HStack>
          </HStack>

          {/* Performance Stats */}
          <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4} mb={4}>
            <Stat size="sm">
              <StatLabel>Render Time</StatLabel>
              <StatNumber>{performanceStats.renderTime.toFixed(1)}ms</StatNumber>
              <StatHelpText>Average render time</StatHelpText>
            </Stat>
            <Stat size="sm">
              <StatLabel>Scroll FPS</StatLabel>
              <StatNumber>{performanceStats.scrollFPS}</StatNumber>
              <StatHelpText>Frames per second</StatHelpText>
            </Stat>
            <Stat size="sm">
              <StatLabel>Memory Usage</StatLabel>
              <StatNumber>{performanceStats.memoryUsage.toFixed(0)}MB</StatNumber>
              <StatHelpText>Current memory</StatHelpText>
            </Stat>
            <Stat size="sm">
              <StatLabel>Selected</StatLabel>
              <StatNumber>{selectedUsers.length}</StatNumber>
              <StatHelpText>Users selected</StatHelpText>
            </Stat>
          </SimpleGrid>
        </Box>

        {/* Controls */}
        <Card>
          <CardHeader>
            <Heading size="md">Table Controls</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              {/* Multi-select toggle */}
              <FormControl display="flex" alignItems="center">
                <FormLabel htmlFor="multi-select" mb="0">
                  Enable Multi-Select
                </FormLabel>
                <Switch
                  id="multi-select"
                  isChecked={enableMultiSelect}
                  onChange={(e) => setEnableMultiSelect(e.target.checked)}
                />
              </FormControl>

              {/* Table height control */}
              <FormControl>
                <FormLabel>Table Height: {tableHeight}px</FormLabel>
                <input
                  type="range"
                  min="400"
                  max="800"
                  value={tableHeight}
                  onChange={(e) => setTableHeight(Number(e.target.value))}
                  style={{ width: '100%' }}
                />
              </FormControl>

              {/* Bulk operations */}
              {enableMultiSelect && (
                <>
                  <Divider />
                  <Box>
                    <Text fontWeight="medium" mb={2}>
                      Bulk Operations ({selectedUsers.length} selected)
                    </Text>
                    <HStack spacing={2}>
                      <Button
                        size="sm"
                        leftIcon={<UserCheck size={16} />}
                        colorScheme="green"
                        variant="outline"
                        onClick={() => handleBulkOperation('activate')}
                        isDisabled={selectedUsers.length === 0}
                        isLoading={bulkOperations.isPending}
                      >
                        Activate
                      </Button>
                      <Button
                        size="sm"
                        leftIcon={<UserX size={16} />}
                        colorScheme="orange"
                        variant="outline"
                        onClick={() => handleBulkOperation('deactivate')}
                        isDisabled={selectedUsers.length === 0}
                        isLoading={bulkOperations.isPending}
                      >
                        Deactivate
                      </Button>
                      <Button
                        size="sm"
                        leftIcon={<Trash2 size={16} />}
                        colorScheme="red"
                        variant="outline"
                        onClick={() => handleBulkOperation('delete')}
                        isDisabled={selectedUsers.length === 0}
                        isLoading={bulkOperations.isPending}
                      >
                        Delete
                      </Button>
                    </HStack>
                  </Box>
                </>
              )}
            </VStack>
          </CardBody>
        </Card>

        {/* Performance Alert */}
        <Alert status="info" borderRadius="md">
          <AlertIcon />
          <VStack align="start" spacing={1}>
            <Text fontWeight="medium">Performance Optimized</Text>
            <Text fontSize="sm">
              This table uses virtual scrolling to efficiently render thousands of users. 
              Only visible rows are rendered in the DOM, ensuring smooth performance even with large datasets.
            </Text>
          </VStack>
        </Alert>

        {/* Virtualized Table */}
        <Card>
          <CardBody p={0}>
            <VirtualizedUserTable
              height={tableHeight}
              enableMultiSelect={enableMultiSelect}
              onSelectionChange={handleSelectionChange}
              customActions={customRowActions}
            />
          </CardBody>
        </Card>

        {/* Technical Details */}
        <Card>
          <CardHeader>
            <Heading size="md">Technical Implementation</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={3} align="start">
              <Text fontSize="sm">
                <strong>Virtual Scrolling:</strong> Only renders visible rows using @tanstack/react-virtual
              </Text>
              <Text fontSize="sm">
                <strong>Infinite Pagination:</strong> Automatically loads more data as you scroll
              </Text>
              <Text fontSize="sm">
                <strong>Optimistic Updates:</strong> UI updates immediately for better user experience
              </Text>
              <Text fontSize="sm">
                <strong>Memory Efficient:</strong> Maintains constant memory usage regardless of dataset size
              </Text>
              <Text fontSize="sm">
                <strong>Accessibility:</strong> Full keyboard navigation and screen reader support
              </Text>
            </VStack>
          </CardBody>
        </Card>
      </VStack>
    </Container>
  );
}
