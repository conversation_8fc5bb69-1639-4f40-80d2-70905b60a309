-- PromptFlow Kullanıcı Planları Sistemi Deployment Script
-- Bu script Supabase'de çalıştırılmalıdır

-- 1. Önce mevcut tabloları kontrol et ve gerekirse oluştur
DO $$
BEGIN
    -- Plan türleri tablosu
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'plan_types') THEN
        CREATE TABLE plan_types (
            id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
            name text NOT NULL UNIQUE,
            display_name text NOT NULL,
            description text,
            price_monthly numeric(10,2) DEFAULT 0,
            price_yearly numeric(10,2) DEFAULT 0,
            max_projects integer NOT NULL DEFAULT 5,
            max_prompts_per_project integer NOT NULL DEFAULT 100,
            features jsonb DEFAULT '{}',
            is_active boolean DEFAULT true,
            sort_order integer DEFAULT 0,
            created_at timestamp with time zone DEFAULT now(),
            updated_at timestamp with time zone DEFAULT now()
        );
        
        RAISE NOTICE 'Created plan_types table';
    END IF;

    -- <PERSON><PERSON><PERSON><PERSON><PERSON> planları tablosu
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_plans') THEN
        CREATE TABLE user_plans (
            id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            plan_type_id uuid NOT NULL REFERENCES plan_types(id),
            status text NOT NULL DEFAULT 'active',
            billing_cycle text DEFAULT 'monthly',
            started_at timestamp with time zone DEFAULT now(),
            expires_at timestamp with time zone,
            cancelled_at timestamp with time zone,
            auto_renew boolean DEFAULT true,
            payment_method text,
            subscription_id text,
            metadata jsonb DEFAULT '{}',
            created_at timestamp with time zone DEFAULT now(),
            updated_at timestamp with time zone DEFAULT now(),
            
            CONSTRAINT valid_status CHECK (status IN ('active', 'cancelled', 'expired', 'suspended')),
            CONSTRAINT valid_billing_cycle CHECK (billing_cycle IN ('monthly', 'yearly', 'lifetime')),
            CONSTRAINT valid_dates CHECK (expires_at IS NULL OR expires_at > started_at)
        );
        
        RAISE NOTICE 'Created user_plans table';
    END IF;

    -- Kullanım istatistikleri tablosu
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'usage_stats') THEN
        CREATE TABLE usage_stats (
            id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            stat_date date NOT NULL DEFAULT CURRENT_DATE,
            projects_count integer DEFAULT 0,
            prompts_count integer DEFAULT 0,
            api_calls_count integer DEFAULT 0,
            storage_used_mb numeric(10,2) DEFAULT 0,
            last_activity_at timestamp with time zone,
            created_at timestamp with time zone DEFAULT now(),
            updated_at timestamp with time zone DEFAULT now(),
            
            UNIQUE(user_id, stat_date)
        );
        
        RAISE NOTICE 'Created usage_stats table';
    END IF;

    -- Plan işlemleri tablosu
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'plan_transactions') THEN
        CREATE TABLE plan_transactions (
            id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            from_plan_id uuid REFERENCES plan_types(id),
            to_plan_id uuid NOT NULL REFERENCES plan_types(id),
            transaction_type text NOT NULL,
            amount numeric(10,2) DEFAULT 0,
            currency text DEFAULT 'TRY',
            payment_status text DEFAULT 'pending',
            payment_provider text,
            payment_reference text,
            notes text,
            processed_at timestamp with time zone,
            created_at timestamp with time zone DEFAULT now(),
            
            CONSTRAINT valid_transaction_type CHECK (transaction_type IN ('upgrade', 'downgrade', 'renewal', 'cancellation', 'initial')),
            CONSTRAINT valid_payment_status CHECK (payment_status IN ('pending', 'completed', 'failed', 'refunded'))
        );
        
        RAISE NOTICE 'Created plan_transactions table';
    END IF;
END $$;

-- 2. İndeksleri oluştur
DO $$
BEGIN
    -- Plan types indeksleri
    IF NOT EXISTS (SELECT FROM pg_indexes WHERE indexname = 'idx_plan_types_name') THEN
        CREATE INDEX idx_plan_types_name ON plan_types(name);
    END IF;
    
    IF NOT EXISTS (SELECT FROM pg_indexes WHERE indexname = 'idx_plan_types_active') THEN
        CREATE INDEX idx_plan_types_active ON plan_types(is_active);
    END IF;

    -- User plans indeksleri
    IF NOT EXISTS (SELECT FROM pg_indexes WHERE indexname = 'idx_user_plans_user_id') THEN
        CREATE INDEX idx_user_plans_user_id ON user_plans(user_id);
    END IF;
    
    IF NOT EXISTS (SELECT FROM pg_indexes WHERE indexname = 'idx_user_plans_status') THEN
        CREATE INDEX idx_user_plans_status ON user_plans(status);
    END IF;
    
    IF NOT EXISTS (SELECT FROM pg_indexes WHERE indexname = 'idx_user_plans_expires_at') THEN
        CREATE INDEX idx_user_plans_expires_at ON user_plans(expires_at);
    END IF;

    -- Usage stats indeksleri
    IF NOT EXISTS (SELECT FROM pg_indexes WHERE indexname = 'idx_usage_stats_user_date') THEN
        CREATE INDEX idx_usage_stats_user_date ON usage_stats(user_id, stat_date);
    END IF;
    
    IF NOT EXISTS (SELECT FROM pg_indexes WHERE indexname = 'idx_usage_stats_date') THEN
        CREATE INDEX idx_usage_stats_date ON usage_stats(stat_date);
    END IF;

    -- Plan transactions indeksleri
    IF NOT EXISTS (SELECT FROM pg_indexes WHERE indexname = 'idx_plan_transactions_user_id') THEN
        CREATE INDEX idx_plan_transactions_user_id ON plan_transactions(user_id);
    END IF;
    
    IF NOT EXISTS (SELECT FROM pg_indexes WHERE indexname = 'idx_plan_transactions_created_at') THEN
        CREATE INDEX idx_plan_transactions_created_at ON plan_transactions(created_at);
    END IF;

    RAISE NOTICE 'Created all indexes';
END $$;

-- 3. RLS politikalarını etkinleştir
ALTER TABLE plan_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE plan_transactions ENABLE ROW LEVEL SECURITY;

-- 4. RLS politikalarını oluştur
DO $$
BEGIN
    -- Plan Types - Herkes okuyabilir
    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Anyone can view active plan types' AND tablename = 'plan_types') THEN
        CREATE POLICY "Anyone can view active plan types" ON plan_types
            FOR SELECT USING (is_active = true);
    END IF;

    -- User Plans - Kullanıcılar sadece kendi planlarını görebilir
    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can view own plans' AND tablename = 'user_plans') THEN
        CREATE POLICY "Users can view own plans" ON user_plans
            FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can insert own plans' AND tablename = 'user_plans') THEN
        CREATE POLICY "Users can insert own plans" ON user_plans
            FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can update own plans' AND tablename = 'user_plans') THEN
        CREATE POLICY "Users can update own plans" ON user_plans
            FOR UPDATE USING (auth.uid() = user_id);
    END IF;

    -- Usage Stats - Kullanıcılar sadece kendi istatistiklerini görebilir
    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can view own usage stats' AND tablename = 'usage_stats') THEN
        CREATE POLICY "Users can view own usage stats" ON usage_stats
            FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can insert own usage stats' AND tablename = 'usage_stats') THEN
        CREATE POLICY "Users can insert own usage stats" ON usage_stats
            FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can update own usage stats' AND tablename = 'usage_stats') THEN
        CREATE POLICY "Users can update own usage stats" ON usage_stats
            FOR UPDATE USING (auth.uid() = user_id);
    END IF;

    -- Plan Transactions - Kullanıcılar sadece kendi işlemlerini görebilir
    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can view own plan transactions' AND tablename = 'plan_transactions') THEN
        CREATE POLICY "Users can view own plan transactions" ON plan_transactions
            FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can insert own plan transactions' AND tablename = 'plan_transactions') THEN
        CREATE POLICY "Users can insert own plan transactions" ON plan_transactions
            FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;

    RAISE NOTICE 'Created all RLS policies';
END $$;

-- 5. Trigger fonksiyonlarını oluştur
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Updated_at trigger'ları
DROP TRIGGER IF EXISTS update_plan_types_updated_at ON plan_types;
CREATE TRIGGER update_plan_types_updated_at 
    BEFORE UPDATE ON plan_types 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_plans_updated_at ON user_plans;
CREATE TRIGGER update_user_plans_updated_at 
    BEFORE UPDATE ON user_plans 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_usage_stats_updated_at ON usage_stats;
CREATE TRIGGER update_usage_stats_updated_at 
    BEFORE UPDATE ON usage_stats 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 6. Varsayılan plan türlerini ekle
INSERT INTO plan_types (name, display_name, description, price_monthly, price_yearly, max_projects, max_prompts_per_project, features, sort_order) VALUES
('free', 'Ücretsiz', 'Başlangıç seviyesi kullanıcılar için ideal', 0, 0, 5, 100, '{"context_gallery": "limited", "api_access": false, "support": "email", "team_features": false}', 1),
('professional', 'Profesyonel', 'Profesyonel kullanıcılar ve küçük takımlar için', 99, 990, 50, 5000, '{"context_gallery": "full", "api_access": true, "support": "priority", "team_features": true, "advanced_analytics": true}', 2),
('enterprise', 'Kurumsal', 'Büyük organizasyonlar için özel çözümler', 0, 0, -1, -1, '{"context_gallery": "full", "api_access": true, "support": "24_7", "team_features": true, "advanced_analytics": true, "sso": true, "custom_deployment": true}', 3)
ON CONFLICT (name) DO NOTHING;

RAISE NOTICE 'Deployment completed successfully!';
RAISE NOTICE 'Plan types created: free, professional, enterprise';
RAISE NOTICE 'All tables, indexes, RLS policies, and triggers are ready';
RAISE NOTICE 'Next step: Run user-plans-functions.sql to create helper functions';
