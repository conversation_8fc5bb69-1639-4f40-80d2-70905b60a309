/**
 * Test file for Enhanced Context Gallery Modal
 * 
 * This file contains basic tests to verify the modal functionality
 * Run with: npm test enhanced-context-gallery-modal.test.tsx
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { EnhancedContextGalleryModal } from '../enhanced-context-gallery-modal';

// Mock the ContextGallery component
jest.mock('../context-gallery', () => {
  return {
    __esModule: true,
    default: ({ onSelectContext, contentOnly }: { onSelectContext: (context: { id: string; title: string }) => void; contentOnly?: boolean }) => (
      <div data-testid="context-gallery">
        <p>Context Gallery Content (contentOnly: {contentOnly?.toString()})</p>
        <button onClick={() => onSelectContext({ id: 'test', title: 'Test Context' })}>
          Select Test Context
        </button>
      </div>
    ),
  };
});

describe('EnhancedContextGalleryModal', () => {
  const mockOnOpenChange = jest.fn();
  const mockOnSelectContext = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders when open is true', () => {
    render(
      <EnhancedContextGalleryModal
        open={true}
        onOpenChange={mockOnOpenChange}
        onSelectContext={mockOnSelectContext}
      />
    );

    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByText('Context Galerisi')).toBeInTheDocument();
  });

  it('does not render when open is false', () => {
    render(
      <EnhancedContextGalleryModal
        open={false}
        onOpenChange={mockOnOpenChange}
        onSelectContext={mockOnSelectContext}
      />
    );

    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  });

  it('calls onOpenChange when close button is clicked', () => {
    render(
      <EnhancedContextGalleryModal
        open={true}
        onOpenChange={mockOnOpenChange}
        onSelectContext={mockOnSelectContext}
      />
    );

    const closeButton = screen.getByLabelText(/kapat/i);
    fireEvent.click(closeButton);

    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });

  it('calls onOpenChange when Escape key is pressed', () => {
    render(
      <EnhancedContextGalleryModal
        open={true}
        onOpenChange={mockOnOpenChange}
        onSelectContext={mockOnSelectContext}
      />
    );

    fireEvent.keyDown(document, { key: 'Escape' });

    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });

  it('passes contentOnly prop to ContextGallery', () => {
    render(
      <EnhancedContextGalleryModal
        open={true}
        onOpenChange={mockOnOpenChange}
        onSelectContext={mockOnSelectContext}
      />
    );

    expect(screen.getByText(/contentOnly: true/)).toBeInTheDocument();
  });

  it('has proper ARIA attributes', () => {
    render(
      <EnhancedContextGalleryModal
        open={true}
        onOpenChange={mockOnOpenChange}
        onSelectContext={mockOnSelectContext}
      />
    );

    const dialog = screen.getByRole('dialog');
    expect(dialog).toHaveAttribute('aria-modal', 'true');
    expect(dialog).toHaveAttribute('aria-labelledby', 'context-gallery-title');
  });

  it('calls onSelectContext when context is selected', () => {
    render(
      <EnhancedContextGalleryModal
        open={true}
        onOpenChange={mockOnOpenChange}
        onSelectContext={mockOnSelectContext}
      />
    );

    const selectButton = screen.getByText('Select Test Context');
    fireEvent.click(selectButton);

    expect(mockOnSelectContext).toHaveBeenCalledWith({
      id: 'test',
      title: 'Test Context'
    });
  });
});
