'use client';

import {
  Box,
  Container,
  Heading,
  Text,
  But<PERSON>,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Badge,
  Avatar,
  HStack,
  VStack,
  Icon,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Card,
  CardHeader,
  CardBody,
  useToast,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Flex,
  Spacer,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton,
} from '@chakra-ui/react';
import { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { AdminLayout } from '@/components/admin-layout';
import { useUsers, useUserStats } from '@/hooks/use-users';
import { 
  Search, 
  Filter, 
  Plus, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Shield, 
  Users, 
  UserCheck, 
  UserX,
  ArrowLeft,
  Calendar,
  Mail,
  Activity
} from 'lucide-react';

interface User {
  id: string;
  email: string;
  created_at: string;
  last_sign_in_at: string | null;
  email_confirmed_at: string | null;
  projects_count?: number;
  prompts_count?: number;
  is_admin?: boolean;
  admin_role?: string;
}

interface UserStats {
  totalUsers: number;
  activeUsers: number;
  adminUsers: number;
  newUsersThisMonth: number;
}

export default function UsersPage() {
  // State for filters and UI
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [newUserEmail, setNewUserEmail] = useState('');
  const [newUserRole, setNewUserRole] = useState('admin');

  // React Query hooks for optimized data fetching
  const { data: usersData, isLoading: usersLoading } = useUsers({
    page: currentPage,
    limit: 20,
    search: searchTerm || undefined,
    role: filterRole !== 'all' ? filterRole : undefined,
    status: filterStatus !== 'all' ? filterStatus : undefined,
  });

  const { data: stats, isLoading: statsLoading } = useUserStats();

  const router = useRouter();
  const toast = useToast();
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
  const { isOpen: isAddOpen, onOpen: onAddOpen, onClose: onAddClose } = useDisclosure();
  const cancelRef = useRef<HTMLButtonElement>(null);

  const isLoading = usersLoading || statsLoading;
  const users = usersData?.users || [];
  const totalUsers = usersData?.total || 0;
  const totalPages = usersData?.totalPages || 1;

  // Handle search with debouncing
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle filter changes
  const handleRoleFilterChange = (value: string) => {
    setFilterRole(value);
    setCurrentPage(1);
  };

  const handleStatusFilterChange = (value: string) => {
    setFilterStatus(value);
    setCurrentPage(1);
  };



  const handleDeleteUser = async () => {
    if (!selectedUser) return;

    try {
      // Kullanıcıyı admin tablosundan kaldır
      if (selectedUser.is_admin) {
        await supabase
          .from('admin_users')
          .delete()
          .eq('user_id', selectedUser.id);
      }

      // Auth kullanıcısını sil (bu normalde admin API ile yapılır)
      const { error } = await supabase.auth.admin.deleteUser(selectedUser.id);

      if (error) {
        throw error;
      }

      toast({
        title: 'Başarılı',
        description: 'Kullanıcı başarıyla silindi.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      fetchUsers();
      fetchStats();
      onDeleteClose();
    } catch (error) {
      console.error('Delete user error:', error);
      toast({
        title: 'Hata',
        description: 'Kullanıcı silinirken bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleAddAdmin = async () => {
    if (!newUserEmail.trim()) return;

    try {
      // Kullanıcıyı bul
      const { data: authUsers } = await supabase.auth.admin.listUsers();
      const user = authUsers?.users.find(u => u.email === newUserEmail);

      if (!user) {
        toast({
          title: 'Hata',
          description: 'Bu email adresine sahip kullanıcı bulunamadı.',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
        return;
      }

      // Admin tablosuna ekle
      const { error } = await supabase
        .from('admin_users')
        .insert({
          user_id: user.id,
          role: newUserRole,
          permissions: {},
        });

      if (error) {
        throw error;
      }

      toast({
        title: 'Başarılı',
        description: 'Kullanıcı admin olarak eklendi.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      fetchUsers();
      fetchStats();
      onAddClose();
      setNewUserEmail('');
      setNewUserRole('admin');
    } catch (error) {
      console.error('Add admin error:', error);
      toast({
        title: 'Hata',
        description: 'Admin eklenirken bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Hiç';
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <Box minH="100vh" bg="gray.50" display="flex" alignItems="center" justifyContent="center">
          <Text>Yükleniyor...</Text>
        </Box>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Container maxW="7xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <Flex align="center" justify="space-between">
            <VStack align="start" spacing={0}>
              <Heading size="lg">Kullanıcı Yönetimi</Heading>
              <Text color="gray.600">Sistem kullanıcılarını yönetin</Text>
            </VStack>
            <Button
              leftIcon={<Icon as={Plus} />}
              colorScheme="blue"
              onClick={onAddOpen}
            >
              Admin Ekle
            </Button>
          </Flex>

          {/* Stats */}
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={Users} w={4} h={4} />
                      <Text>Toplam Kullanıcı</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber color="blue.600">{stats?.totalUsers || 0}</StatNumber>
                </Stat>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={Activity} w={4} h={4} />
                      <Text>Aktif Kullanıcı</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber color="green.600">{stats?.activeUsers || 0}</StatNumber>
                  <StatHelpText>Son 24 saat</StatHelpText>
                </Stat>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={Shield} w={4} h={4} />
                      <Text>Admin Kullanıcı</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber color="purple.600">{stats?.adminUsers || 0}</StatNumber>
                </Stat>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={UserCheck} w={4} h={4} />
                      <Text>Yeni Kullanıcı</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber color="orange.600">{stats?.newUsersThisMonth || 0}</StatNumber>
                  <StatHelpText>Bu ay</StatHelpText>
                </Stat>
              </CardBody>
            </Card>
          </SimpleGrid>

          {/* Filters */}
          <Card>
            <CardHeader>
              <Heading size="md">Kullanıcı Listesi</Heading>
            </CardHeader>
            <CardBody>
              <HStack spacing={4} mb={6}>
                <InputGroup maxW="300px">
                  <InputLeftElement>
                    <Icon as={Search} color="gray.400" />
                  </InputLeftElement>
                  <Input
                    placeholder="Email ara..."
                    value={searchTerm}
                    onChange={(e) => handleSearchChange(e.target.value)}
                  />
                </InputGroup>
                <Select
                  maxW="200px"
                  value={filterRole}
                  onChange={(e) => handleRoleFilterChange(e.target.value)}
                >
                  <option value="all">Tüm Roller</option>
                  <option value="admin">Admin</option>
                  <option value="user">Kullanıcı</option>
                </Select>
              </HStack>

              <TableContainer>
                <Table variant="simple">
                  <Thead>
                    <Tr>
                      <Th>Kullanıcı</Th>
                      <Th>Rol</Th>
                      <Th>Projeler</Th>
                      <Th>Promptlar</Th>
                      <Th>Kayıt Tarihi</Th>
                      <Th>Son Giriş</Th>
                      <Th>İşlemler</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {users.map((user) => (
                      <Tr key={user.id}>
                        <Td>
                          <HStack>
                            <Avatar size="sm" name={user.email} />
                            <VStack align="start" spacing={0}>
                              <Text fontWeight="medium">{user.email}</Text>
                              <Badge
                                size="sm"
                                colorScheme={user.email_confirmed_at ? 'green' : 'yellow'}
                              >
                                {user.email_confirmed_at ? 'Doğrulanmış' : 'Bekliyor'}
                              </Badge>
                            </VStack>
                          </HStack>
                        </Td>
                        <Td>
                          {user.is_admin ? (
                            <Badge colorScheme="purple">
                              {user.admin_role}
                            </Badge>
                          ) : (
                            <Badge colorScheme="gray">Kullanıcı</Badge>
                          )}
                        </Td>
                        <Td>
                          <Text fontWeight="medium">{user.projects_count}</Text>
                        </Td>
                        <Td>
                          <Text fontWeight="medium">{user.prompts_count}</Text>
                        </Td>
                        <Td>
                          <Text fontSize="sm" color="gray.600">
                            {formatDate(user.created_at)}
                          </Text>
                        </Td>
                        <Td>
                          <Text fontSize="sm" color="gray.600">
                            {formatDate(user.last_sign_in_at)}
                          </Text>
                        </Td>
                        <Td>
                          <Menu>
                            <MenuButton
                              as={IconButton}
                              icon={<Icon as={MoreVertical} />}
                              variant="ghost"
                              size="sm"
                            />
                            <MenuList>
                              <MenuItem
                                icon={<Icon as={Edit} />}
                                onClick={() => {
                                  // Edit user functionality
                                }}
                              >
                                Düzenle
                              </MenuItem>
                              <MenuItem
                                icon={<Icon as={Trash2} />}
                                color="red.500"
                                onClick={() => {
                                  setSelectedUser(user);
                                  onDeleteOpen();
                                }}
                              >
                                Sil
                              </MenuItem>
                            </MenuList>
                          </Menu>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </TableContainer>
            </CardBody>
          </Card>
        </VStack>
      </Container>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        isOpen={isDeleteOpen}
        leastDestructiveRef={cancelRef}
        onClose={onDeleteClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              Kullanıcıyı Sil
            </AlertDialogHeader>
            <AlertDialogBody>
              <Text>
                <strong>{selectedUser?.email}</strong> kullanıcısını silmek istediğinizden emin misiniz?
              </Text>
              <Text color="red.500" fontSize="sm" mt={2}>
                Bu işlem geri alınamaz ve kullanıcının tüm verileri silinecektir.
              </Text>
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onDeleteClose}>
                İptal
              </Button>
              <Button colorScheme="red" onClick={handleDeleteUser} ml={3}>
                Sil
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>

      {/* Add Admin Modal */}
      <Modal isOpen={isAddOpen} onClose={onAddClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Admin Kullanıcı Ekle</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4}>
              <FormControl>
                <FormLabel>Email Adresi</FormLabel>
                <Input
                  placeholder="<EMAIL>"
                  value={newUserEmail}
                  onChange={(e) => setNewUserEmail(e.target.value)}
                />
              </FormControl>
              <FormControl>
                <FormLabel>Rol</FormLabel>
                <Select
                  value={newUserRole}
                  onChange={(e) => setNewUserRole(e.target.value)}
                >
                  <option value="admin">Admin</option>
                  <option value="super_admin">Super Admin</option>
                </Select>
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onAddClose}>
              İptal
            </Button>
            <Button colorScheme="blue" onClick={handleAddAdmin}>
              Ekle
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </AdminLayout>
  );
}