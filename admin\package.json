{"name": "promptbir-admin", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --port 4445", "build": "next build", "start": "next start", "lint": "next lint", "test:performance": "node scripts/performance-test.js", "test:virtualization": "node scripts/test-virtualization.js", "analyze": "ANALYZE=true npm run build"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/next-js": "^2.4.2", "@chakra-ui/react": "^2.8.2", "@chakra-ui/styled-system": "^2.10.1", "@chakra-ui/theme-tools": "^2.2.6", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^5.1.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-virtual": "^3.13.12", "dotenv": "^17.2.1", "framer-motion": "^11.15.0", "lucide-react": "^0.525.0", "next": "^14.2.30", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "recharts": "^3.1.0", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.4.5", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "eslint": "^8.57.0", "eslint-config-next": "14.2.18", "tailwindcss": "^4", "typescript": "^5"}}