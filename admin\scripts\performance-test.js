#!/usr/bin/env node

/**
 * Performance Test Script for PromptFlow Admin Panel
 * Tests database queries, API endpoints, and frontend performance
 */

import { performance } from 'perf_hooks';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env.local') });

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('🔧 Configuration:');
console.log('  SUPABASE_URL:', SUPABASE_URL ? '✅ Set' : '❌ Missing');
console.log('  SUPABASE_ANON_KEY:', SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing');

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Performance test results
const results = {
  databaseQueries: {},
  apiEndpoints: {},
  indexPerformance: {},
  timestamp: new Date().toISOString(),
};

// Test database query performance
async function testDatabaseQueries() {
  console.log('🔍 Testing database query performance...');

  const tests = [
    {
      name: 'Dashboard Stats RPC',
      query: () => supabase.rpc('get_admin_dashboard_stats'),
    },
    {
      name: 'User Stats RPC',
      query: () => supabase.rpc('get_user_stats'),
    },
    {
      name: 'Prompts with Project Filter',
      query: () => supabase
        .from('prompts')
        .select('*')
        .limit(20)
        .order('order_index'),
    },
    {
      name: 'Projects with User Filter',
      query: () => supabase
        .from('projects')
        .select('*')
        .limit(20)
        .order('created_at', { ascending: false }),
    },
    {
      name: 'Admin Users View',
      query: () => supabase
        .from('admin_users_view')
        .select('*')
        .limit(10),
    },
  ];

  for (const test of tests) {
    const start = performance.now();
    try {
      const { data, error } = await test.query();
      const end = performance.now();
      const duration = end - start;

      results.databaseQueries[test.name] = {
        duration: Math.round(duration * 100) / 100,
        success: !error,
        recordCount: data?.length || (typeof data === 'object' ? 1 : 0),
        error: error?.message,
      };

      console.log(`  ✅ ${test.name}: ${duration.toFixed(2)}ms`);
    } catch (err) {
      results.databaseQueries[test.name] = {
        duration: -1,
        success: false,
        error: err.message,
      };
      console.log(`  ❌ ${test.name}: ${err.message}`);
    }
  }
}

// Test index performance
async function testIndexPerformance() {
  console.log('📊 Testing index performance...');

  const indexTests = [
    {
      name: 'Prompts Project ID Index',
      query: `EXPLAIN (ANALYZE, BUFFERS) SELECT * FROM prompts WHERE project_id = '00000000-0000-0000-0000-000000000000' LIMIT 10;`,
    },
    {
      name: 'Prompts Order Index',
      query: `EXPLAIN (ANALYZE, BUFFERS) SELECT * FROM prompts ORDER BY order_index LIMIT 10;`,
    },
    {
      name: 'Admin Users User ID Index',
      query: `EXPLAIN (ANALYZE, BUFFERS) SELECT * FROM admin_users WHERE user_id = '00000000-0000-0000-0000-000000000000';`,
    },
  ];

  for (const test of indexTests) {
    try {
      const start = performance.now();
      const { data, error } = await supabase.rpc('exec_sql', { sql: test.query });
      const end = performance.now();

      if (!error) {
        const executionTime = data?.[0]?.['Execution Time'] || 'N/A';
        results.indexPerformance[test.name] = {
          duration: Math.round((end - start) * 100) / 100,
          executionTime,
          success: true,
        };
        console.log(`  ✅ ${test.name}: ${executionTime}ms`);
      } else {
        results.indexPerformance[test.name] = {
          success: false,
          error: error.message,
        };
        console.log(`  ❌ ${test.name}: ${error.message}`);
      }
    } catch (err) {
      results.indexPerformance[test.name] = {
        success: false,
        error: err.message,
      };
      console.log(`  ❌ ${test.name}: ${err.message}`);
    }
  }
}

// Generate performance report
function generateReport() {
  console.log('\n📋 Performance Test Report');
  console.log('=' .repeat(50));

  // Database Queries Summary
  console.log('\n🗄️  Database Queries:');
  Object.entries(results.databaseQueries).forEach(([name, result]) => {
    const status = result.success ? '✅' : '❌';
    const duration = result.duration > 0 ? `${result.duration}ms` : 'Failed';
    console.log(`  ${status} ${name}: ${duration}`);
  });

  // Performance Recommendations
  console.log('\n💡 Performance Recommendations:');
  
  const slowQueries = Object.entries(results.databaseQueries)
    .filter(([_, result]) => result.success && result.duration > 100)
    .map(([name]) => name);

  if (slowQueries.length > 0) {
    console.log('  ⚠️  Slow queries detected (>100ms):');
    slowQueries.forEach(query => console.log(`    - ${query}`));
  } else {
    console.log('  ✅ All queries performing well (<100ms)');
  }

  // Save results to file
  
  const reportDir = path.join(__dirname, '../reports');
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }

  const reportFile = path.join(reportDir, `performance-report-${Date.now()}.json`);
  fs.writeFileSync(reportFile, JSON.stringify(results, null, 2));
  
  console.log(`\n📄 Detailed report saved to: ${reportFile}`);
}

// Main test runner
async function runPerformanceTests() {
  console.log('🚀 Starting PromptFlow Admin Panel Performance Tests\n');

  try {
    await testDatabaseQueries();
    await testIndexPerformance();
    generateReport();
    
    console.log('\n✅ Performance tests completed successfully!');
  } catch (error) {
    console.error('\n❌ Performance tests failed:', error.message);
    process.exit(1);
  }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runPerformanceTests();
}

export { runPerformanceTests, results };
