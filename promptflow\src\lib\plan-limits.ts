// Plan Limitleri Kontrol Middleware ve Utility Fonksiyonları

import { supabaseBrowser as supabase } from '@/lib/supabase-browser'

export interface PlanLimits {
  current_projects: number
  current_prompts: number
  max_projects: number
  max_prompts_per_project: number
  can_create_project: boolean
  can_create_prompt: boolean
}

export interface UserPlan {
  plan_id: string
  plan_name: string
  display_name: string
  max_projects: number
  max_prompts_per_project: number
  features: Record<string, boolean | string | number>
  status: string
  expires_at: string | null
}

// Plan limitlerini kontrol et
export async function checkUserLimits(): Promise<PlanLimits | null> {
  try {
    const { data: user } = await supabase.auth.getUser()
    if (!user.user) {
      throw new Error('Kullanıcı oturumu bulunamadı')
    }

    const { data, error } = await supabase.rpc('check_user_limits', {
      user_uuid: user.user.id
    })

    if (error) {
      console.error('Plan limitleri kontrol edilemedi:', error)
      throw new Error(error.message)
    }

    return data?.[0] || null
  } catch (error) {
    console.error('Plan limitleri kontrol hatası:', error)
    return null
  }
}

// Kullanıcının aktif planını getir
export async function getUserActivePlan(): Promise<UserPlan | null> {
  try {
    const { data: user } = await supabase.auth.getUser()
    if (!user.user) {
      throw new Error('Kullanıcı oturumu bulunamadı')
    }

    const { data, error } = await supabase.rpc('get_user_active_plan', {
      user_uuid: user.user.id
    })

    if (error) {
      console.error('Aktif plan getirilemedi:', error)
      throw new Error(error.message)
    }

    return data?.[0] || null
  } catch (error) {
    console.error('Aktif plan getirme hatası:', error)
    return null
  }
}

// Proje oluşturma limiti kontrol et
export async function canCreateProject(): Promise<{ allowed: boolean; reason?: string }> {
  const limits = await checkUserLimits()
  
  if (!limits) {
    return { allowed: false, reason: 'Plan bilgileri alınamadı' }
  }

  if (!limits.can_create_project) {
    return { 
      allowed: false, 
      reason: `Proje limiti aşıldı (${limits.current_projects}/${limits.max_projects})` 
    }
  }

  return { allowed: true }
}

// Prompt oluşturma limiti kontrol et
export async function canCreatePrompt(projectId?: string): Promise<{ allowed: boolean; reason?: string }> {
  const limits = await checkUserLimits()
  
  if (!limits) {
    return { allowed: false, reason: 'Plan bilgileri alınamadı' }
  }

  if (!limits.can_create_prompt) {
    return { 
      allowed: false, 
      reason: `Prompt limiti aşıldı (${limits.current_prompts}/${limits.max_prompts_per_project})` 
    }
  }

  // Eğer belirli bir proje için kontrol ediliyorsa, o projenin prompt sayısını kontrol et
  if (projectId) {
    try {
      const { count, error } = await supabase
        .from('prompts')
        .select('*', { count: 'exact', head: true })
        .eq('project_id', projectId)

      if (error) {
        console.error('Proje prompt sayısı kontrol edilemedi:', error)
        return { allowed: false, reason: 'Proje bilgileri alınamadı' }
      }

      const currentPrompts = count || 0
      if (limits.max_prompts_per_project !== -1 && currentPrompts >= limits.max_prompts_per_project) {
        return { 
          allowed: false, 
          reason: `Bu proje için prompt limiti aşıldı (${currentPrompts}/${limits.max_prompts_per_project})` 
        }
      }
    } catch (error) {
      console.error('Proje prompt sayısı kontrol hatası:', error)
      return { allowed: false, reason: 'Proje bilgileri kontrol edilemedi' }
    }
  }

  return { allowed: true }
}

// Plan özelliği kontrol et
export async function hasPlanFeature(featureName: string): Promise<boolean> {
  const plan = await getUserActivePlan()
  return plan?.features?.[featureName] === true
}

// Kullanım istatistiklerini güncelle
export async function updateUsageStats(): Promise<void> {
  try {
    const { data: user } = await supabase.auth.getUser()
    if (!user.user) {
      throw new Error('Kullanıcı oturumu bulunamadı')
    }

    const { error } = await supabase.rpc('update_usage_stats', {
      user_uuid: user.user.id
    })

    if (error) {
      console.error('Kullanım istatistikleri güncellenemedi:', error)
      throw new Error(error.message)
    }
  } catch (error) {
    console.error('Kullanım istatistikleri güncelleme hatası:', error)
    throw error
  }
}

// Plan upgrade önerisi
export function getPlanUpgradeSuggestion(limits: PlanLimits): {
  shouldUpgrade: boolean
  reason: string
  suggestedPlan: string
} {
  // Proje limiti %80'e ulaştıysa
  if (limits.max_projects !== -1 && limits.current_projects / limits.max_projects >= 0.8) {
    return {
      shouldUpgrade: true,
      reason: 'Proje limitinizin %80\'ine ulaştınız',
      suggestedPlan: 'professional'
    }
  }

  // Prompt limiti %80'e ulaştıysa
  if (limits.max_prompts_per_project !== -1 && limits.current_prompts / limits.max_prompts_per_project >= 0.8) {
    return {
      shouldUpgrade: true,
      reason: 'Prompt limitinizin %80\'ine ulaştınız',
      suggestedPlan: 'professional'
    }
  }

  return {
    shouldUpgrade: false,
    reason: '',
    suggestedPlan: ''
  }
}

// Plan limiti hata mesajları
export const PLAN_LIMIT_MESSAGES = {
  PROJECT_LIMIT_REACHED: 'Proje oluşturma limitinize ulaştınız. Daha fazla proje oluşturmak için planınızı yükseltin.',
  PROMPT_LIMIT_REACHED: 'Prompt oluşturma limitinize ulaştınız. Daha fazla prompt oluşturmak için planınızı yükseltin.',
  FEATURE_NOT_AVAILABLE: 'Bu özellik mevcut planınızda bulunmamaktadır. Planınızı yükseltin.',
  PLAN_EXPIRED: 'Planınızın süresi dolmuştur. Lütfen planınızı yenileyin.',
  PLAN_SUSPENDED: 'Hesabınız askıya alınmıştır. Lütfen destek ekibi ile iletişime geçin.'
}

// Plan durumu kontrol et
export async function checkPlanStatus(): Promise<{
  isActive: boolean
  status: string
  message?: string
}> {
  const plan = await getUserActivePlan()
  
  if (!plan) {
    return {
      isActive: false,
      status: 'no_plan',
      message: 'Aktif plan bulunamadı'
    }
  }

  if (plan.status !== 'active') {
    return {
      isActive: false,
      status: plan.status,
      message: PLAN_LIMIT_MESSAGES.PLAN_SUSPENDED
    }
  }

  if (plan.expires_at && new Date(plan.expires_at) < new Date()) {
    return {
      isActive: false,
      status: 'expired',
      message: PLAN_LIMIT_MESSAGES.PLAN_EXPIRED
    }
  }

  return {
    isActive: true,
    status: 'active'
  }
}
