/**
 * Environment Variables Security Checker
 * Ensures sensitive data is not exposed to client-side
 */

// Sensitive environment variables that should NEVER be exposed to client
const SENSITIVE_ENV_VARS = [
  'SUPABASE_SERVICE_ROLE_KEY',
  'SUPABASE_JWT_SECRET',
  'DATABASE_URL',
  'NEXTAUTH_SECRET',
  'OPENAI_API_KEY',
  'STRIPE_SECRET_KEY',
  'WEBHOOK_SECRET',
  'PRIVATE_KEY',
  'SECRET_KEY',
  'API_SECRET',
  'ADMIN_PASSWORD',
  'DB_PASSWORD',
  'ENCRYPTION_KEY'
] as const

// Client-safe environment variables (must start with NEXT_PUBLIC_)
const ALLOWED_CLIENT_ENV_VARS = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'NEXT_PUBLIC_APP_URL',
  'NEXT_PUBLIC_ENVIRONMENT',
  'NEXT_PUBLIC_ANALYTICS_ID',
  'NEXT_PUBLIC_SENTRY_DSN'
] as const

interface SecurityCheckResult {
  isSecure: boolean
  violations: string[]
  warnings: string[]
  recommendations: string[]
}

/**
 * Check if environment variables are properly secured
 */
export function checkEnvironmentSecurity(): SecurityCheckResult {
  const violations: string[] = []
  const warnings: string[] = []
  const recommendations: string[] = []

  // Check if we're in client-side environment
  const isClientSide = typeof window !== 'undefined'

  if (isClientSide) {
    // Client-side checks
    console.log('🔍 [ENV_SECURITY] Running client-side environment security check...')

    // Check for exposed sensitive variables
    SENSITIVE_ENV_VARS.forEach(varName => {
      if (process.env[varName]) {
        violations.push(`CRITICAL: ${varName} is exposed to client-side code`)
      }
    })

    // Check for non-NEXT_PUBLIC_ variables in client
    Object.keys(process.env).forEach(key => {
      if (!key.startsWith('NEXT_PUBLIC_') && 
          !key.startsWith('NODE_') && 
          !key.startsWith('npm_') &&
          key !== '__NEXT_PRIVATE_PREBUNDLED_REACT') {
        warnings.push(`Variable ${key} is available on client but doesn't follow NEXT_PUBLIC_ convention`)
      }
    })

    // Check for required client variables
    const requiredClientVars = ['NEXT_PUBLIC_SUPABASE_URL', 'NEXT_PUBLIC_SUPABASE_ANON_KEY']
    requiredClientVars.forEach(varName => {
      if (!process.env[varName]) {
        violations.push(`Required client variable ${varName} is missing`)
      }
    })

  } else {
    // Server-side checks
    console.log('🔍 [ENV_SECURITY] Running server-side environment security check...')

    // Check for missing sensitive variables that should be present
    const requiredServerVars = ['NEXT_PUBLIC_SUPABASE_URL', 'NEXT_PUBLIC_SUPABASE_ANON_KEY']
    requiredServerVars.forEach(varName => {
      if (!process.env[varName]) {
        violations.push(`Required server variable ${varName} is missing`)
      }
    })

    // Check for weak or default values
    if (process.env.NEXT_PUBLIC_SUPABASE_URL?.includes('localhost') && 
        process.env.NODE_ENV === 'production') {
      warnings.push('Using localhost Supabase URL in production environment')
    }

    // Check for development keys in production
    if (process.env.NODE_ENV === 'production') {
      if (process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.includes('test') ||
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.includes('dev')) {
        warnings.push('Potentially using development keys in production')
      }
    }
  }

  // General recommendations
  if (process.env.NODE_ENV !== 'production') {
    recommendations.push('Use .env.local for local development secrets')
    recommendations.push('Never commit .env files to version control')
    recommendations.push('Use different keys for development and production')
  }

  recommendations.push('Regularly rotate API keys and secrets')
  recommendations.push('Use environment-specific configurations')
  recommendations.push('Monitor for exposed secrets in client bundles')

  const isSecure = violations.length === 0

  return {
    isSecure,
    violations,
    warnings,
    recommendations
  }
}

/**
 * Validate specific environment variable
 */
export function validateEnvVar(
  name: string,
  value: string | undefined,
  options: {
    required?: boolean
    clientSafe?: boolean
    pattern?: RegExp
    minLength?: number
    maxLength?: number
  } = {}
): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []

  // Check if required
  if (options.required && !value) {
    errors.push(`Environment variable ${name} is required but not set`)
    return { isValid: false, errors, warnings }
  }

  if (!value) {
    return { isValid: true, errors, warnings }
  }

  // Check client safety
  if (options.clientSafe && !name.startsWith('NEXT_PUBLIC_')) {
    errors.push(`Client-safe variable ${name} must start with NEXT_PUBLIC_`)
  }

  if (!options.clientSafe && name.startsWith('NEXT_PUBLIC_')) {
    warnings.push(`Variable ${name} starts with NEXT_PUBLIC_ but is marked as not client-safe`)
  }

  // Check pattern
  if (options.pattern && !options.pattern.test(value)) {
    errors.push(`Environment variable ${name} does not match required pattern`)
  }

  // Check length
  if (options.minLength && value.length < options.minLength) {
    errors.push(`Environment variable ${name} is too short (minimum ${options.minLength} characters)`)
  }

  if (options.maxLength && value.length > options.maxLength) {
    errors.push(`Environment variable ${name} is too long (maximum ${options.maxLength} characters)`)
  }

  // Check for common weak values
  const weakValues = ['test', 'dev', 'development', 'localhost', 'example', 'changeme', '123456']
  if (weakValues.some(weak => value.toLowerCase().includes(weak)) && 
      process.env.NODE_ENV === 'production') {
    warnings.push(`Environment variable ${name} appears to contain weak or development values`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Get safe environment variables for client-side use
 */
export function getSafeClientEnv(): Record<string, string> {
  const safeEnv: Record<string, string> = {}

  ALLOWED_CLIENT_ENV_VARS.forEach(varName => {
    if (process.env[varName]) {
      safeEnv[varName] = process.env[varName]!
    }
  })

  return safeEnv
}

/**
 * Sanitize environment variable for logging
 */
export function sanitizeEnvForLogging(name: string, value: string): string {
  // Never log sensitive values
  if (SENSITIVE_ENV_VARS.includes(name as typeof SENSITIVE_ENV_VARS[number])) {
    return '[REDACTED]'
  }

  // For API keys and tokens, show only first and last few characters
  if (name.toLowerCase().includes('key') || 
      name.toLowerCase().includes('token') || 
      name.toLowerCase().includes('secret')) {
    if (value.length > 8) {
      return `${value.slice(0, 4)}...${value.slice(-4)}`
    }
    return '[REDACTED]'
  }

  // For URLs, hide sensitive parts
  if (name.toLowerCase().includes('url') && value.includes('://')) {
    try {
      const url = new URL(value)
      return `${url.protocol}//${url.hostname}${url.pathname ? url.pathname : ''}`
    } catch {
      return value
    }
  }

  return value
}

/**
 * Runtime security check - call this in app initialization
 */
export function performRuntimeSecurityCheck(): void {
  const result = checkEnvironmentSecurity()

  if (!result.isSecure) {
    console.error('🚨 [ENV_SECURITY] CRITICAL SECURITY VIOLATIONS DETECTED:')
    result.violations.forEach(violation => {
      console.error(`  ❌ ${violation}`)
    })
    
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Critical environment security violations detected in production')
    }
  }

  if (result.warnings.length > 0) {
    console.warn('⚠️ [ENV_SECURITY] Security warnings:')
    result.warnings.forEach(warning => {
      console.warn(`  ⚠️ ${warning}`)
    })
  }

  if (result.recommendations.length > 0 && process.env.NODE_ENV === 'development') {
    console.info('💡 [ENV_SECURITY] Security recommendations:')
    result.recommendations.forEach(rec => {
      console.info(`  💡 ${rec}`)
    })
  }

  console.log('✅ [ENV_SECURITY] Environment security check completed')
}

// Auto-run security check in development
if (process.env.NODE_ENV === 'development' && typeof window === 'undefined') {
  performRuntimeSecurityCheck()
}
