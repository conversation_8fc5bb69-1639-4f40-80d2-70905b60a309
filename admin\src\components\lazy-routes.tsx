'use client';

import { lazy, Suspense } from 'react';
import { Box, Spinner, Text, VStack } from '@chakra-ui/react';

// Lazy load admin page components
export const LazyDashboard = lazy(() => import('@/app/dashboard/page'));
export const LazyUsers = lazy(() => import('@/app/users/page'));
export const LazyProjects = lazy(() => import('@/app/projects/page'));
export const LazyPrompts = lazy(() => import('@/app/prompts/page'));
export const LazyContexts = lazy(() => import('@/app/contexts/page'));
export const LazySettings = lazy(() => import('@/app/settings/page'));

// Loading component for lazy routes
export function RouteLoadingSpinner() {
  return (
    <Box 
      minH="100vh" 
      display="flex" 
      alignItems="center" 
      justifyContent="center"
      bg="gray.50"
    >
      <VStack spacing={4}>
        <Spinner size="xl" color="blue.500" thickness="4px" />
        <Text color="gray.600">Sayfa yükleniyor...</Text>
      </VStack>
    </Box>
  );
}

// Wrapper component for lazy routes with error boundary
export function LazyRoute({ children }: { children: React.ReactNode }) {
  return (
    <Suspense fallback={<RouteLoadingSpinner />}>
      {children}
    </Suspense>
  );
}
