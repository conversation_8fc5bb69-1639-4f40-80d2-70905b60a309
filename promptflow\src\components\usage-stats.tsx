'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { BarChart3, TrendingUp, Calendar, Activity } from 'lucide-react'
import { useUsageStats, useUserLimits, useUserActivePlan } from '@/hooks/use-plans'
import { format, parseISO } from 'date-fns'
import { tr } from 'date-fns/locale'

interface UsageStatsProps {
  days?: number
  compact?: boolean
}

export function UsageStats({ days = 30, compact = false }: UsageStatsProps) {
  const { data: usageStats, isLoading: statsLoading } = useUsageStats(days)
  const { data: limits, isLoading: limitsLoading } = useUserLimits()
  const { data: activePlan } = useUserActivePlan()

  if (statsLoading || limitsLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="h-6 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 bg-gray-200 rounded animate-pulse w-2/3" />
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
          </div>
        </CardContent>
      </Card>
    )
  }

  const getUsageColor = (current: number, max: number) => {
    if (max === -1) return 'text-green-600'
    const percentage = (current / max) * 100
    if (percentage >= 90) return 'text-red-600'
    if (percentage >= 70) return 'text-yellow-600'
    return 'text-green-600'
  }



  const getActivityLevel = () => {
    if (!usageStats || usageStats.length === 0) return 'Düşük'
    
    const recentStats = usageStats.slice(0, 7) // Son 7 gün
    const totalActivity = recentStats.reduce((sum, stat) => sum + stat.projects_count + stat.prompts_count, 0)
    const avgDaily = totalActivity / 7
    
    if (avgDaily > 50) return 'Yüksek'
    if (avgDaily > 20) return 'Orta'
    return 'Düşük'
  }

  const getActivityColor = (level: string) => {
    switch (level) {
      case 'Yüksek':
        return 'bg-green-100 text-green-800'
      case 'Orta':
        return 'bg-yellow-100 text-yellow-800'
      case 'Düşük':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getTotalUsage = () => {
    if (!usageStats || usageStats.length === 0) return { projects: 0, prompts: 0 }
    
    return usageStats.reduce(
      (totals, stat) => ({
        projects: Math.max(totals.projects, stat.projects_count),
        prompts: Math.max(totals.prompts, stat.prompts_count)
      }),
      { projects: 0, prompts: 0 }
    )
  }

  const getWeeklyGrowth = () => {
    if (!usageStats || usageStats.length < 14) return null
    
    const thisWeek = usageStats.slice(0, 7)
    const lastWeek = usageStats.slice(7, 14)
    
    const thisWeekTotal = thisWeek.reduce((sum, stat) => sum + stat.projects_count + stat.prompts_count, 0)
    const lastWeekTotal = lastWeek.reduce((sum, stat) => sum + stat.projects_count + stat.prompts_count, 0)
    
    if (lastWeekTotal === 0) return null
    
    const growth = ((thisWeekTotal - lastWeekTotal) / lastWeekTotal) * 100
    return Math.round(growth)
  }

  const totalUsage = getTotalUsage()
  const activityLevel = getActivityLevel()
  const weeklyGrowth = getWeeklyGrowth()

  if (compact) {
    return (
      <div className="space-y-3">
        {limits && (
          <>
            <div className="flex items-center justify-between text-sm">
              <span>Projeler</span>
              <span className={getUsageColor(limits.current_projects, limits.max_projects)}>
                {limits.current_projects} / {limits.max_projects === -1 ? '∞' : limits.max_projects}
              </span>
            </div>
            <Progress 
              value={limits.max_projects === -1 ? 0 : (limits.current_projects / limits.max_projects) * 100}
              className="h-2"
            />
            
            <div className="flex items-center justify-between text-sm">
              <span>Prompt&apos;lar</span>
              <span className={getUsageColor(limits.current_prompts, limits.max_prompts_per_project)}>
                {limits.current_prompts} / {limits.max_prompts_per_project === -1 ? '∞' : limits.max_prompts_per_project}
              </span>
            </div>
            <Progress 
              value={limits.max_prompts_per_project === -1 ? 0 : (limits.current_prompts / limits.max_prompts_per_project) * 100}
              className="h-2"
            />
          </>
        )}
      </div>
    )
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-blue-500" />
            <div>
              <CardTitle className="text-lg">Kullanım İstatistikleri</CardTitle>
              <CardDescription>
                Son {days} günlük kullanım verileriniz
              </CardDescription>
            </div>
          </div>
          <Badge className={getActivityColor(activityLevel)}>
            {activityLevel} Aktivite
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Mevcut Limitler */}
        {limits && (
          <div className="space-y-3">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Mevcut Kullanım
            </h4>
            
            <div className="space-y-3">
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Projeler</span>
                  <span className={getUsageColor(limits.current_projects, limits.max_projects)}>
                    {limits.current_projects} / {limits.max_projects === -1 ? '∞' : limits.max_projects}
                  </span>
                </div>
                <Progress 
                  value={limits.max_projects === -1 ? 0 : (limits.current_projects / limits.max_projects) * 100}
                  className="h-2"
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Prompt&apos;lar (Proje başına max)</span>
                  <span className={getUsageColor(limits.current_prompts, limits.max_prompts_per_project)}>
                    {limits.current_prompts} / {limits.max_prompts_per_project === -1 ? '∞' : limits.max_prompts_per_project}
                  </span>
                </div>
                <Progress 
                  value={limits.max_prompts_per_project === -1 ? 0 : (limits.current_prompts / limits.max_prompts_per_project) * 100}
                  className="h-2"
                />
              </div>
            </div>
          </div>
        )}

        <Separator />

        {/* İstatistik Özeti */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <div className="text-2xl font-bold text-blue-600">
              {totalUsage.projects}
            </div>
            <div className="text-xs text-gray-600">Toplam Proje</div>
          </div>
          
          <div className="space-y-1">
            <div className="text-2xl font-bold text-green-600">
              {totalUsage.prompts}
            </div>
            <div className="text-xs text-gray-600">Toplam Prompt</div>
          </div>
        </div>

        {/* Haftalık Büyüme */}
        {weeklyGrowth !== null && (
          <div className="flex items-center gap-2 p-2 bg-gray-50 rounded">
            <TrendingUp className={`h-4 w-4 ${weeklyGrowth >= 0 ? 'text-green-500' : 'text-red-500'}`} />
            <span className="text-sm">
              Bu hafta {weeklyGrowth >= 0 ? '+' : ''}{weeklyGrowth}% değişim
            </span>
          </div>
        )}

        {/* Son Aktivite */}
        {usageStats && usageStats.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Son Aktivite
            </h4>
            <div className="text-sm text-gray-600">
              {format(parseISO(usageStats[0].stat_date), 'dd MMMM yyyy', { locale: tr })}
            </div>
          </div>
        )}

        {/* Plan Bilgisi */}
        {activePlan && (
          <div className="pt-2 border-t">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Mevcut Plan</span>
              <Badge variant="outline">
                {activePlan.display_name}
              </Badge>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
