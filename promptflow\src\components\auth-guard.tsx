'use client'

import { useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useUser, useAuthStateListener } from '@/hooks/use-auth'

interface AuthGuardProps {
  children: React.ReactNode
}

export function AuthGuard({ children }: AuthGuardProps) {
  const { data: user, isLoading, error } = useUser()
  const router = useRouter()
  const pathname = usePathname()

  console.log(`🛡️ [AUTH_GUARD] Status: loading=${isLoading}, user=${user?.email || 'null'}, error=${error ? 'YES' : 'NO'}, pathname=${pathname}`)

  // Auth state değişikliklerini dinle
  useAuthStateListener()

  // Authenticated user'ları dashboard'a yönlendir
  useEffect(() => {
    if (!isLoading && user && pathname === '/auth') {
      console.log(`🚀 [AUTH_GUARD] Authenticated user on /auth, redirecting to dashboard`)
      router.replace('/dashboard')
    }
  }, [user, isLoading, pathname, router])

  // Loading durumu - middleware handles redirects, so we just show loading
  if (isLoading) {
    console.log(`⏳ [AUTH_GUARD] Showing loading state`)
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">PromptFlow yükleniyor...</p>
        </div>
      </div>
    )
  }

  // Auth error durumu (refresh token hataları vs.)
  if (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    console.error(`❌ [AUTH_GUARD] Auth error:`, errorMessage)

    if (errorMessage.includes('Invalid Refresh Token') ||
        errorMessage.includes('Refresh Token Not Found')) {
      console.log(`🔄 [AUTH_GUARD] Refresh token error - showing loading`)
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Oturum yenileniyor...</p>
          </div>
        </div>
      )
    }
  }

  // If no user, let middleware handle redirect - just show loading
  if (!user) {
    console.log(`🚫 [AUTH_GUARD] No user - showing loading (middleware will handle redirect)`)
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Kimlik doğrulanıyor...</p>
        </div>
      </div>
    )
  }

  // Kullanıcı giriş yapmışsa ana uygulamayı göster
  console.log(`✅ [AUTH_GUARD] User authenticated - showing app`)
  return <>{children}</>
}