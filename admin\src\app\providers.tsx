'use client';

import { <PERSON><PERSON><PERSON><PERSON>ider } from '@chakra-ui/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState, useEffect } from 'react';
import { RealtimeProvider } from '@/components/realtime/realtime-provider';

export function Providers({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false);
  
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000, // 5 dakika
        gcTime: 10 * 60 * 1000, // 10 dakika
        retry: (failureCount, error) => {
          // Auth hataları için retry yapma
          if (error instanceof Error && (
            error.message.includes('401') ||
            error.message.includes('403') ||
            error.message.includes('Unauthorized') ||
            error.message.includes('Admin access required') ||
            error.message.includes('Invalid login credentials')
          )) {
            return false;
          }
          return failureCount < 3;
        },
        refetchOnWindowFocus: false, // Hydration mismatch'i önlemek için
        refetchOnMount: true,
      },
      mutations: {
        retry: (failureCount, error) => {
          // Auth hataları için retry yapma
          if (error instanceof Error && (
            error.message.includes('401') ||
            error.message.includes('403') ||
            error.message.includes('Unauthorized') ||
            error.message.includes('Admin access required')
          )) {
            return false;
          }
          return failureCount < 2;
        },
      },
    },
  }));

  // Hydration mismatch'i önlemek için mount kontrolü
  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <ChakraProvider>
        <RealtimeProvider>
          {children}
          {mounted && process.env.NODE_ENV === 'development' && (
            <ReactQueryDevtools initialIsOpen={false} />
          )}
        </RealtimeProvider>
      </ChakraProvider>
    </QueryClientProvider>
  );
} 