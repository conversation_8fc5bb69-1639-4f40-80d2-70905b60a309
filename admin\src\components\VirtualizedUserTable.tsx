'use client';

import React, { useMemo, useRef, useCallback, memo } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Text,
  Badge,
  Button,
  Spinner,
  Alert,
  AlertIcon,
  Flex,
  Input,
  Select,
  HStack,
  VStack,
  useColorModeValue,
  IconButton,
  Tooltip,
  Checkbox,
} from '@chakra-ui/react';
import {
  Search,
  Filter,
  ChevronUp,
  ChevronDown,
  MoreVertical,
  UserCheck,
  UserX,
  Trash2,
} from 'lucide-react';
import { usePaginatedUsers, useUpdateUserStatus, useBulkUserOperations, PaginatedUser } from '@/hooks/usePaginatedUsers';

/**
 * Props for the VirtualizedUserTable component
 */
interface VirtualizedUserTableProps {
  /** Height of the table container */
  height?: number;
  /** Enable multi-select functionality */
  enableMultiSelect?: boolean;
  /** Callback when users are selected */
  onSelectionChange?: (selectedUsers: string[]) => void;
  /** Custom row actions */
  customActions?: (user: PaginatedUser) => React.ReactNode;
}

/**
 * Individual table row component with memoization for performance
 */
const UserTableRow = memo(({ 
  user, 
  isSelected, 
  onToggleSelect, 
  onUpdateStatus,
  customActions,
  enableMultiSelect 
}: {
  user: PaginatedUser;
  isSelected: boolean;
  onToggleSelect: (userId: string) => void;
  onUpdateStatus: (userId: string, isActive: boolean) => void;
  customActions?: (user: PaginatedUser) => React.ReactNode;
  enableMultiSelect: boolean;
}) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const handleStatusToggle = useCallback(() => {
    onUpdateStatus(user.user_id, !user.is_active);
  }, [user.user_id, user.is_active, onUpdateStatus]);

  const handleSelectToggle = useCallback(() => {
    onToggleSelect(user.user_id);
  }, [user.user_id, onToggleSelect]);

  return (
    <Tr
      bg={isSelected ? 'blue.50' : bgColor}
      _hover={{ bg: hoverBg }}
      borderBottom="1px"
      borderColor={borderColor}
      role="row"
      aria-selected={isSelected}
    >
      {enableMultiSelect && (
        <Td width="40px" padding="8px">
          <Checkbox
            isChecked={isSelected}
            onChange={handleSelectToggle}
            aria-label={`Select user ${user.email}`}
          />
        </Td>
      )}
      
      <Td>
        <VStack align="start" spacing={1}>
          <Text fontWeight="medium" fontSize="sm">
            {user.email}
          </Text>
          <Text fontSize="xs" color="gray.500">
            ID: {user.user_id.slice(0, 8)}...
          </Text>
        </VStack>
      </Td>
      
      <Td>
        <Badge
          colorScheme={user.role === 'super_admin' ? 'purple' : 'blue'}
          variant="subtle"
        >
          {user.role === 'super_admin' ? 'Super Admin' : 'Admin'}
        </Badge>
      </Td>
      
      <Td>
        <Badge
          colorScheme={user.is_active ? 'green' : 'red'}
          variant="subtle"
        >
          {user.is_active ? 'Aktif' : 'Pasif'}
        </Badge>
      </Td>
      
      <Td>
        <VStack align="start" spacing={1}>
          <Text fontSize="sm">{user.projects_count} Proje</Text>
          <Text fontSize="sm">{user.prompts_count} Prompt</Text>
        </VStack>
      </Td>
      
      <Td>
        <Text fontSize="sm">
          {user.last_sign_in_at 
            ? new Date(user.last_sign_in_at).toLocaleDateString('tr-TR')
            : 'Hiç giriş yapmamış'
          }
        </Text>
      </Td>
      
      <Td>
        <Text fontSize="sm">
          {new Date(user.created_at).toLocaleDateString('tr-TR')}
        </Text>
      </Td>
      
      <Td>
        <HStack spacing={1}>
          <Tooltip label={user.is_active ? 'Pasif Yap' : 'Aktif Yap'}>
            <IconButton
              aria-label={user.is_active ? 'Deactivate user' : 'Activate user'}
              icon={user.is_active ? <UserX size={16} /> : <UserCheck size={16} />}
              size="sm"
              variant="ghost"
              colorScheme={user.is_active ? 'red' : 'green'}
              onClick={handleStatusToggle}
            />
          </Tooltip>
          
          {customActions && customActions(user)}
          
          <Tooltip label="Daha fazla işlem">
            <IconButton
              aria-label="More actions"
              icon={<MoreVertical size={16} />}
              size="sm"
              variant="ghost"
            />
          </Tooltip>
        </HStack>
      </Td>
    </Tr>
  );
});

UserTableRow.displayName = 'UserTableRow';

/**
 * High-performance virtualized user table component
 * 
 * Features:
 * - Virtual scrolling for 10,000+ records
 * - Infinite scroll pagination
 * - Search and filtering
 * - Multi-select functionality
 * - Optimistic updates
 * - Accessibility support
 * - Memory-efficient rendering
 */
export const VirtualizedUserTable: React.FC<VirtualizedUserTableProps> = memo(({
  height = 600,
  enableMultiSelect = false,
  onSelectionChange,
  customActions,
}) => {
  const parentRef = useRef<HTMLDivElement>(null);
  const [selectedUsers, setSelectedUsers] = React.useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = React.useState('');
  const [roleFilter, setRoleFilter] = React.useState('all');
  const [statusFilter, setStatusFilter] = React.useState('all');
  const [sortBy, setSortBy] = React.useState<'created_at' | 'email' | 'last_sign_in_at'>('created_at');
  const [sortOrder, setSortOrder] = React.useState<'asc' | 'desc'>('desc');

  // Data fetching with infinite scroll
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error,
  } = usePaginatedUsers({
    pageSize: 50,
    search: searchTerm,
    role: roleFilter,
    status: statusFilter,
    sortBy,
    sortOrder,
  });

  // Mutations
  const updateUserStatus = useUpdateUserStatus();
  const bulkOperations = useBulkUserOperations();

  // Flatten all pages into a single array for virtualization
  const allUsers = useMemo(() => {
    return data?.pages.flatMap(page => page.data) ?? [];
  }, [data]);

  // Virtual scrolling setup
  const rowVirtualizer = useVirtualizer({
    count: hasNextPage ? allUsers.length + 1 : allUsers.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 60, // Estimated row height
    overscan: 10, // Render extra items for smooth scrolling
  });

  // Load more data when scrolling near the end
  React.useEffect(() => {
    const [lastItem] = [...rowVirtualizer.getVirtualItems()].reverse();

    if (!lastItem) return;

    if (
      lastItem.index >= allUsers.length - 1 &&
      hasNextPage &&
      !isFetchingNextPage
    ) {
      fetchNextPage();
    }
  }, [
    hasNextPage,
    fetchNextPage,
    allUsers.length,
    isFetchingNextPage,
    rowVirtualizer.getVirtualItems(),
  ]);

  // Selection handlers
  const handleToggleSelect = useCallback((userId: string) => {
    setSelectedUsers(prev => {
      const newSet = new Set(prev);
      if (newSet.has(userId)) {
        newSet.delete(userId);
      } else {
        newSet.add(userId);
      }
      onSelectionChange?.(Array.from(newSet));
      return newSet;
    });
  }, [onSelectionChange]);

  const handleSelectAll = useCallback(() => {
    const allUserIds = allUsers.map(user => user.user_id);
    setSelectedUsers(new Set(allUserIds));
    onSelectionChange?.(allUserIds);
  }, [allUsers, onSelectionChange]);

  const handleClearSelection = useCallback(() => {
    setSelectedUsers(new Set());
    onSelectionChange?.([]);
  }, [onSelectionChange]);

  // Status update handler
  const handleUpdateStatus = useCallback((userId: string, isActive: boolean) => {
    updateUserStatus.mutate({ userId, isActive });
  }, [updateUserStatus]);

  // Sorting handlers
  const handleSort = useCallback((column: typeof sortBy) => {
    if (sortBy === column) {
      setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('desc');
    }
  }, [sortBy]);

  const getSortIcon = useCallback((column: typeof sortBy) => {
    if (sortBy !== column) return null;
    return sortOrder === 'asc' ? <ChevronUp size={16} /> : <ChevronDown size={16} />;
  }, [sortBy, sortOrder]);

  // Loading state
  if (isLoading) {
    return (
      <Flex justify="center" align="center" height={height}>
        <VStack spacing={4}>
          <Spinner size="lg" />
          <Text>Kullanıcılar yükleniyor...</Text>
        </VStack>
      </Flex>
    );
  }

  // Error state
  if (isError) {
    return (
      <Alert status="error">
        <AlertIcon />
        <VStack align="start" spacing={2}>
          <Text fontWeight="bold">Kullanıcılar yüklenemedi</Text>
          <Text fontSize="sm">{error?.message}</Text>
        </VStack>
      </Alert>
    );
  }

  return (
    <VStack spacing={4} align="stretch">
      {/* Filters and Search */}
      <Flex gap={4} wrap="wrap" align="center">
        <Input
          placeholder="Email ile ara..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          leftElement={<Search size={16} />}
          maxWidth="300px"
        />
        
        <Select
          value={roleFilter}
          onChange={(e) => setRoleFilter(e.target.value)}
          maxWidth="150px"
        >
          <option value="all">Tüm Roller</option>
          <option value="admin">Admin</option>
          <option value="super_admin">Super Admin</option>
        </Select>
        
        <Select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          maxWidth="150px"
        >
          <option value="all">Tüm Durumlar</option>
          <option value="active">Aktif</option>
          <option value="inactive">Pasif</option>
        </Select>

        {enableMultiSelect && selectedUsers.size > 0 && (
          <HStack>
            <Text fontSize="sm">
              {selectedUsers.size} kullanıcı seçili
            </Text>
            <Button size="sm" onClick={handleClearSelection}>
              Seçimi Temizle
            </Button>
          </HStack>
        )}
      </Flex>

      {/* Virtualized Table */}
      <Box
        ref={parentRef}
        height={height}
        overflow="auto"
        border="1px"
        borderColor="gray.200"
        borderRadius="md"
        role="table"
        aria-label="Kullanıcı listesi"
      >
        <Table size="sm" variant="simple">
          <Thead position="sticky" top={0} bg="white" zIndex={1}>
            <Tr>
              {enableMultiSelect && (
                <Th width="40px">
                  <Checkbox
                    isChecked={selectedUsers.size === allUsers.length && allUsers.length > 0}
                    isIndeterminate={selectedUsers.size > 0 && selectedUsers.size < allUsers.length}
                    onChange={selectedUsers.size === allUsers.length ? handleClearSelection : handleSelectAll}
                    aria-label="Select all users"
                  />
                </Th>
              )}
              <Th cursor="pointer" onClick={() => handleSort('email')}>
                <HStack>
                  <Text>Email</Text>
                  {getSortIcon('email')}
                </HStack>
              </Th>
              <Th>Rol</Th>
              <Th>Durum</Th>
              <Th>İstatistikler</Th>
              <Th cursor="pointer" onClick={() => handleSort('last_sign_in_at')}>
                <HStack>
                  <Text>Son Giriş</Text>
                  {getSortIcon('last_sign_in_at')}
                </HStack>
              </Th>
              <Th cursor="pointer" onClick={() => handleSort('created_at')}>
                <HStack>
                  <Text>Kayıt Tarihi</Text>
                  {getSortIcon('created_at')}
                </HStack>
              </Th>
              <Th>İşlemler</Th>
            </Tr>
          </Thead>
          
          <Tbody>
            <Tr>
              <Td colSpan={enableMultiSelect ? 8 : 7} padding={0}>
                <Box
                  height={`${rowVirtualizer.getTotalSize()}px`}
                  position="relative"
                >
                  {rowVirtualizer.getVirtualItems().map((virtualItem) => {
                    const isLoaderRow = virtualItem.index > allUsers.length - 1;
                    const user = allUsers[virtualItem.index];

                    return (
                      <Box
                        key={virtualItem.index}
                        position="absolute"
                        top={0}
                        left={0}
                        width="100%"
                        height={`${virtualItem.size}px`}
                        transform={`translateY(${virtualItem.start}px)`}
                      >
                        {isLoaderRow ? (
                          hasNextPage ? (
                            <Flex justify="center" align="center" height="100%">
                              <Spinner size="sm" />
                              <Text ml={2} fontSize="sm">Daha fazla yükleniyor...</Text>
                            </Flex>
                          ) : null
                        ) : (
                          <UserTableRow
                            user={user}
                            isSelected={selectedUsers.has(user.user_id)}
                            onToggleSelect={handleToggleSelect}
                            onUpdateStatus={handleUpdateStatus}
                            customActions={customActions}
                            enableMultiSelect={enableMultiSelect}
                          />
                        )}
                      </Box>
                    );
                  })}
                </Box>
              </Td>
            </Tr>
          </Tbody>
        </Table>
      </Box>

      {/* Stats */}
      <Text fontSize="sm" color="gray.600">
        Toplam {data?.pages[0]?.count || 0} kullanıcı
        {selectedUsers.size > 0 && ` • ${selectedUsers.size} seçili`}
      </Text>
    </VStack>
  );
});

VirtualizedUserTable.displayName = 'VirtualizedUserTable';
