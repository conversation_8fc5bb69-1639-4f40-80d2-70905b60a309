---
type: "fixes"
role: "typescript_troubleshooting_guide"
dependencies:
  - "core/PROJECT_CORE.md"
  - "frontend/MAIN_APP.md"
  - "frontend/ADMIN_PANEL.md"
  - "backend/SUPABASE.md"
related_fixes:
  - "fixes/BUILD_ERROR_RESOLUTION.md"
  - "fixes/ESLINT_CONFIGURATION.md"
auto_update_from:
  - "frontend/MAIN_APP.md"
  - "frontend/ADMIN_PANEL.md"
  - "backend/SUPABASE.md"
solution_history:
  - date: "2025-01-29"
    issue: "TypeScript any type violations"
    solution: "Replace any with Record<string, boolean | string | number>"
    status: "resolved"
last_updated: "2025-01-29"
---

# PromptFlow TypeScript Patterns & Error Resolution

## Type Safety Enforcement

### Strict TypeScript Configuration
```json
// tsconfig.json requirements
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true
  }
}
```

### ESLint TypeScript Rules
```json
// .eslintrc.json
{
  "rules": {
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/no-unused-vars": "warn",
    "@typescript-eslint/prefer-const": "error",
    "@typescript-eslint/no-inferrable-types": "error"
  }
}
```

## Common Type Patterns

### Database Type Definitions
```typescript
// ✅ Proper Supabase type definitions
export interface Database {
  public: {
    Tables: {
      plan_types: {
        Row: {
          id: string
          name: string
          display_name: string
          max_projects: number
          max_prompts_per_project: number
          features: Record<string, boolean | string | number> // Not any!
          is_active: boolean
          sort_order: number
          created_at: string
          updated_at: string
        }
        Insert: {
          name: string
          display_name: string
          description?: string
          price_monthly?: number
          price_yearly?: number
          max_projects?: number
          max_prompts_per_project?: number
          features?: Record<string, boolean | string | number> // Not any!
          is_active?: boolean
          sort_order?: number
        }
        Update: {
          name?: string
          display_name?: string
          description?: string
          price_monthly?: number
          price_yearly?: number
          max_projects?: number
          max_prompts_per_project?: number
          features?: Record<string, boolean | string | number> // Not any!
          is_active?: boolean
          sort_order?: number
        }
      }
    }
    Functions: {
      get_user_active_plan: {
        Args: { user_uuid: string }
        Returns: {
          plan_id: string
          plan_name: string
          display_name: string
          max_projects: number
          max_prompts_per_project: number
          features: Record<string, boolean | string | number> // Not any!
          status: string
          expires_at: string | null
        }[]
      }
    }
  }
}
```

### Component Props Patterns
```typescript
// ✅ Proper component prop typing
interface PlanUpgradeModalProps {
  isOpen: boolean
  onClose: () => void
  currentPlan?: {
    plan_id: string
    plan_name: string
    display_name: string
    max_projects: number
    max_prompts_per_project: number
    features: Record<string, boolean | string | number> // Not any!
    status: string
    expires_at: string | null
  }
  availablePlans: PlanType[]
}

// ✅ Generic type constraints
interface ApiResponse<T = unknown> {
  data: T
  error: string | null
  status: 'success' | 'error' | 'loading'
}

// ✅ Union types for specific values
type PlanStatus = 'active' | 'cancelled' | 'expired' | 'pending'
type BillingCycle = 'monthly' | 'yearly' | 'lifetime'
```

### Hook Return Types
```typescript
// ✅ Proper hook typing
export function useUserLimits(): {
  data: UserLimits | undefined
  isLoading: boolean
  error: Error | null
  refetch: () => void
} {
  return useQuery({
    queryKey: ['user-limits'],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('check_user_limits', {
        user_uuid: user?.id
      })
      
      if (error) throw error
      return data as UserLimits
    }
  })
}

// ✅ Generic hook patterns
export function useApiCall<T>(
  endpoint: string,
  options?: RequestInit
): ApiResponse<T> {
  // Implementation with proper typing
}
```

## Error Resolution Patterns

### Pattern 1: `any` Type Replacement
```typescript
// ❌ Before
interface BadInterface {
  metadata: any
  config: any
  features: Record<string, any>
}

// ✅ After
interface GoodInterface {
  metadata: Record<string, string | number | boolean>
  config: {
    enabled: boolean
    maxItems: number
    theme: 'light' | 'dark'
  }
  features: Record<string, boolean | string | number>
}
```

### Pattern 2: Unused Variable Cleanup
```typescript
// ❌ Before
import { UnusedComponent, UsedComponent } from './components'
import { unusedFunction, usedFunction } from './utils'

function MyComponent() {
  const unusedVariable = 'not used'
  const usedVariable = 'used in render'
  
  return <UsedComponent value={usedVariable} />
}

// ✅ After
import { UsedComponent } from './components'
import { usedFunction } from './utils'

function MyComponent() {
  const usedVariable = 'used in render'
  
  return <UsedComponent value={usedVariable} />
}
```

### Pattern 3: Proper Type Assertions
```typescript
// ❌ Avoid type assertions when possible
const data = response as any

// ✅ Use proper typing
interface ExpectedResponse {
  id: string
  name: string
  features: Record<string, boolean | string | number>
}

const data = response as ExpectedResponse

// ✅ Even better: Runtime validation
function isValidResponse(obj: unknown): obj is ExpectedResponse {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'name' in obj &&
    'features' in obj
  )
}

if (isValidResponse(response)) {
  // TypeScript knows response is ExpectedResponse here
  const data = response
}
```

## Automation Rules

### Auto-Fix Patterns
1. **Replace `any` with Union Types**
   - `Record<string, any>` → `Record<string, boolean | string | number>`
   - `any[]` → `unknown[]` or specific type array
   - Function parameters: `(param: any)` → `(param: unknown)` or specific type

2. **Remove Unused Imports**
   - Scan for imports not referenced in file
   - Remove entire import line if no exports used
   - Update import list if partial usage

3. **Clean Unused Variables**
   - Remove variables not referenced after declaration
   - Remove function parameters not used in body
   - Remove type definitions not referenced

### IDE Integration
```json
// VS Code settings.json
{
  "typescript.preferences.noSemicolons": "off",
  "typescript.preferences.quoteStyle": "single",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "editor.codeActionsOnSave": {
    "source.organizeImports": true,
    "source.fixAll.eslint": true
  }
}
```

## Project-Specific Patterns

### PromptFlow Main App (React 19 + Next.js 15)
- Use new React 19 features cautiously
- Maintain compatibility with Next.js 15 patterns
- Leverage new TypeScript 5+ features

### Admin Panel (React 18 + Next.js 14)
- Stick to stable React 18 patterns
- Use proven TypeScript patterns
- Maintain backward compatibility

### Shared Patterns
- Database types must be identical
- API response types should be shared
- Common utility types in shared location

## Validation and Security Patterns

### Input Validation Types
```typescript
// ✅ Structured validation results
interface ValidationResult {
  isValid: boolean
  error?: string
  sanitizedValue?: string
}

// ✅ Type-safe validation functions
function validateProjectName(name: string): ValidationResult {
  const sanitized = sanitizeProjectName(name)

  if (!sanitized) {
    return { isValid: false, error: 'Proje adı boş olamaz' }
  }

  if (sanitized.length < PROJECT_NAME_RULES.minLength) {
    return {
      isValid: false,
      error: `Proje adı en az ${PROJECT_NAME_RULES.minLength} karakter olmalıdır`
    }
  }

  return { isValid: true, sanitizedValue: sanitized }
}

// ✅ Async validation with proper typing
async function validateProjectNameUnique(
  name: string,
  currentProjectId: string,
  projects: Array<{ id: string; name: string }>
): Promise<ValidationResult> {
  const basicValidation = validateProjectName(name)
  if (!basicValidation.isValid) {
    return basicValidation
  }

  const isDuplicate = projects.some(project =>
    project.id !== currentProjectId &&
    isSameProjectName(project.name, basicValidation.sanitizedValue!)
  )

  if (isDuplicate) {
    return { isValid: false, error: 'Bu isimde bir proje zaten mevcut' }
  }

  return { isValid: true, sanitizedValue: basicValidation.sanitizedValue }
}
```

### Error Handling Patterns
```typescript
// ❌ Using any for errors
catch (error: any) {
  console.error(error.message)
}

// ✅ Proper error handling
catch (error) {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error'
  console.error(errorMessage)
}

// ✅ Type-safe error formatting
function formatValidationError(error: string): string {
  const errorMap: Record<string, string> = {
    'unique_violation': 'Bu isimde bir proje zaten mevcut',
    'check_violation': 'Proje adı geçersiz karakterler içeriyor',
    'not_null_violation': 'Proje adı boş olamaz',
    'RateLimitExceeded': 'Çok fazla güncelleme isteği. Lütfen bekleyin.'
  }

  return errorMap[error] || error
}
```

### Debounced Function Types
```typescript
// ✅ Type-safe debounced validator
function createAdvancedDebouncedValidator(
  projects: Array<{ id: string; name: string }>,
  currentProjectId: string,
  delay: number = 300
) {
  let timeoutId: NodeJS.Timeout

  return (value: string, callback: (result: ValidationResult) => void) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(async () => {
      const result = await validateProjectNameUnique(value, currentProjectId, projects)
      callback(result)
    }, delay)
  }
}
```

### React Component Props Types
```typescript
// ✅ Comprehensive component props interface
interface ProjectNameEditorProps {
  projectId: string
  currentName: string
  onNameUpdated?: (newName: string) => void
  className?: string
  disabled?: boolean
}

// ✅ Memoized component with proper typing
const ProjectNameEditor = memo(function ProjectNameEditor({
  projectId,
  currentName,
  onNameUpdated,
  className,
  disabled = false
}: ProjectNameEditorProps) {
  // Component implementation
})
```

## Update Requirements
- Update patterns when TypeScript version changes
- Document new error patterns as they emerge
- Maintain compatibility with both app versions
- Keep automation rules current with tooling updates
- Add new type patterns for emerging features
- Document validation patterns for user input security
- Maintain type safety for async operations
