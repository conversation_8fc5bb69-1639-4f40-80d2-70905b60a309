'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { AuthGuard } from '@/components/auth-guard'
import { useUser, useSignOut } from '@/hooks/use-auth'
import { useProjects } from '@/hooks/use-projects'
import { useUserActivePlan, usePlanTypes, useChangePlan } from '@/hooks/use-plans'
import { supabase } from '@/lib/supabase'
import { ArrowLeft, User, Mail, Calendar, Shield, Key, LogOut, AlertCircle, CheckCircle, Crown, Star } from 'lucide-react'
import Link from 'next/link'

export default function ProfilePage() {
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [isChangingPassword, setIsChangingPassword] = useState(false)
  const [passwordMessage, setPasswordMessage] = useState('')
  const [messageType, setMessageType] = useState<'success' | 'error' | null>(null)

  const { data: user } = useUser()
  const { data: projects = [] } = useProjects()
  const { data: activePlan } = useUserActivePlan()
  const { data: planTypes = [] } = usePlanTypes()
  const changePlanMutation = useChangePlan()
  const signOutMutation = useSignOut()

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault()
    setPasswordMessage('')
    setMessageType(null)

    if (newPassword !== confirmPassword) {
      setPasswordMessage('Yeni şifreler eşleşmiyor!')
      setMessageType('error')
      return
    }

    if (newPassword.length < 6) {
      setPasswordMessage('Yeni şifre en az 6 karakter olmalı!')
      setMessageType('error')
      return
    }

    setIsChangingPassword(true)

    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      })

      if (error) {
        throw error
      }

      setPasswordMessage('Şifre başarıyla güncellendi!')
      setMessageType('success')
      setNewPassword('')
      setConfirmPassword('')
    } catch (error) {
      setPasswordMessage(error instanceof Error ? error.message : 'Şifre güncellenirken bir hata oluştu')
      setMessageType('error')
    } finally {
      setIsChangingPassword(false)
    }
  }

  const handleSignOut = () => {
    signOutMutation.mutate()
  }

  const handlePlanChange = async (planName: string) => {
    try {
      // Validation checks before plan change
      const targetPlan = planTypes.find(p => p.name === planName)
      if (!targetPlan) {
        throw new Error('Geçersiz plan seçimi')
      }

      // Check if downgrading and validate current usage
      if (activePlan && targetPlan.max_projects < activePlan.max_projects) {
        if (projects.length > targetPlan.max_projects) {
          throw new Error(
            `Bu plana geçmek için önce proje sayınızı ${targetPlan.max_projects}'e düşürmeniz gerekiyor. ` +
            `Şu anda ${projects.length} projeniz var.`
          )
        }
      }

      // Confirm downgrade if applicable
      if (activePlan && targetPlan.price_monthly < activePlan.price_monthly) {
        const confirmed = window.confirm(
          `${targetPlan.display_name} planına geçmek istediğinizden emin misiniz? ` +
          'Bu işlem bazı özelliklerinizi kısıtlayabilir.'
        )
        if (!confirmed) return
      }

      await changePlanMutation.mutateAsync({
        planName,
        billingCycle: 'monthly'
      })

      setPasswordMessage('Plan başarıyla değiştirildi!')
      setMessageType('success')

      // Clear message after 5 seconds
      setTimeout(() => {
        setPasswordMessage('')
        setMessageType(null)
      }, 5000)

    } catch (error) {
      setPasswordMessage(error instanceof Error ? error.message : 'Plan değiştirilemedi')
      setMessageType('error')

      // Clear error message after 8 seconds
      setTimeout(() => {
        setPasswordMessage('')
        setMessageType(null)
      }, 8000)
    }
  }

  const getPlanIcon = (planName: string) => {
    switch (planName) {
      case 'free': return <User className="h-5 w-5" />
      case 'professional': return <Crown className="h-5 w-5" />
      case 'enterprise': return <Star className="h-5 w-5" />
      default: return <Shield className="h-5 w-5" />
    }
  }

  const getPlanColor = (planName: string) => {
    switch (planName) {
      case 'free': return 'bg-gray-100 text-gray-800'
      case 'professional': return 'bg-blue-100 text-blue-800'
      case 'enterprise': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (!user) {
    return null
  }

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <Link href="/">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Ana Sayfaya Dön
                </Button>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Profil Ayarları</h1>
                <p className="text-gray-600">Hesap bilgilerinizi yönetin</p>
              </div>
            </div>
            <Button
              variant="outline"
              onClick={handleSignOut}
              className="text-red-600 hover:text-red-700"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Çıkış Yap
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Profil Bilgileri */}
            <div className="lg:col-span-2 space-y-6">
              {/* Temel Bilgiler */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Temel Bilgiler
                  </CardTitle>
                  <CardDescription>
                    Hesap bilgilerinizi görüntüleyin
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="email">E-posta Adresi</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <Mail className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-900">{user.email}</span>
                      </div>
                    </div>
                    <div>
                      <Label>Kayıt Tarihi</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-900">
                          {new Date(user.created_at).toLocaleDateString('tr-TR')}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <Label>Hesap Durumu</Label>
                    <div className="flex items-center gap-2 mt-1">
                      <Shield className="h-4 w-4 text-green-500" />
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Aktif
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Şifre Değiştirme */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Key className="h-5 w-5" />
                    Şifre Değiştir
                  </CardTitle>
                  <CardDescription>
                    Güvenliğiniz için düzenli olarak şifrenizi değiştirin
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handlePasswordChange} className="space-y-4">
                    <div>
                      <Label htmlFor="newPassword">Yeni Şifre</Label>
                      <Input
                        id="newPassword"
                        type="password"
                        placeholder="Yeni şifrenizi girin"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        disabled={isChangingPassword}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="confirmPassword">Yeni Şifre (Tekrar)</Label>
                      <Input
                        id="confirmPassword"
                        type="password"
                        placeholder="Yeni şifrenizi tekrar girin"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        disabled={isChangingPassword}
                        required
                      />
                    </div>

                    {passwordMessage && (
                      <div className={`flex items-center gap-2 text-sm p-3 rounded-md ${
                        messageType === 'success' 
                          ? 'bg-green-50 text-green-700' 
                          : 'bg-red-50 text-red-700'
                      }`}>
                        {messageType === 'success' ? (
                          <CheckCircle className="h-4 w-4" />
                        ) : (
                          <AlertCircle className="h-4 w-4" />
                        )}
                        {passwordMessage}
                      </div>
                    )}

                    <Button
                      type="submit"
                      disabled={isChangingPassword || !newPassword || !confirmPassword}
                      className="w-full"
                    >
                      {isChangingPassword ? 'Güncelleniyor...' : 'Şifreyi Güncelle'}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Yan Panel - İstatistikler */}
            <div className="space-y-6">
              {/* Hesap İstatistikleri */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Hesap İstatistikleri</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Toplam Proje</span>
                    <Badge variant="outline">{projects.length}</Badge>
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Hesap Tipi</span>
                    <Badge variant="secondary" className={getPlanColor(activePlan?.plan_name || 'free')}>
                      {activePlan?.display_name || 'Ücretsiz'}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Son Giriş</span>
                    <span className="text-sm text-gray-900">
                      {new Date(user.last_sign_in_at || user.created_at).toLocaleDateString('tr-TR')}
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Plan Yönetimi */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Crown className="h-5 w-5 text-blue-600" />
                    Plan Yönetimi
                  </CardTitle>
                  <CardDescription>
                    Mevcut planınızı görüntüleyin ve değiştirin
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Mevcut Plan */}
                  <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getPlanIcon(activePlan?.plan_name || 'free')}
                        <span className="font-medium text-blue-900">
                          {activePlan?.display_name || 'Ücretsiz Plan'}
                        </span>
                      </div>
                      <Badge className={getPlanColor(activePlan?.plan_name || 'free')}>
                        Aktif
                      </Badge>
                    </div>
                    <div className="text-sm text-blue-700 space-y-1">
                      <div>• {activePlan?.max_projects || 5} proje limiti</div>
                      <div>• {activePlan?.max_prompts_per_project || 100} prompt/proje limiti</div>
                      {activePlan?.expires_at && (
                        <div>• Bitiş: {new Date(activePlan.expires_at).toLocaleDateString('tr-TR')}</div>
                      )}
                    </div>
                  </div>

                  {/* Plan Seçenekleri */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Mevcut Planlar</Label>
                    {planTypes.map((plan) => (
                      <div
                        key={plan.id}
                        className={`p-3 border rounded-lg transition-all ${
                          plan.name === activePlan?.plan_name
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            {getPlanIcon(plan.name)}
                            <div>
                              <div className="font-medium text-sm">{plan.display_name}</div>
                              <div className="text-xs text-gray-600">
                                {plan.max_projects === -1 ? 'Sınırsız' : plan.max_projects} proje, {' '}
                                {plan.max_prompts_per_project === -1 ? 'Sınırsız' : plan.max_prompts_per_project} prompt/proje
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">
                              {plan.price_monthly === 0 ? 'Ücretsiz' : `₺${plan.price_monthly}/ay`}
                            </span>
                            {plan.name !== activePlan?.plan_name && (
                              <Button
                                size="sm"
                                variant={plan.name === 'free' ? 'outline' : 'default'}
                                onClick={() => handlePlanChange(plan.name)}
                                disabled={changePlanMutation.isPending}
                                className="ml-2"
                              >
                                {changePlanMutation.isPending ? 'Değiştiriliyor...' : 'Seç'}
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Plan Değişiklik Mesajı */}
                  {passwordMessage && messageType && (
                    <div className={`p-3 rounded-lg flex items-center gap-2 ${
                      messageType === 'success'
                        ? 'bg-green-50 text-green-800 border border-green-200'
                        : 'bg-red-50 text-red-800 border border-red-200'
                    }`}>
                      {messageType === 'success' ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <AlertCircle className="h-4 w-4" />
                      )}
                      <span className="text-sm">{passwordMessage}</span>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Güvenlik Önerileri */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Güvenlik Önerileri</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-start gap-3">
                    <Shield className="h-4 w-4 text-blue-500 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Güçlü Şifre</p>
                      <p className="text-xs text-gray-600">En az 8 karakter, büyük/küçük harf ve rakam kullanın</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Shield className="h-4 w-4 text-blue-500 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Düzenli Güncelleme</p>
                      <p className="text-xs text-gray-600">Şifrenizi 3-6 ayda bir değiştirin</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </AuthGuard>
  )
} 