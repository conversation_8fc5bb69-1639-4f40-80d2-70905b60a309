'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

import { Folder, FolderOpen, ChevronRight, Home, Plus } from 'lucide-react'
import { cn } from '@/lib/utils'
import {
  cleanFolderPath,
  isValidFolderPath,
  getFolderHierarchy,
  getFolderName,
  getFolderSuggestions
} from '@/lib/hashtag-utils'

interface CategorySelectorProps {
  category: string | null
  onCategoryChange: (category: string | null) => void
  suggestions?: string[]
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function CategorySelector({
  category,
  onCategoryChange,
  suggestions = [],
  placeholder = "Klasör seçin... (örn: /frontend, /admin/users)",
  className,
  disabled = false
}: CategorySelectorProps) {
  const [inputValue, setInputValue] = useState('')
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1)
  const [isEditing, setIsEditing] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const suggestionsRef = useRef<HTMLDivElement>(null)

  // Filter suggestions based on input
  const filteredSuggestions = getFolderSuggestions(inputValue, suggestions, 8)

  // Get folder hierarchy for breadcrumb display
  const hierarchy = category ? getFolderHierarchy(category) : []

  // Set category
  const setCategory = (newCategory: string) => {
    const cleaned = cleanFolderPath(newCategory)
    if (!cleaned || !isValidFolderPath(cleaned)) return
    
    onCategoryChange(cleaned === '/' ? null : cleaned)
    setInputValue('')
    setShowSuggestions(false)
    setSelectedSuggestionIndex(-1)
    setIsEditing(false)
  }

  // Clear category
  const clearCategory = () => {
    onCategoryChange(null)
    setIsEditing(false)
  }

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setInputValue(value)
    setShowSuggestions(value.length > 0)
    setSelectedSuggestionIndex(-1)
  }

  // Handle key down
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      if (selectedSuggestionIndex >= 0 && filteredSuggestions[selectedSuggestionIndex]) {
        setCategory(filteredSuggestions[selectedSuggestionIndex])
      } else if (inputValue.trim()) {
        setCategory(inputValue.trim())
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault()
      setSelectedSuggestionIndex(prev => 
        prev < filteredSuggestions.length - 1 ? prev + 1 : prev
      )
    } else if (e.key === 'ArrowUp') {
      e.preventDefault()
      setSelectedSuggestionIndex(prev => prev > 0 ? prev - 1 : -1)
    } else if (e.key === 'Escape') {
      setShowSuggestions(false)
      setSelectedSuggestionIndex(-1)
      setIsEditing(false)
      setInputValue('')
    }
  }

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: string) => {
    setCategory(suggestion)
    inputRef.current?.focus()
  }

  // Handle breadcrumb click
  const handleBreadcrumbClick = (path: string) => {
    if (path === '/') {
      clearCategory()
    } else {
      onCategoryChange(path)
    }
  }

  // Start editing
  const startEditing = () => {
    setIsEditing(true)
    setInputValue(category || '')
    setTimeout(() => inputRef.current?.focus(), 0)
  }

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current && 
        !suggestionsRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div className={cn("space-y-2", className)}>
      {/* Category Display */}
      {category && !isEditing && (
        <div className="flex items-center gap-1 p-2 bg-gray-50 rounded-md border">
          <div className="flex items-center gap-1 flex-1 min-w-0">
            {hierarchy.map((path, index) => (
              <div key={path} className="flex items-center gap-1">
                {index > 0 && <ChevronRight className="w-3 h-3 text-gray-400" />}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-auto p-1 text-xs hover:bg-gray-200"
                  onClick={() => handleBreadcrumbClick(path)}
                >
                  {index === 0 ? (
                    <Home className="w-3 h-3" />
                  ) : (
                    <>
                      <Folder className="w-3 h-3" />
                      <span className="ml-1">{getFolderName(path)}</span>
                    </>
                  )}
                </Button>
              </div>
            ))}
          </div>
          {!disabled && (
            <div className="flex items-center gap-1">
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-auto p-1"
                onClick={startEditing}
              >
                Edit
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-auto p-1"
                onClick={clearCategory}
              >
                Clear
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Input Field */}
      {(!category || isEditing) && (
        <div className="relative">
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Folder className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onFocus={() => setShowSuggestions(inputValue.length > 0)}
                placeholder={placeholder}
                disabled={disabled}
                className="pl-10"
              />
            </div>
            {inputValue.trim() && (
              <Button
                type="button"
                size="sm"
                onClick={() => setCategory(inputValue.trim())}
                disabled={disabled}
                className="shrink-0"
              >
                <Plus className="w-4 h-4" />
              </Button>
            )}
          </div>

          {/* Suggestions Dropdown */}
          {showSuggestions && filteredSuggestions.length > 0 && (
            <div
              ref={suggestionsRef}
              className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-40 overflow-y-auto"
            >
              {filteredSuggestions.map((suggestion, index) => (
                <button
                  key={suggestion}
                  type="button"
                  className={cn(
                    "w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2",
                    selectedSuggestionIndex === index && "bg-blue-50"
                  )}
                  onClick={() => handleSuggestionClick(suggestion)}
                >
                  <FolderOpen className="w-3 h-3 text-gray-400" />
                  {suggestion}
                </button>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Helper Text */}
      <div className="text-xs text-gray-500">
        Klasörler için eğik çizgi kullanın (örn: /frontend/components)
      </div>
    </div>
  )
}
