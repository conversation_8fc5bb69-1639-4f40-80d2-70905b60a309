---
type: "fixes"
role: "build_troubleshooting_guide"
dependencies:
  - "core/PROJECT_CORE.md"
  - "frontend/MAIN_APP.md"
  - "frontend/ADMIN_PANEL.md"
auto_update_from:
  - "frontend/MAIN_APP.md"
  - "frontend/ADMIN_PANEL.md"
solution_history:
  - date: "2025-01-29"
    issue: "TypeScript any type violations"
    solution: "Replaced with specific union types Record<string, boolean | string | number>"
    status: "resolved"
  - date: "2025-01-29"
    issue: "ESLint escape character errors"
    solution: "Replaced apostrophes with &apos; in Turkish text"
    status: "resolved"
  - date: "2025-01-29"
    issue: "Next.js configuration deprecation"
    solution: "Removed optimizeFonts from next.config.ts"
    status: "resolved"
last_updated: "2025-01-29"
---

# PromptFlow Build Error Resolution System

## Systematic Build Error Resolution Process

### Phase 1: Error Detection & Analysis
```bash
# 1. Run build command to identify errors
npm run build

# 2. Categorize errors by type:
# - TypeScript type errors
# - ESLint violations
# - Import/export issues
# - Configuration problems
# - Missing dependencies
# - Syntax errors
```

### Phase 2: TypeScript Error Resolution

#### Pattern: `any` Type Violations
**Detection**: `Unexpected any. Specify a different type.`
**Solution Strategy**:
```typescript
// ❌ Problematic
features: Record<string, any>

// ✅ Fixed
features: Record<string, boolean | string | number>
```

**Automation Trigger**: When encountering `@typescript-eslint/no-explicit-any`
**Files Commonly Affected**:
- `src/lib/supabase.ts` (Database type definitions)
- `src/lib/plan-limits.ts` (Plan feature definitions)
- `src/components/plan-upgrade-modal.tsx` (Component props)

#### Pattern: Missing Type Definitions
**Detection**: `Property 'X' does not exist on type 'Y'`
**Solution Strategy**:
1. Check if type is imported correctly
2. Verify interface/type definitions
3. Add missing properties to type definitions
4. Use type assertions only as last resort

### Phase 3: ESLint Error Resolution

#### Pattern: Unescaped Characters in JSX
**Detection**: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`
**Solution Strategy**:
```typescript
// ❌ Problematic
<span>Prompt'lar</span>

// ✅ Fixed
<span>Prompt&apos;lar</span>
```

**Automation Trigger**: When encountering Turkish text with apostrophes
**Common Locations**:
- User interface text
- Plan descriptions
- Error messages
- Help text

#### Pattern: Unused Variables/Imports
**Detection**: `'X' is defined but never used.`
**Solution Strategy**:
1. Remove unused imports
2. Remove unused variables
3. Remove unused functions
4. Update interface definitions

**Common Cleanup Targets**:
```typescript
// Remove unused imports
import { UnusedComponent } from './unused'

// Remove unused variables
const unusedVariable = someValue

// Remove unused function parameters
function handler(used: string, unused: string) // Remove 'unused'
```

### Phase 4: Next.js Configuration Issues

#### Pattern: Deprecated Configuration Options
**Detection**: `Invalid next.config.ts options detected`
**Solution Strategy**:
```typescript
// ❌ Deprecated in Next.js 15
const nextConfig = {
  optimizeFonts: true, // Remove this
}

// ✅ Current approach
const nextConfig = {
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react'],
  }
}
```

**Version Compatibility Checks**:
- Next.js 14 → 15: Remove `optimizeFonts`
- React 18 → 19: Update component patterns
- TailwindCSS 3 → 4: Update configuration syntax

### Phase 5: Dependency Management

#### Pattern: Missing Dependencies
**Detection**: `Cannot find module 'X'`
**Solution Strategy**:
```bash
# 1. Install missing dependency
npm install missing-package

# 2. Check for peer dependencies
npm ls --depth=0

# 3. Resolve version conflicts
npm audit fix

# 4. Clear cache if needed
rm -rf node_modules package-lock.json
npm install
```

**Critical Dependencies for PromptFlow**:
- `critters` (CSS optimization)
- `@types/*` packages for TypeScript
- Peer dependencies for UI libraries

### Phase 6: Build Verification

#### Verification Checklist
```bash
# 1. Clean build
npm run build

# 2. Type checking
npm run type-check  # If available

# 3. Linting
npm run lint

# 4. Development server test
npm run dev

# 5. Production build test
npm run start
```

#### Success Criteria
- ✅ Build completes without errors
- ✅ No TypeScript type errors
- ✅ No ESLint violations (warnings acceptable)
- ✅ Development server starts successfully
- ✅ All pages load without runtime errors

## Automation Patterns

### Auto-Fix Triggers
1. **TypeScript `any` Detection**: Automatically suggest union types
2. **Unescaped Apostrophes**: Auto-replace with `&apos;`
3. **Unused Imports**: Auto-remove with IDE integration
4. **Deprecated Config**: Auto-update Next.js configuration

### Prevention Strategies
1. **Pre-commit Hooks**: Run type checking and linting
2. **CI/CD Integration**: Automated build verification
3. **IDE Configuration**: Real-time error detection
4. **Dependency Updates**: Regular security audits

## Error Pattern Database

### Common Error Signatures
```
❌ "Unexpected any. Specify a different type."
→ Replace with specific union types

❌ "'X' is defined but never used."
→ Remove unused imports/variables

❌ "' can be escaped with &apos;"
→ Replace apostrophes in Turkish text

❌ "Invalid next.config.ts options detected"
→ Update deprecated configuration options

❌ "Cannot find module 'critters'"
→ Install missing build dependencies
```

### Project-Specific Patterns
- **PromptFlow Main**: React 19 + Next.js 15 compatibility
- **Admin Panel**: React 18 + Next.js 14 compatibility
- **Dual App**: Maintain separate dependency versions

## Update Requirements
- Document new error patterns as they occur
- Update automation triggers for new Next.js versions
- Maintain compatibility matrices for both applications
- Keep dependency lists current with security updates
- Add new TypeScript patterns as language evolves
