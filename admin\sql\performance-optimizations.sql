-- Performance optimization SQL for admin panel

-- 1. Create optimized view for admin users with joined data
CREATE OR REPLACE VIEW admin_users_view AS
SELECT 
  au.id,
  au.user_id,
  au.role,
  au.permissions,
  au.is_active,
  au.created_at,
  au.updated_at,
  u.email,
  u.last_sign_in_at,
  u.email_confirmed_at,
  -- Count related data
  COALESCE(p.projects_count, 0) as projects_count,
  COALESCE(pr.prompts_count, 0) as prompts_count
FROM admin_users au
LEFT JOIN auth.users u ON au.user_id = u.id
LEFT JOIN (
  SELECT user_id, COUNT(*) as projects_count 
  FROM projects 
  GROUP BY user_id
) p ON au.user_id = p.user_id
LEFT JOIN (
  SELECT user_id, COUNT(*) as prompts_count 
  FROM prompts 
  GROUP BY user_id
) pr ON au.user_id = pr.user_id;

-- 2. Create RPC function for consolidated dashboard stats
CREATE OR REPLACE FUNCTION get_admin_dashboard_stats()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
  total_users_count INTEGER := 0;
  total_projects_count INTEGER := 0;
  total_prompts_count INTEGER := 0;
  total_contexts_count INTEGER := 0;
  active_users_count INTEGER := 0;
  user_growth_rate NUMERIC := 0;
  project_growth_rate NUMERIC := 0;
  avg_prompts_per_project NUMERIC := 0;
  avg_context_usage NUMERIC := 0;
  temp_calc NUMERIC := 0;
BEGIN
  -- Get total users (fallback to auth.users if admin_users doesn't exist)
  BEGIN
    SELECT COUNT(*) INTO total_users_count FROM admin_users WHERE is_active = true;
  EXCEPTION WHEN undefined_table THEN
    SELECT COUNT(*) INTO total_users_count FROM auth.users;
  END;

  -- Get total projects
  BEGIN
    SELECT COUNT(*) INTO total_projects_count FROM projects;
  EXCEPTION WHEN undefined_table THEN
    total_projects_count := 0;
  END;

  -- Get total prompts
  BEGIN
    SELECT COUNT(*) INTO total_prompts_count FROM prompts;
  EXCEPTION WHEN undefined_table THEN
    total_prompts_count := 0;
  END;

  -- Get total contexts
  BEGIN
    SELECT COUNT(*) INTO total_contexts_count FROM contexts WHERE status = 'active';
  EXCEPTION WHEN undefined_table THEN
    total_contexts_count := 0;
  END;

  -- Get active users (fallback if user_sessions doesn't exist)
  BEGIN
    SELECT COUNT(DISTINCT user_id) INTO active_users_count
    FROM user_sessions
    WHERE is_active = true
    AND last_activity_at > NOW() - INTERVAL '24 hours';
  EXCEPTION WHEN undefined_table THEN
    -- Fallback to recent auth users
    SELECT COUNT(*) INTO active_users_count
    FROM auth.users
    WHERE last_sign_in_at > NOW() - INTERVAL '24 hours';
  END;

  -- Calculate user growth rate with proper type casting
  BEGIN
    WITH user_counts AS (
      SELECT
        COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '30 days') as new_users,
        COUNT(*) FILTER (WHERE created_at <= NOW() - INTERVAL '30 days') as old_users
      FROM admin_users
      WHERE is_active = true
    )
    SELECT
      CASE
        WHEN old_users > 0 THEN ROUND((new_users::NUMERIC / old_users::NUMERIC * 100), 2)
        ELSE 0
      END
    INTO user_growth_rate
    FROM user_counts;
  EXCEPTION WHEN undefined_table THEN
    WITH user_counts AS (
      SELECT
        COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '30 days') as new_users,
        COUNT(*) FILTER (WHERE created_at <= NOW() - INTERVAL '30 days') as old_users
      FROM auth.users
    )
    SELECT
      CASE
        WHEN old_users > 0 THEN ROUND((new_users::NUMERIC / old_users::NUMERIC * 100), 2)
        ELSE 0
      END
    INTO user_growth_rate
    FROM user_counts;
  END;

  -- Calculate project growth rate with proper type casting
  BEGIN
    WITH project_counts AS (
      SELECT
        COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '30 days') as new_projects,
        COUNT(*) FILTER (WHERE created_at <= NOW() - INTERVAL '30 days') as old_projects
      FROM projects
    )
    SELECT
      CASE
        WHEN old_projects > 0 THEN ROUND((new_projects::NUMERIC / old_projects::NUMERIC * 100), 2)
        ELSE 0
      END
    INTO project_growth_rate
    FROM project_counts;
  EXCEPTION WHEN undefined_table THEN
    project_growth_rate := 0;
  END;

  -- Calculate average prompts per project with proper type casting
  BEGIN
    WITH prompt_counts AS (
      SELECT COUNT(*) as prompt_count
      FROM prompts
      GROUP BY project_id
    )
    SELECT COALESCE(ROUND(AVG(prompt_count::NUMERIC), 2), 0)
    INTO avg_prompts_per_project
    FROM prompt_counts;
  EXCEPTION WHEN undefined_table THEN
    avg_prompts_per_project := 0;
  END;

  -- Calculate average context usage per project with proper type casting
  BEGIN
    SELECT COALESCE(
      ROUND(
        AVG(
          CASE
            WHEN context_text IS NOT NULL AND context_text != '' THEN 1::NUMERIC
            ELSE 0::NUMERIC
          END
        ) * 100,
        2
      ),
      0
    )
    INTO avg_context_usage
    FROM projects;
  EXCEPTION WHEN undefined_table THEN
    avg_context_usage := 0;
  END;

  -- Build final result
  SELECT json_build_object(
    'totalUsers', total_users_count,
    'totalProjects', total_projects_count,
    'totalPrompts', total_prompts_count,
    'totalContexts', total_contexts_count,
    'activeUsers', active_users_count,
    'userGrowth', user_growth_rate,
    'projectGrowth', project_growth_rate,
    'avgPromptsPerProject', avg_prompts_per_project,
    'avgContextUsage', avg_context_usage
  ) INTO result;

  RETURN result;
END;
$$;

-- 3. Create RPC function for user stats
CREATE OR REPLACE FUNCTION get_user_stats()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'totalUsers', (SELECT COUNT(*) FROM admin_users WHERE is_active = true),
    'activeUsers', (
      SELECT COUNT(DISTINCT user_id) 
      FROM user_sessions 
      WHERE is_active = true 
      AND last_activity_at > NOW() - INTERVAL '24 hours'
    ),
    'adminUsers', (SELECT COUNT(*) FROM admin_users WHERE is_active = true AND role = 'admin'),
    'newUsersThisMonth', (
      SELECT COUNT(*) 
      FROM admin_users 
      WHERE is_active = true 
      AND created_at > DATE_TRUNC('month', NOW())
    )
  ) INTO result;
  
  RETURN result;
END;
$$;

-- 4. Create indexes for better query performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_admin_users_user_id_active 
ON admin_users(user_id, is_active) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_user_id_created 
ON projects(user_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_prompts_project_id_created 
ON prompts(project_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_prompts_user_id_created 
ON prompts(user_id, created_at DESC);

-- Enhanced admin_logs indexes for performance optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_admin_logs_created_desc
ON admin_logs(created_at DESC);

-- Composite index for admin_id and created_at (most common query pattern)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_admin_logs_admin_created
ON admin_logs(admin_id, created_at DESC);

-- Index for resource_type filtering (for different admin action types)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_admin_logs_resource_type
ON admin_logs(resource_type, created_at DESC);

-- Composite index for admin_id and resource_type (user-specific resource queries)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_admin_logs_admin_resource
ON admin_logs(admin_id, resource_type, created_at DESC);

-- Partial index for recent logs (last 30 days) - frequently accessed data
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_admin_logs_recent
ON admin_logs(created_at DESC, admin_id)
WHERE created_at > (NOW() - INTERVAL '30 days');

-- Index for action type filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_admin_logs_action_created
ON admin_logs(action, created_at DESC);

-- Covering index for common admin log queries (includes frequently selected columns)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_admin_logs_covering
ON admin_logs(admin_id, created_at DESC)
INCLUDE (action, resource_type, resource_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_sessions_active_activity 
ON user_sessions(is_active, last_activity_at) WHERE is_active = true;

-- 5. Create materialized view for dashboard stats (optional, for very high traffic)
CREATE MATERIALIZED VIEW IF NOT EXISTS dashboard_stats_cache AS
SELECT 
  (SELECT COUNT(*) FROM admin_users WHERE is_active = true) as total_users,
  (SELECT COUNT(*) FROM projects) as total_projects,
  (SELECT COUNT(*) FROM prompts) as total_prompts,
  (SELECT COUNT(DISTINCT user_id) FROM user_sessions WHERE is_active = true AND last_activity_at > NOW() - INTERVAL '24 hours') as active_users,
  NOW() as last_updated;

-- Create function to refresh the materialized view
CREATE OR REPLACE FUNCTION refresh_dashboard_stats_cache()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  REFRESH MATERIALIZED VIEW dashboard_stats_cache;
END;
$$;

-- 6. Grant necessary permissions
GRANT SELECT ON admin_users_view TO authenticated;
GRANT EXECUTE ON FUNCTION get_admin_dashboard_stats() TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_stats() TO authenticated;
GRANT SELECT ON dashboard_stats_cache TO authenticated;
GRANT EXECUTE ON FUNCTION refresh_dashboard_stats_cache() TO authenticated;
