'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabaseBrowser as supabase } from '@/lib/supabase-browser'
import { Database } from '@/lib/supabase'
import { canCreateProject, updateUsageStats, PLAN_LIMIT_MESSAGES } from '@/lib/plan-limits'
import {
  validateProjectName,
  formatValidationError
} from '@/lib/project-validation'
import { checkRateLimit, RateLimitError } from '@/lib/rate-limiter'

type Project = Database['public']['Tables']['projects']['Row']
type ProjectInsert = Database['public']['Tables']['projects']['Insert']
type ProjectUpdate = Database['public']['Tables']['projects']['Update']

// Projeleri getir
export function useProjects() {
  return useQuery({
    queryKey: ['projects'],
    queryFn: async (): Promise<Project[]> => {
      console.log(`📁 [USE_PROJECTS] Fetching projects...`)

      try {
        // Check if we have a session first
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        console.log(`📁 [USE_PROJECTS] Session check:`, {
          hasSession: !!session,
          sessionError: sessionError?.message,
          userId: session?.user?.id
        })

        const { data, error } = await supabase
          .from('projects')
          .select('*')
          .order('created_at', { ascending: false })

        if (error) {
          console.error(`❌ [USE_PROJECTS] Error fetching projects:`, error)
          throw new Error(error.message)
        }

        console.log(`✅ [USE_PROJECTS] Projects fetched:`, data?.length || 0, 'projects')
        return data || []
      } catch (err) {
        console.error(`💥 [USE_PROJECTS] Exception:`, err)
        throw err
      }
    },
  })
}

// Tek proje getir
export function useProject(projectId: string | null) {
  return useQuery({
    queryKey: ['project', projectId],
    queryFn: async (): Promise<Project | null> => {
      if (!projectId) return null

      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .single()

      if (error) {
        throw new Error(error.message)
      }

      return data
    },
    enabled: !!projectId,
  })
}

// Proje oluştur
export function useCreateProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (project: Omit<ProjectInsert, 'user_id'>): Promise<Project> => {
      // Plan limiti kontrol et
      const limitCheck = await canCreateProject()
      if (!limitCheck.allowed) {
        throw new Error(limitCheck.reason || PLAN_LIMIT_MESSAGES.PROJECT_LIMIT_REACHED)
      }

      const { data: { user }, error: userError } = await supabase.auth.getUser()

      if (userError || !user) {
        throw new Error('Kullanıcı oturumu bulunamadı')
      }

      const { data, error } = await supabase
        .from('projects')
        .insert({
          ...project,
          user_id: user.id,
        })
        .select()
        .single()

      if (error) {
        throw new Error(error.message)
      }

      return data
    },
    onSuccess: async () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      // Kullanım istatistiklerini güncelle
      try {
        await updateUsageStats()
        queryClient.invalidateQueries({ queryKey: ['user-limits'] })
        queryClient.invalidateQueries({ queryKey: ['usage-stats'] })
      } catch (error) {
        console.warn('Kullanım istatistikleri güncellenemedi:', error)
      }
    },
  })
}

// Proje güncelle
export function useUpdateProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ id, ...updates }: ProjectUpdate & { id: string }): Promise<Project> => {
      const { data, error } = await supabase
        .from('projects')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        throw new Error(error.message)
      }

      return data
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      queryClient.invalidateQueries({ queryKey: ['project', data.id] })
    },
  })
}

// Güvenli proje adı güncelleme
export function useUpdateProjectNameSecure() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      projectId,
      newName
    }: {
      projectId: string;
      newName: string
    }): Promise<Project> => {
      console.log(`🔒 [UPDATE_PROJECT_NAME] Starting secure update:`, { projectId, newName })

      // Advanced rate limiting check
      const rateLimitResult = await checkRateLimit('project_update')
      if (!rateLimitResult.allowed) {
        throw new RateLimitError(
          'project_update',
          rateLimitResult.retryAfter || 60,
          rateLimitResult.resetTime
        )
      }

      // Client-side validation
      const validation = validateProjectName(newName)
      if (!validation.isValid) {
        throw new Error(validation.error || 'Geçersiz proje adı')
      }

      const sanitizedName = validation.sanitizedValue!

      try {
        // Güvenli database function'ını çağır
        const { data, error } = await supabase.rpc('update_project_name_secure', {
          p_project_id: projectId,
          p_new_name: sanitizedName
        })

        if (error) {
          console.error(`❌ [UPDATE_PROJECT_NAME] Database error:`, error)
          throw new Error(formatValidationError(error.message))
        }

        // Function response kontrolü
        if (!data?.success) {
          console.error(`❌ [UPDATE_PROJECT_NAME] Function error:`, data)
          throw new Error(data?.message || 'Proje adı güncellenemedi')
        }

        console.log(`✅ [UPDATE_PROJECT_NAME] Success:`, data.data)
        return data.data as Project

      } catch (err) {
        console.error(`💥 [UPDATE_PROJECT_NAME] Exception:`, err)

        // Error mesajını kullanıcı dostu hale getir
        let errorMessage = err instanceof Error ? err.message : 'Bilinmeyen hata'

        if (errorMessage.includes('unique_violation') || errorMessage.includes('DuplicateName')) {
          errorMessage = 'Bu isimde bir proje zaten mevcut'
        } else if (errorMessage.includes('check_violation') || errorMessage.includes('InvalidInput')) {
          errorMessage = 'Proje adı geçersiz karakterler içeriyor'
        } else if (errorMessage.includes('RateLimitExceeded')) {
          errorMessage = 'Çok fazla güncelleme isteği. Lütfen bekleyin.'
        }

        throw new Error(errorMessage)
      }
    },
    onMutate: async ({ projectId, newName }) => {
      console.log(`🚀 [UPDATE_PROJECT_NAME] Starting optimistic update:`, { projectId, newName })

      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['projects'] })
      await queryClient.cancelQueries({ queryKey: ['project', projectId] })

      // Snapshot previous values
      const previousProjects = queryClient.getQueryData<Project[]>(['projects'])
      const previousProject = queryClient.getQueryData<Project>(['project', projectId])

      // Optimistically update projects list
      if (previousProjects) {
        queryClient.setQueryData<Project[]>(['projects'], (old) => {
          if (!old) return old
          return old.map(project =>
            project.id === projectId
              ? { ...project, name: newName.trim(), updated_at: new Date().toISOString() }
              : project
          )
        })
      }

      // Optimistically update single project
      if (previousProject) {
        queryClient.setQueryData<Project>(['project', projectId], {
          ...previousProject,
          name: newName.trim(),
          updated_at: new Date().toISOString()
        })
      }

      return { previousProjects, previousProject }
    },
    onSuccess: (data) => {
      console.log(`✅ [UPDATE_PROJECT_NAME] Success, updating cache with server data:`, data.id)

      // Update with actual server data
      queryClient.setQueryData<Project[]>(['projects'], (old) => {
        if (!old) return old
        return old.map(project =>
          project.id === data.id ? { ...project, ...data } : project
        )
      })

      queryClient.setQueryData<Project>(['project', data.id], data)
    },
    onError: (error, variables, context) => {
      console.error(`💥 [UPDATE_PROJECT_NAME] Error, rolling back:`, error)

      // Rollback optimistic updates
      if (context?.previousProjects) {
        queryClient.setQueryData(['projects'], context.previousProjects)
      }
      if (context?.previousProject) {
        queryClient.setQueryData(['project', variables.projectId], context.previousProject)
      }
    },
    onSettled: () => {
      // Always refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['projects'] })
    }
  })
}

// Proje sil
export function useDeleteProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (projectId: string): Promise<void> => {
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectId)

      if (error) {
        throw new Error(error.message)
      }
    },
    onSuccess: async () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      // Kullanım istatistiklerini güncelle
      try {
        await updateUsageStats()
        queryClient.invalidateQueries({ queryKey: ['user-limits'] })
        queryClient.invalidateQueries({ queryKey: ['usage-stats'] })
      } catch (error) {
        console.warn('Kullanım istatistikleri güncellenemedi:', error)
      }
    },
  })
} 