'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'

export interface AdminContext {
  id: string
  title: string
  description: string
  content: string
  category: {
    id: string
    name: string
    icon: string
    color: string
  }
  author_id: string
  author_name: string
  author_email: string
  is_public: boolean
  is_featured: boolean
  is_template: boolean
  tags: string[]
  usage_count: number
  like_count: number
  view_count: number
  status: 'active' | 'inactive' | 'archived'
  approval_status: 'pending' | 'approved' | 'rejected'
  approved_by?: string
  approved_at?: string
  approval_notes?: string
  created_at: string
  updated_at: string
}

// Get all contexts for admin
export function useAdminContexts(filters?: {
  search?: string
  category?: string
  type?: string
  approval_status?: string
}) {
  return useQuery({
    queryKey: ['admin-contexts', filters],
    queryFn: async () => {
      let query = supabase
        .from('contexts')
        .select(`
          *,
          category:context_categories(*),
          author:auth.users(email)
        `)
        .order('created_at', { ascending: false })

      // Apply filters
      if (filters?.search) {
        query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
      }

      if (filters?.category && filters.category !== 'all') {
        query = query.eq('category_id', filters.category)
      }

      if (filters?.approval_status && filters.approval_status !== 'all') {
        query = query.eq('approval_status', filters.approval_status)
      }

      if (filters?.type && filters.type !== 'all') {
        switch (filters.type) {
          case 'public':
            query = query.eq('is_public', true)
            break
          case 'private':
            query = query.eq('is_public', false)
            break
          case 'featured':
            query = query.eq('is_featured', true)
            break
          case 'template':
            query = query.eq('is_template', true)
            break
        }
      }

      const { data, error } = await query

      if (error) throw error

      // Transform data to match AdminContext interface
      return data.map((item) => ({
        id: item.id,
        title: item.title,
        description: item.description,
        content: item.content,
        category: item.category,
        author_id: item.author_id,
        author_name: item.author?.email?.split('@')[0] || 'Unknown',
        author_email: item.author?.email || '<EMAIL>',
        is_public: item.is_public,
        is_featured: item.is_featured,
        is_template: item.is_template,
        tags: item.tags || [],
        usage_count: item.usage_count,
        like_count: item.like_count,
        view_count: item.view_count,
        status: item.status,
        approval_status: item.approval_status,
        approved_by: item.approved_by,
        approved_at: item.approved_at,
        approval_notes: item.approval_notes,
        created_at: item.created_at,
        updated_at: item.updated_at,
      })) as AdminContext[]
    },
  })
}

// Get context statistics for admin dashboard
export function useAdminContextStats() {
  return useQuery({
    queryKey: ['admin-context-stats'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('contexts')
        .select('is_public, is_featured, is_template, approval_status, usage_count, like_count, view_count')

      if (error) throw error

      const stats = {
        totalContexts: data.length,
        publicContexts: data.filter(c => c.is_public).length,
        privateContexts: data.filter(c => !c.is_public).length,
        featuredContexts: data.filter(c => c.is_featured).length,
        templateContexts: data.filter(c => c.is_template).length,
        pendingApproval: data.filter(c => c.approval_status === 'pending').length,
        approvedContexts: data.filter(c => c.approval_status === 'approved').length,
        rejectedContexts: data.filter(c => c.approval_status === 'rejected').length,
        totalUsage: data.reduce((sum, c) => sum + (c.usage_count || 0), 0),
        totalLikes: data.reduce((sum, c) => sum + (c.like_count || 0), 0),
        totalViews: data.reduce((sum, c) => sum + (c.view_count || 0), 0),
      }

      return stats
    },
  })
}

// Approve or reject context
export function useApproveContext() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      contextId,
      status,
      notes
    }: {
      contextId: string
      status: 'approved' | 'rejected' | 'pending'
      notes?: string
    }) => {
      if (status === 'pending') {
        // For pending status, directly update the database
        const { error } = await supabase
          .from('contexts')
          .update({
            approval_status: 'pending',
            approved_by: null,
            approved_at: null,
            approval_notes: notes || null
          })
          .eq('id', contextId)

        if (error) throw error
      } else {
        // For approved/rejected, use the RPC function
        const { error } = await supabase.rpc('approve_context', {
          context_id_param: contextId,
          approval_status_param: status,
          approval_notes_param: notes
        })

        if (error) throw error
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-contexts'] })
      queryClient.invalidateQueries({ queryKey: ['admin-context-stats'] })
      queryClient.invalidateQueries({ queryKey: ['contexts'] }) // Also invalidate main contexts
    },
  })
}

// Toggle context featured status
export function useToggleContextFeatured() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ contextId, featured }: { contextId: string; featured: boolean }) => {
      const { error } = await supabase
        .from('contexts')
        .update({ is_featured: featured })
        .eq('id', contextId)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-contexts'] })
      queryClient.invalidateQueries({ queryKey: ['admin-context-stats'] })
      queryClient.invalidateQueries({ queryKey: ['contexts'] })
    },
  })
}

// Toggle context public status
export function useToggleContextPublic() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ contextId, isPublic }: { contextId: string; isPublic: boolean }) => {
      const { error } = await supabase
        .from('contexts')
        .update({ 
          is_public: isPublic,
          // If making private, auto-approve. If making public, set to pending
          approval_status: isPublic ? 'pending' : 'approved'
        })
        .eq('id', contextId)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-contexts'] })
      queryClient.invalidateQueries({ queryKey: ['admin-context-stats'] })
      queryClient.invalidateQueries({ queryKey: ['contexts'] })
    },
  })
}

// Delete context
export function useDeleteContext() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (contextId: string) => {
      const { error } = await supabase
        .from('contexts')
        .delete()
        .eq('id', contextId)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-contexts'] })
      queryClient.invalidateQueries({ queryKey: ['admin-context-stats'] })
      queryClient.invalidateQueries({ queryKey: ['contexts'] })
    },
  })
}

// Get context categories
export function useContextCategories() {
  return useQuery({
    queryKey: ['context-categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('context_categories')
        .select('*')
        .eq('is_active', true)
        .order('order_index')

      if (error) throw error
      return data
    },
  })
}
