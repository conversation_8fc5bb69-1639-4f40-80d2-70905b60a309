/**
 * Advanced Rate Limiter Tests
 * Comprehensive testing for the new rate limiting system
 */

import {
  ClientRateLimiter,
  ServerRateLimiter,
  checkRateLimit,
  RateLimitError,
  RATE_LIMITS
} from '../rate-limiter'

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn()
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
})

// Mock Supabase
jest.mock('../supabase-browser', () => ({
  supabaseBrowser: {
    auth: {
      getUser: jest.fn().mockResolvedValue({
        data: { user: { id: 'test-user-id' } }
      })
    },
    rpc: jest.fn()
  }
}))

describe('ClientRateLimiter', () => {
  let rateLimiter: ClientRateLimiter

  beforeEach(() => {
    rateLimiter = new ClientRateLimiter()
    jest.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue(null)
  })

  describe('checkLimit', () => {
    it('allows first request', async () => {
      const result = await rateLimiter.checkLimit('login')
      
      expect(result.allowed).toBe(true)
      expect(result.remainingRequests).toBe(RATE_LIMITS.login.maxRequests - 1)
      expect(mockLocalStorage.setItem).toHaveBeenCalled()
    })

    it('tracks request count correctly', async () => {
      const now = Date.now()
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        count: 3,
        windowStart: now,
        lastRequest: now
      }))

      const result = await rateLimiter.checkLimit('login')
      
      expect(result.allowed).toBe(true)
      expect(result.remainingRequests).toBe(RATE_LIMITS.login.maxRequests - 4)
    })

    it('blocks when limit exceeded', async () => {
      const now = Date.now()
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        count: RATE_LIMITS.login.maxRequests,
        windowStart: now,
        lastRequest: now
      }))

      const result = await rateLimiter.checkLimit('login')
      
      expect(result.allowed).toBe(false)
      expect(result.remainingRequests).toBe(0)
      expect(result.retryAfter).toBeGreaterThan(0)
    })

    it('resets after time window expires', async () => {
      const oldTime = Date.now() - (RATE_LIMITS.login.windowMinutes * 60 * 1000 + 1000)
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        count: RATE_LIMITS.login.maxRequests,
        windowStart: oldTime,
        lastRequest: oldTime
      }))

      const result = await rateLimiter.checkLimit('login')
      
      expect(result.allowed).toBe(true)
      expect(result.remainingRequests).toBe(RATE_LIMITS.login.maxRequests - 1)
    })

    it('handles localStorage errors gracefully', async () => {
      mockLocalStorage.getItem.mockImplementation(() => {
        throw new Error('localStorage error')
      })

      const result = await rateLimiter.checkLimit('login')
      
      expect(result.allowed).toBe(true) // Should allow on error
    })

    it('supports user-specific rate limiting', async () => {
      const userId = 'user-123'
      
      await rateLimiter.checkLimit('login', userId)
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        expect.stringContaining(`_${userId}`),
        expect.any(String)
      )
    })
  })

  describe('resetLimit', () => {
    it('removes rate limit data from localStorage', () => {
      rateLimiter.resetLimit('login')
      
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('rate_limit_login')
    })

    it('handles errors gracefully', () => {
      mockLocalStorage.removeItem.mockImplementation(() => {
        throw new Error('localStorage error')
      })

      expect(() => rateLimiter.resetLimit('login')).not.toThrow()
    })
  })

  describe('getStatus', () => {
    it('returns correct status for new action', () => {
      const status = rateLimiter.getStatus('login')
      
      expect(status).toEqual({
        count: 0,
        remaining: RATE_LIMITS.login.maxRequests,
        resetTime: expect.any(Number)
      })
    })

    it('returns correct status for existing action', () => {
      const now = Date.now()
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        count: 3,
        windowStart: now,
        lastRequest: now
      }))

      const status = rateLimiter.getStatus('login')
      
      expect(status).toEqual({
        count: 3,
        remaining: RATE_LIMITS.login.maxRequests - 3,
        resetTime: expect.any(Number)
      })
    })
  })
})

describe('ServerRateLimiter', () => {
  let rateLimiter: ServerRateLimiter
  const mockSupabase = jest.requireMock('../supabase-browser').supabaseBrowser

  beforeEach(() => {
    rateLimiter = new ServerRateLimiter()
    jest.clearAllMocks()
  })

  describe('checkLimit', () => {
    it('calls Supabase RPC with correct parameters', async () => {
      mockSupabase.rpc.mockResolvedValue({ data: true, error: null })

      await rateLimiter.checkLimit('login', 'user-123')

      expect(mockSupabase.rpc).toHaveBeenCalledWith('check_rate_limit', {
        p_user_id: 'user-123',
        p_action_type: 'login',
        p_max_requests: RATE_LIMITS.login.maxRequests,
        p_window_minutes: RATE_LIMITS.login.windowMinutes
      })
    })

    it('returns allowed when server allows', async () => {
      mockSupabase.rpc.mockResolvedValue({ data: true, error: null })

      const result = await rateLimiter.checkLimit('login')

      expect(result.allowed).toBe(true)
    })

    it('returns blocked when server blocks', async () => {
      mockSupabase.rpc.mockResolvedValue({ data: false, error: null })

      const result = await rateLimiter.checkLimit('login')

      expect(result.allowed).toBe(false)
      expect(result.retryAfter).toBeGreaterThan(0)
    })

    it('handles server errors gracefully', async () => {
      mockSupabase.rpc.mockResolvedValue({ 
        data: null, 
        error: { message: 'Server error' } 
      })

      const result = await rateLimiter.checkLimit('login')

      expect(result.allowed).toBe(true) // Should allow on error
    })

    it('gets user ID automatically when not provided', async () => {
      mockSupabase.rpc.mockResolvedValue({ data: true, error: null })

      await rateLimiter.checkLimit('login')

      expect(mockSupabase.auth.getUser).toHaveBeenCalled()
    })
  })
})

describe('checkRateLimit (combined)', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue(null)
  })

  it('performs client-only check when specified', async () => {
    const result = await checkRateLimit('login', { clientOnly: true })

    expect(result.source).toBe('client')
    expect(result.allowed).toBe(true)
  })

  it('performs server-only check when specified', async () => {
    const mockSupabase = jest.requireMock('../supabase-browser').supabaseBrowser
    mockSupabase.rpc.mockResolvedValue({ data: true, error: null })

    const result = await checkRateLimit('login', { serverOnly: true })

    expect(result.source).toBe('server')
    expect(mockSupabase.rpc).toHaveBeenCalled()
  })

  it('performs both checks by default', async () => {
    const mockSupabase = jest.requireMock('../supabase-browser').supabaseBrowser
    mockSupabase.rpc.mockResolvedValue({ data: true, error: null })

    const result = await checkRateLimit('login')

    expect(result.source).toBe('both')
  })

  it('blocks if client check fails', async () => {
    const now = Date.now()
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
      count: RATE_LIMITS.login.maxRequests,
      windowStart: now,
      lastRequest: now
    }))

    const result = await checkRateLimit('login')

    expect(result.allowed).toBe(false)
    expect(result.source).toBe('client')
  })
})

describe('RateLimitError', () => {
  it('creates error with correct properties', () => {
    const retryAfter = 60
    const resetTime = Date.now() + 60000
    const error = new RateLimitError('login', retryAfter, resetTime)

    expect(error.name).toBe('RateLimitError')
    expect(error.action).toBe('login')
    expect(error.retryAfter).toBe(60)
    expect(error.message).toContain('login')
    expect(error.message).toContain('60 seconds')
  })
})

describe('RATE_LIMITS configuration', () => {
  it('has all required actions', () => {
    const requiredActions = [
      'login', 'signup', 'password_reset',
      'project_create', 'project_update', 'project_delete',
      'prompt_create', 'prompt_update', 'prompt_delete',
      'context_create', 'context_batch_add',
      'plan_change', 'api_general'
    ]

    requiredActions.forEach(action => {
      expect(RATE_LIMITS).toHaveProperty(action)
      expect(RATE_LIMITS[action as keyof typeof RATE_LIMITS]).toHaveProperty('maxRequests')
      expect(RATE_LIMITS[action as keyof typeof RATE_LIMITS]).toHaveProperty('windowMinutes')
    })
  })

  it('has reasonable limits', () => {
    Object.entries(RATE_LIMITS).forEach(([action, limits]) => {
      expect(limits.maxRequests).toBeGreaterThan(0)
      expect(limits.windowMinutes).toBeGreaterThan(0)
      expect(limits.maxRequests).toBeLessThan(1000) // Sanity check
      expect(limits.windowMinutes).toBeLessThan(1440) // Max 24 hours
    })
  })
})
