// Debug script to test admin authentication
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://iqehopwgrczylqliajww.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlxZWhvcHdncmN6eWxxbGlhancyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjEyMzE5NzQsImV4cCI6MjAzNjgwNzk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testAuth() {
  console.log('Testing admin authentication...');

  try {
    // Test login
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'your-password-here' // Replace with actual password
    });

    if (error) {
      console.error('Login failed:', error.message);
      return;
    }

    console.log('Login successful for user:', data.user.id);

    // Check admin status
    const { data: adminData, error: adminError } = await supabase
      .from('admin_users')
      .select('user_id, is_active, role')
      .eq('user_id', data.user.id)
      .eq('is_active', true)
      .single();

    if (adminError) {
      console.error('Admin check failed:', adminError.message);
      return;
    }

    console.log('Admin status:', adminData);

    // Test API call
    const response = await fetch('http://localhost:3000/api/admin/dashboard/stats', {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${data.session.access_token}`
      },
    });

    console.log('API Response status:', response.status);

    if (response.ok) {
      const apiData = await response.json();
      console.log('API Response:', apiData);
    } else {
      const errorData = await response.text();
      console.error('API Error:', errorData);
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
}

testAuth();