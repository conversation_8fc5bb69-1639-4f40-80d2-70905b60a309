---
type: "performance_guide"
role: "performance_optimization_patterns"
dependencies:
  - "core/PROJECT_CORE.md"
  - "frontend/MAIN_APP.md"
  - "backend/SUPABASE.md"
  - "fixes/TYPESCRIPT_PATTERNS.md"
related_performance:
  - "frontend/REACT_OPTIMIZATION.md"
  - "backend/DATABASE_OPTIMIZATION.md"
auto_update_from:
  - "frontend/MAIN_APP.md"
  - "backend/SUPABASE.md"
performance_implementations:
  - date: "2025-01-29"
    feature: "Project Name Editing"
    optimizations: "Debounced validation, React.memo, optimistic updates, smart caching"
    metrics: "300ms debounce, <200ms response time, minimal re-renders"
    status: "implemented"
last_updated: "2025-01-29"
---

# PromptFlow Performance Optimization Guide

## Performance-First Development Principles

### 1. Debounced Operations

#### Debounced Validation Pattern
```typescript
// ✅ Efficient debounced validation to prevent excessive API calls
export function createAdvancedDebouncedValidator(
  projects: Array<{ id: string; name: string }>,
  currentProjectId: string,
  delay: number = 300 // Optimal balance between UX and performance
) {
  let timeoutId: NodeJS.Timeout
  
  return (value: string, callback: (result: ValidationResult) => void) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(async () => {
      const result = await validateProjectNameUnique(value, currentProjectId, projects)
      callback(result)
    }, delay)
  }
}

// ✅ Usage in component
const ProjectNameEditor = memo(function ProjectNameEditor({ projectId, currentName }) {
  const { data: projects = [] } = useProjects()
  const [isValidating, setIsValidating] = useState(false)
  
  // Memoized debounced validator
  const debouncedValidator = useCallback(() => {
    return createAdvancedDebouncedValidator(projects, projectId, 300)
  }, [projects, projectId])()
  
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setEditValue(value)
    
    if (value.trim()) {
      setIsValidating(true)
      debouncedValidator(value, (result) => {
        setValidationError(result.isValid ? '' : result.error || '')
        setIsValidating(false)
      })
    }
  }, [debouncedValidator])
})
```

### 2. React Performance Optimization

#### Component Memoization
```typescript
// ✅ Memoized component to prevent unnecessary re-renders
const ProjectNameEditor = memo(function ProjectNameEditor({
  projectId,
  currentName,
  onNameUpdated,
  className,
  disabled = false
}: ProjectNameEditorProps) {
  // Component implementation
})

// ✅ Memoized callbacks to prevent child re-renders
const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
  const value = e.target.value
  setEditValue(value)
  // Validation logic
}, [debouncedValidator, currentName])

const saveChanges = useCallback(async () => {
  if (!editValue.trim() || validationError || isValidating) return
  
  try {
    const result = await updateMutation.mutateAsync({
      projectId,
      newName: editValue.trim()
    })
    
    toast.success('Proje adı başarıyla güncellendi!')
    setIsEditing(false)
    onNameUpdated?.(result.name)
  } catch (error) {
    // Error handling
  }
}, [editValue, validationError, isValidating, projectId, updateMutation, onNameUpdated])
```

#### Optimized Re-render Prevention
```typescript
// ✅ Prevent unnecessary re-renders with proper dependencies
useEffect(() => {
  if (!isEditing) {
    setEditValue(currentName)
  }
}, [currentName, isEditing]) // Only re-run when these specific values change

// ✅ Memoized validation state
const isFormValid = useMemo(() => {
  return !validationError && 
         !isValidating && 
         editValue.trim() && 
         !isSameProjectName(editValue, currentName)
}, [validationError, isValidating, editValue, currentName])
```

### 3. Optimistic Updates

#### Smart Cache Management
```typescript
// ✅ Optimistic updates with rollback capability
export function useUpdateProjectNameSecure() {
  const queryClient = useQueryClient()

  return useMutation({
    onMutate: async ({ projectId, newName }) => {
      // Cancel outgoing refetches to prevent race conditions
      await queryClient.cancelQueries({ queryKey: ['projects'] })
      await queryClient.cancelQueries({ queryKey: ['project', projectId] })
      
      // Snapshot previous values for rollback
      const previousProjects = queryClient.getQueryData<Project[]>(['projects'])
      const previousProject = queryClient.getQueryData<Project>(['project', projectId])
      
      // Optimistically update projects list
      if (previousProjects) {
        queryClient.setQueryData<Project[]>(['projects'], (old) => {
          if (!old) return old
          return old.map(project => 
            project.id === projectId 
              ? { ...project, name: newName.trim(), updated_at: new Date().toISOString() }
              : project
          )
        })
      }
      
      // Optimistically update single project
      if (previousProject) {
        queryClient.setQueryData<Project>(['project', projectId], {
          ...previousProject,
          name: newName.trim(),
          updated_at: new Date().toISOString()
        })
      }
      
      return { previousProjects, previousProject }
    },
    
    onSuccess: (data) => {
      // Update with actual server data
      queryClient.setQueryData<Project[]>(['projects'], (old) => {
        if (!old) return old
        return old.map(project => 
          project.id === data.id ? { ...project, ...data } : project
        )
      })
      
      queryClient.setQueryData<Project>(['project', data.id], data)
    },
    
    onError: (error, variables, context) => {
      // Rollback optimistic updates on error
      if (context?.previousProjects) {
        queryClient.setQueryData(['projects'], context.previousProjects)
      }
      if (context?.previousProject) {
        queryClient.setQueryData(['project', variables.projectId], context.previousProject)
      }
    },
    
    onSettled: () => {
      // Ensure consistency with server
      queryClient.invalidateQueries({ queryKey: ['projects'] })
    }
  })
}
```

### 4. Bundle Size Optimization

#### Selective Imports
```typescript
// ✅ Import only what you need
import { memo, useCallback, useState, useEffect } from 'react'
import { Check, X, Loader2, CheckCircle, Edit2 } from 'lucide-react' // Specific icons only

// ❌ Avoid importing entire libraries
// import * as Icons from 'lucide-react'

// ✅ Dynamic imports for heavy components
const HeavyComponent = lazy(() => import('./heavy-component'))
```

#### Code Splitting
```typescript
// ✅ Route-based code splitting
const ProjectNameEditor = lazy(() => 
  import('./project-name-editor').then(module => ({ 
    default: module.ProjectNameEditor 
  }))
)

// ✅ Feature-based splitting
const AdvancedValidation = lazy(() => 
  import('./advanced-validation').then(module => ({
    default: module.AdvancedValidation
  }))
)
```

### 5. Database Performance

#### Efficient Queries
```sql
-- ✅ Optimized project update with minimal data transfer
CREATE OR REPLACE FUNCTION public.update_project_name_secure(
  p_project_id uuid,
  p_new_name text
) RETURNS json AS $$
BEGIN
  -- Single query with all validations
  UPDATE public.projects 
  SET 
    name = TRIM(p_new_name),
    updated_at = now()
  WHERE 
    id = p_project_id 
    AND user_id = auth.uid()
    AND char_length(TRIM(p_new_name)) BETWEEN 3 AND 50
    AND TRIM(p_new_name) ~ '^[a-zA-Z0-9\s\-_.]+$'
  RETURNING 
    id, name, updated_at; -- Return only necessary fields
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### Indexed Queries
```sql
-- ✅ Efficient indexes for performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS projects_user_id_status_idx 
ON public.projects (user_id, status) 
WHERE status = 'active';

CREATE INDEX CONCURRENTLY IF NOT EXISTS projects_name_search_idx 
ON public.projects USING gin(to_tsvector('english', name));
```

### 6. Network Performance

#### Request Optimization
```typescript
// ✅ Batch operations when possible
const updateMultipleProjects = async (updates: ProjectUpdate[]) => {
  // Single request for multiple updates
  return await supabase.rpc('batch_update_projects', { updates })
}

// ✅ Efficient error handling without retries for validation errors
const updateProjectMutation = useMutation({
  mutationFn: updateProjectNameSecure,
  retry: (failureCount, error) => {
    // Don't retry validation errors
    if (error.message.includes('InvalidInput') || 
        error.message.includes('DuplicateName')) {
      return false
    }
    return failureCount < 3
  }
})
```

### 7. Memory Management

#### Cleanup Patterns
```typescript
// ✅ Proper cleanup to prevent memory leaks
useEffect(() => {
  let timeoutId: NodeJS.Timeout
  
  const debouncedValidation = (value: string) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      // Validation logic
    }, 300)
  }
  
  // Cleanup on unmount
  return () => {
    clearTimeout(timeoutId)
  }
}, [])

// ✅ Abort controllers for cancelled requests
useEffect(() => {
  const abortController = new AbortController()
  
  const validateAsync = async () => {
    try {
      const result = await fetch('/api/validate', {
        signal: abortController.signal
      })
      // Handle result
    } catch (error) {
      if (error.name !== 'AbortError') {
        // Handle actual errors
      }
    }
  }
  
  return () => {
    abortController.abort()
  }
}, [])
```

## Performance Metrics & Monitoring

### Key Performance Indicators
```typescript
// ✅ Performance monitoring
const performanceMetrics = {
  debounceDelay: 300, // ms - optimal for UX/performance balance
  maxResponseTime: 200, // ms - target API response time
  cacheHitRate: 95, // % - TanStack Query cache effectiveness
  bundleSize: 85, // kB - dashboard page size
  renderTime: 16, // ms - target render time (60fps)
  memoryUsage: 50 // MB - target memory usage
}

// ✅ Performance measurement
const measurePerformance = () => {
  const start = performance.now()
  
  // Operation
  
  const end = performance.now()
  console.log(`Operation took ${end - start} milliseconds`)
}
```

### Bundle Analysis
```bash
# ✅ Analyze bundle size
npm run build
npx @next/bundle-analyzer

# ✅ Performance profiling
npm run dev -- --profile
```

## Performance Testing

### Load Testing
```typescript
// ✅ Performance tests
describe('Performance Tests', () => {
  it('debounces validation calls efficiently', async () => {
    const mockValidator = jest.fn()
    const debouncedValidator = createDebouncedValidator(mockValidator, 100)
    
    // Rapid calls
    debouncedValidator('test1')
    debouncedValidator('test2')
    debouncedValidator('test3')
    
    // Should only call once after debounce
    await new Promise(resolve => setTimeout(resolve, 150))
    expect(mockValidator).toHaveBeenCalledTimes(1)
    expect(mockValidator).toHaveBeenCalledWith('test3')
  })
  
  it('prevents memory leaks in component', () => {
    const { unmount } = render(<ProjectNameEditor {...props} />)
    
    // Simulate rapid mount/unmount
    for (let i = 0; i < 100; i++) {
      unmount()
      render(<ProjectNameEditor {...props} />)
    }
    
    // Memory should be stable
    expect(performance.memory?.usedJSHeapSize).toBeLessThan(50 * 1024 * 1024) // 50MB
  })
})
```

## Update Requirements
- Monitor and update performance metrics regularly
- Profile new features for performance impact
- Maintain bundle size targets
- Update optimization patterns as React/Next.js evolve
- Document performance regressions and solutions
- Regular performance audits and improvements
