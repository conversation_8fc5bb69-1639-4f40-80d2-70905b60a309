'use client';

import { useMemo } from 'react';
import {
  SortableContext,
  verticalListSortingStrategy,
  arrayMove,
} from '@dnd-kit/sortable';
import { DragEndEvent } from '@dnd-kit/core';
import { Box, VStack } from '@chakra-ui/react';
import { SortablePromptItem } from './sortable-prompt-item';
import { DragDropProvider } from './drag-drop-provider';

interface Prompt {
  id: string;
  prompt_text: string;
  order_index: number;
  is_used: boolean;
  created_at: string;
  updated_at: string;
  project_id: string;
  user_id: string;
}

interface SortablePromptListProps {
  prompts: Prompt[];
  onReorder: (prompts: Prompt[]) => void;
  onEdit?: (prompt: Prompt) => void;
  onDelete?: (promptId: string) => void;
  onToggleUsed?: (promptId: string, isUsed: boolean) => void;
  isLoading?: boolean;
}

export function SortablePromptList({
  prompts,
  onReorder,
  onEdit,
  onDelete,
  onToggleUsed,
  isLoading = false,
}: SortablePromptListProps) {
  // Sort prompts by order_index for consistent ordering
  const sortedPrompts = useMemo(() => {
    return [...prompts].sort((a, b) => a.order_index - b.order_index);
  }, [prompts]);

  // Extract IDs for SortableContext
  const promptIds = useMemo(() => {
    return sortedPrompts.map(prompt => prompt.id);
  }, [sortedPrompts]);

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    const oldIndex = sortedPrompts.findIndex(prompt => prompt.id === active.id);
    const newIndex = sortedPrompts.findIndex(prompt => prompt.id === over.id);

    if (oldIndex === -1 || newIndex === -1) {
      return;
    }

    // Reorder the prompts array
    const reorderedPrompts = arrayMove(sortedPrompts, oldIndex, newIndex);

    // Update order_index for each prompt
    const updatedPrompts = reorderedPrompts.map((prompt, index) => ({
      ...prompt,
      order_index: index + 1,
    }));

    onReorder(updatedPrompts);
  };

  if (isLoading) {
    return (
      <VStack spacing={4} align="stretch">
        {Array.from({ length: 5 }).map((_, index) => (
          <Box
            key={index}
            height="80px"
            bg="gray.100"
            borderRadius="md"
            className="animate-pulse"
          />
        ))}
      </VStack>
    );
  }

  return (
    <DragDropProvider onDragEnd={handleDragEnd}>
      <SortableContext items={promptIds} strategy={verticalListSortingStrategy}>
        <VStack spacing={4} align="stretch">
          {sortedPrompts.map((prompt) => (
            <SortablePromptItem
              key={prompt.id}
              prompt={prompt}
              onEdit={onEdit}
              onDelete={onDelete}
              onToggleUsed={onToggleUsed}
            />
          ))}
        </VStack>
      </SortableContext>
    </DragDropProvider>
  );
}
