---
type: "fixes"
role: "eslint_troubleshooting_guide"
dependencies:
  - "core/PROJECT_CORE.md"
  - "frontend/MAIN_APP.md"
  - "frontend/ADMIN_PANEL.md"
related_fixes:
  - "fixes/BUILD_ERROR_RESOLUTION.md"
  - "fixes/TYPESCRIPT_PATTERNS.md"
auto_update_from:
  - "frontend/MAIN_APP.md"
  - "frontend/ADMIN_PANEL.md"
solution_history:
  - date: "2025-01-29"
    issue: "Unescaped entities in Turkish text"
    solution: "Replace apostrophes with &apos; in JSX"
    status: "resolved"
last_updated: "2025-01-29"
---

# PromptFlow ESLint Configuration & Error Resolution

## ESLint Configuration Standards

### Base Configuration
```javascript
// eslint.config.mjs (Next.js 15 - Flat Config)
import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      "@typescript-eslint/no-explicit-any": "error",
      "@typescript-eslint/no-unused-vars": "warn",
      "@next/next/no-img-element": "warn",
      "react/no-unescaped-entities": "error"
    }
  }
];

export default eslintConfig;
```

### Legacy Configuration (Admin Panel)
```json
// .eslintrc.json (Next.js 14)
{
  "extends": [
    "next/core-web-vitals",
    "@next/eslint-config-next"
  ],
  "rules": {
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/no-unused-vars": "warn",
    "@next/next/no-img-element": "warn",
    "react/no-unescaped-entities": "error",
    "prefer-const": "error",
    "no-var": "error"
  }
}
```

## Common ESLint Error Patterns

### Pattern 1: Unescaped Entities in JSX
**Error**: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`
**Rule**: `react/no-unescaped-entities`

#### Turkish Text Handling
```typescript
// ❌ Problematic
<span>Prompt'lar (Proje başına max)</span>
<p>Kullanıcı'nın planı</p>
<div>Plan'ınızı yükseltin</div>

// ✅ Fixed
<span>Prompt&apos;lar (Proje başına max)</span>
<p>Kullanıcı&apos;nın planı</p>
<div>Plan&apos;ınızı yükseltin</div>
```

#### Automation Pattern
```bash
# Find and replace pattern
find src -name "*.tsx" -exec sed -i "s/'/\&apos;/g" {} \;

# Or use VS Code regex find/replace:
# Find: ([^=])'([^=])
# Replace: $1&apos;$2
```

### Pattern 2: Unused Variables and Imports
**Error**: `'X' is defined but never used`
**Rule**: `@typescript-eslint/no-unused-vars`

#### Common Cleanup Scenarios
```typescript
// ❌ Unused imports
import { UnusedComponent, UsedComponent } from './components'
import { unusedUtil } from './utils'
import React, { useState, useEffect, useMemo } from 'react' // useMemo unused

// ✅ Cleaned imports
import { UsedComponent } from './components'
import React, { useState, useEffect } from 'react'

// ❌ Unused variables
function Component({ data, unusedProp }: Props) {
  const unusedVariable = 'not used'
  const [count, setCount] = useState(0)
  const [unusedState, setUnusedState] = useState('')
  
  return <div>{count}</div>
}

// ✅ Cleaned variables
function Component({ data }: Props) {
  const [count, setCount] = useState(0)
  
  return <div>{count}</div>
}
```

#### Automation Rules
1. **Remove unused imports**: If import not referenced, remove entire line
2. **Remove unused variables**: If variable declared but not used, remove
3. **Remove unused props**: If destructured prop not used, remove from destructuring
4. **Remove unused state**: If useState declared but never read, remove

### Pattern 3: Image Optimization Warnings
**Error**: `Using <img> could result in slower LCP and higher bandwidth`
**Rule**: `@next/next/no-img-element`

#### Next.js Image Optimization
```typescript
// ❌ Regular img tags
<img src="/logo.png" alt="Logo" width={100} height={50} />
<img src={user.avatar} alt="Avatar" className="rounded-full" />

// ✅ Next.js Image component
import Image from 'next/image'

<Image 
  src="/logo.png" 
  alt="Logo" 
  width={100} 
  height={50}
  priority // For above-the-fold images
/>

<Image 
  src={user.avatar} 
  alt="Avatar" 
  width={40}
  height={40}
  className="rounded-full"
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
/>
```

#### When to Keep `<img>` Tags
- External images with unknown dimensions
- SVG icons that don't need optimization
- Images in markdown content
- Third-party widget images

### Pattern 4: TypeScript Specific Rules
**Error**: `Unexpected any. Specify a different type`
**Rule**: `@typescript-eslint/no-explicit-any`

#### Type Replacement Patterns
```typescript
// ❌ Generic any usage
function handleData(data: any): any {
  return data.map((item: any) => item.value)
}

// ✅ Proper typing
interface DataItem {
  value: string | number
  id: string
}

function handleData(data: DataItem[]): (string | number)[] {
  return data.map((item: DataItem) => item.value)
}

// ❌ Any in object types
interface Config {
  settings: any
  metadata: any
}

// ✅ Specific object types
interface Config {
  settings: {
    theme: 'light' | 'dark'
    language: string
    notifications: boolean
  }
  metadata: Record<string, string | number | boolean>
}
```

## Project-Specific ESLint Rules

### PromptFlow Main App Rules
```javascript
// Additional rules for React 19 + Next.js 15
{
  rules: {
    "react/no-unescaped-entities": "error",
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/no-unused-vars": ["warn", { 
      "argsIgnorePattern": "^_",
      "varsIgnorePattern": "^_" 
    }],
    "@next/next/no-img-element": "warn",
    "prefer-const": "error",
    "no-console": ["warn", { "allow": ["warn", "error"] }]
  }
}
```

### Admin Panel Rules
```json
{
  "rules": {
    "react/no-unescaped-entities": "error",
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/no-unused-vars": "warn",
    "@next/next/no-img-element": "warn",
    "prefer-const": "error",
    "no-console": "off"
  }
}
```

## Automated Fix Procedures

### Pre-commit Hook Setup
```bash
# Install husky and lint-staged
npm install --save-dev husky lint-staged

# Add to package.json
{
  "lint-staged": {
    "*.{ts,tsx}": [
      "eslint --fix",
      "prettier --write"
    ]
  }
}

# Setup husky
npx husky install
npx husky add .husky/pre-commit "npx lint-staged"
```

### VS Code Integration
```json
// .vscode/settings.json
{
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "eslint.format.enable": true
}
```

### Batch Fix Commands
```bash
# Fix all auto-fixable issues
npm run lint -- --fix

# Fix specific file types
npx eslint src/**/*.{ts,tsx} --fix

# Check for remaining issues
npm run lint

# Type check
npx tsc --noEmit
```

## Error Priority Matrix

### Critical (Must Fix for Build)
1. `@typescript-eslint/no-explicit-any` - Type safety
2. `react/no-unescaped-entities` - JSX syntax errors
3. Syntax errors and import issues

### Important (Should Fix)
1. `@typescript-eslint/no-unused-vars` - Code cleanliness
2. `@next/next/no-img-element` - Performance optimization
3. `prefer-const` - Code quality

### Optional (Nice to Have)
1. `no-console` - Production cleanliness
2. Formatting and style rules
3. Accessibility warnings

## Update Requirements
- Update ESLint configuration when upgrading Next.js versions
- Maintain separate configs for main app vs admin panel
- Document new error patterns as they emerge
- Keep automation scripts current with tooling changes
- Update priority matrix based on project needs
- Sync rules between both applications where applicable
