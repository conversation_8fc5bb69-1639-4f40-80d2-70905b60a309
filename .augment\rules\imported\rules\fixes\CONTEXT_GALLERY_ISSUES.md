---
type: "fixes"
role: "troubleshooting_guide"
parent_feature: "features/CONTEXT_GALLERY.md"
dependencies:
  - "features/CONTEXT_GALLERY.md"
  - "features/CONTEXT_GALLERY_ADMIN.md"
  - "frontend/MAIN_APP.md"
  - "backend/SUPABASE.md"
auto_update_from:
  - "features/CONTEXT_GALLERY.md"
  - "features/CONTEXT_GALLERY_ADMIN.md"
issue_patterns:
  - "Ekle button not working"
  - "Templates not loading"
  - "Admin approval failures"
  - "Search functionality issues"
  - "Performance problems"
  - "RLS policy problems"
solution_history:
  - date: "2025-01-29"
    issue: "Ekle button not working"
    solution: "Added proper event handlers, permission checks, and error handling"
    status: "resolved"
  - date: "2025-01-29"
    issue: "Build errors in Context Gallery"
    solution: "Fixed TypeScript any types and ESLint escape characters"
    status: "resolved"
last_updated: "2025-01-29"
---

# Context Gallery Issues & Troubleshooting

## Common Context Gallery Issues

### Issue 1: "Ekle" Button Not Working
**Symptoms**: Context Gallery "Ekle" (Add) button doesn't respond or shows error
**Priority**: High (Core functionality)

#### Root Cause Analysis
```typescript
// ❌ Common causes:
1. Missing onClick handler
2. Permission/limit checks failing
3. Missing project context
4. Network/API errors
5. State management issues

// ✅ Diagnostic steps:
console.log('Button clicked:', { template, currentProject, userLimits })
console.log('User permissions:', await checkUserPermissions())
console.log('Current project:', currentProjectId)
```

#### Solution Patterns
```typescript
// ✅ Complete "Ekle" button implementation
const handleAddContext = async (template: ContextTemplate) => {
  try {
    // 1. Validate current project
    if (!currentProjectId) {
      toast.error('Lütfen önce bir proje seçin')
      return
    }

    // 2. Check user limits
    const { data: limits } = await checkUserLimits()
    if (!limits.can_create_prompt) {
      toast.error('Plan limitinize ulaştınız')
      showUpgradeModal()
      return
    }

    // 3. Check template access
    if (template.is_premium && !userPlan.has_premium_access) {
      toast.error('Bu template premium üyelik gerektirir')
      showUpgradeModal()
      return
    }

    // 4. Add to project
    const newPrompt = await createPrompt({
      project_id: currentProjectId,
      prompt_text: template.content,
      title: template.title,
      tags: template.tags,
      source: 'context_gallery',
      source_template_id: template.id
    })

    // 5. Track usage
    await trackContextUsage(template.id)

    // 6. Update UI
    toast.success('Context başarıyla eklendi')
    onClose?.() // Close modal if needed
    
    // 7. Navigate to new prompt (optional)
    router.push(`/dashboard?project=${currentProjectId}&prompt=${newPrompt.id}`)

  } catch (error) {
    console.error('Context add error:', error)
    toast.error('Context eklenirken hata oluştu')
  }
}

// ✅ Button with proper error handling
<Button 
  onClick={() => handleAddContext(template)}
  disabled={isAdding || !currentProjectId}
  className="w-full"
>
  {isAdding ? (
    <>
      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
      Ekleniyor...
    </>
  ) : (
    'Ekle'
  )}
</Button>
```

### Issue 2: Templates Not Loading
**Symptoms**: Empty gallery, infinite loading, or error states
**Priority**: High (Core functionality)

#### Diagnostic Steps
```typescript
// Check data fetching
const { data, isLoading, error } = useContextTemplates(categoryId)
console.log('Templates data:', { data, isLoading, error })

// Check RLS policies
const testRLS = async () => {
  const { data, error } = await supabase
    .from('context_templates')
    .select('*')
    .eq('is_public', true)
    .eq('status', 'approved')
    .limit(1)
  
  console.log('RLS test:', { data, error })
}
```

#### Solution Patterns
```typescript
// ✅ Robust template loading with error handling
export const useContextTemplates = (categoryId?: string) => {
  return useQuery({
    queryKey: ['context-templates', categoryId],
    queryFn: async () => {
      let query = supabase
        .from('context_templates')
        .select(`
          *,
          category:context_categories(*),
          author:auth.users(email),
          is_liked:context_likes(user_id)
        `)
        .eq('is_public', true)
        .eq('status', 'approved')
        .order('is_featured', { ascending: false })
        .order('usage_count', { ascending: false })

      if (categoryId) {
        query = query.eq('category_id', categoryId)
      }

      const { data, error } = await query

      if (error) {
        console.error('Template fetch error:', error)
        throw new Error(`Template yükleme hatası: ${error.message}`)
      }

      return data || []
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000)
  })
}

// ✅ Component with proper loading states
function ContextGallery() {
  const { data: templates, isLoading, error, refetch } = useContextTemplates()

  if (isLoading) {
    return <ContextGallerySkeleton />
  }

  if (error) {
    return (
      <ErrorState 
        title="Template'ler yüklenemedi"
        description={error.message}
        onRetry={refetch}
      />
    )
  }

  if (!templates?.length) {
    return (
      <EmptyState 
        title="Henüz template yok"
        description="İlk template'i siz ekleyin"
        action={<CreateTemplateButton />}
      />
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {templates.map(template => (
        <ContextTemplateCard key={template.id} template={template} />
      ))}
    </div>
  )
}
```

### Issue 3: Admin Approval Not Working
**Symptoms**: Templates stuck in pending state, approval actions fail
**Priority**: Medium (Admin functionality)

#### Solution Patterns
```typescript
// ✅ Robust admin approval system
const useTemplateApproval = () => {
  const approveTemplate = useMutation({
    mutationFn: async ({ templateId, notes }: { templateId: string, notes?: string }) => {
      // Check admin permissions
      const { data: isAdmin } = await checkIsAdmin()
      if (!isAdmin) {
        throw new Error('Admin yetkisi gerekli')
      }

      const { data, error } = await supabase
        .from('context_templates')
        .update({
          status: 'approved',
          is_public: true,
          approved_by: adminUserId,
          approved_at: new Date().toISOString(),
          approval_notes: notes
        })
        .eq('id', templateId)
        .select()
        .single()

      if (error) throw error

      // Notify template author
      await notifyTemplateAuthor(templateId, 'approved', notes)

      return data
    },
    onSuccess: () => {
      toast.success('Template onaylandı')
      queryClient.invalidateQueries({ queryKey: ['admin-contexts'] })
    },
    onError: (error) => {
      console.error('Approval error:', error)
      toast.error('Onaylama hatası: ' + error.message)
    }
  })

  return { approveTemplate }
}
```

### Issue 4: Search Not Working
**Symptoms**: Search returns no results or incorrect results
**Priority**: Medium (User experience)

#### Solution Patterns
```typescript
// ✅ Comprehensive search implementation
const useContextSearch = () => {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<ContextTemplate[]>([])

  const searchTemplates = useMutation({
    mutationFn: async (query: string) => {
      if (!query.trim()) return []

      // Client-side search for immediate feedback
      const clientResults = templates.filter(template =>
        template.title.toLowerCase().includes(query.toLowerCase()) ||
        template.description.toLowerCase().includes(query.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
      )

      // Server-side search for comprehensive results
      const { data: serverResults, error } = await supabase
        .from('context_templates')
        .select('*')
        .textSearch('title,description,content', query)
        .eq('is_public', true)
        .eq('status', 'approved')

      if (error) {
        console.error('Search error:', error)
        return clientResults // Fallback to client results
      }

      // Combine and deduplicate results
      const combined = [...clientResults, ...serverResults]
      const unique = combined.filter((template, index, self) =>
        index === self.findIndex(t => t.id === template.id)
      )

      return unique
    },
    onSuccess: (results) => {
      setSearchResults(results)
    }
  })

  return {
    searchQuery,
    setSearchQuery,
    searchResults,
    searchTemplates: searchTemplates.mutate,
    isSearching: searchTemplates.isPending
  }
}
```

## Performance Issues

### Issue 5: Slow Loading
**Symptoms**: Gallery takes long time to load
**Priority**: Medium (Performance)

#### Optimization Strategies
```typescript
// ✅ Performance optimizations
1. Implement pagination
2. Use virtual scrolling for large lists
3. Optimize images with Next.js Image
4. Implement proper caching
5. Use React.memo for components

// ✅ Pagination implementation
const useContextTemplatesPaginated = (categoryId?: string, pageSize = 12) => {
  const [page, setPage] = useState(0)

  return useInfiniteQuery({
    queryKey: ['context-templates-paginated', categoryId],
    queryFn: async ({ pageParam = 0 }) => {
      const from = pageParam * pageSize
      const to = from + pageSize - 1

      let query = supabase
        .from('context_templates')
        .select('*')
        .eq('is_public', true)
        .eq('status', 'approved')
        .range(from, to)
        .order('created_at', { ascending: false })

      if (categoryId) {
        query = query.eq('category_id', categoryId)
      }

      const { data, error } = await query
      if (error) throw error

      return {
        data: data || [],
        nextPage: data && data.length === pageSize ? pageParam + 1 : undefined
      }
    },
    getNextPageParam: (lastPage) => lastPage.nextPage,
    initialPageParam: 0
  })
}
```

## Database Issues

### Issue 6: RLS Policy Problems
**Symptoms**: Permission denied errors, templates not visible
**Priority**: High (Security & Functionality)

#### Solution Patterns
```sql
-- ✅ Correct RLS policies for context gallery
-- Public templates visible to all authenticated users
CREATE POLICY "Public approved templates visible" ON context_templates
  FOR SELECT USING (
    is_public = true 
    AND status = 'approved' 
    AND auth.role() = 'authenticated'
  );

-- Users can see their own templates regardless of status
CREATE POLICY "Users see own templates" ON context_templates
  FOR SELECT USING (auth.uid() = author_id);

-- Users can create templates
CREATE POLICY "Users can create templates" ON context_templates
  FOR INSERT WITH CHECK (
    auth.uid() = author_id 
    AND auth.role() = 'authenticated'
  );

-- Users can update their own templates (if not approved yet)
CREATE POLICY "Users update own pending templates" ON context_templates
  FOR UPDATE USING (
    auth.uid() = author_id 
    AND status = 'pending'
  );

-- Admins can do everything
CREATE POLICY "Admins full access" ON context_templates
  FOR ALL USING (
    EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
  );
```

## Troubleshooting Checklist

### Quick Diagnostic Steps
1. **Check Authentication**: `console.log(await supabase.auth.getUser())`
2. **Check RLS Policies**: Test with admin user vs regular user
3. **Check Network**: Open browser dev tools, check for 400/500 errors
4. **Check Database**: Verify data exists in Supabase dashboard
5. **Check Permissions**: Verify user plan and limits
6. **Check State**: Log React state and props
7. **Check Console**: Look for JavaScript errors

### Common Error Messages & Solutions
- `"Permission denied"` → Check RLS policies
- `"Cannot read property of undefined"` → Add null checks
- `"Network error"` → Check Supabase connection
- `"Plan limit exceeded"` → Check user limits
- `"Template not found"` → Verify template exists and is approved

## Update Requirements
- Add new error patterns as they are discovered
- Update solutions when new features are added
- Maintain troubleshooting checklist
- Document performance optimizations
- Keep RLS policy examples current
- Update diagnostic procedures for new tools
