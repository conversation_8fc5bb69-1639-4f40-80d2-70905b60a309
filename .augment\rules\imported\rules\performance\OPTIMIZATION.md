---
type: "manual"
---

# PromptFlow Performance Optimization Rules

## Frontend Optimization
### React Performance
```typescript
// Component memoization
const ExpensiveComponent = memo(({ data }) => {
  const processedData = useMemo(() => 
    heavyProcessing(data), [data]
  )
  
  const handleClick = useCallback((id) => 
    onItemClick(id), [onItemClick]
  )
  
  return <div>{/* component */}</div>
})

// Virtualization for large lists
import { FixedSizeList as List } from 'react-window'

<List
  height={600}
  itemCount={prompts.length}
  itemSize={80}
  itemData={prompts}
>
  {PromptItem}
</List>
```

### Bundle Optimization
```typescript
// Dynamic imports
const HeavyComponent = lazy(() => import('./HeavyComponent'))
const ChartLibrary = lazy(() => import('recharts'))

// Code splitting by routes
const AdminDashboard = lazy(() => import('@/app/dashboard/page'))

// Conditional loading
const loadFeature = async (featureName: string) => {
  switch (featureName) {
    case 'charts':
      return import('./charts')
    case 'editor':
      return import('./editor')
  }
}
```

### Query Optimization
```typescript
// TanStack Query optimization
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,      // 5 minutes
      gcTime: 15 * 60 * 1000,        // 15 minutes
      refetchOnWindowFocus: false,
      retry: 2
    }
  }
})

// Optimistic updates
const updatePromptMutation = useMutation({
  mutationFn: updatePrompt,
  onMutate: async (newData) => {
    await queryClient.cancelQueries(['prompts'])
    const previousData = queryClient.getQueryData(['prompts'])
    queryClient.setQueryData(['prompts'], newData)
    return { previousData }
  },
  onError: (err, newData, context) => {
    queryClient.setQueryData(['prompts'], context.previousData)
  }
})
```

## Backend Optimization
### Database Performance
```sql
-- Composite indexes for common queries
CREATE INDEX idx_prompts_project_smart_sort 
ON prompts(project_id, is_used, order_index);

CREATE INDEX idx_prompts_user_project 
ON prompts(user_id, project_id);

CREATE INDEX idx_prompts_tags_gin 
ON prompts USING gin(tags);

-- Materialized views for analytics
CREATE MATERIALIZED VIEW prompt_analytics AS
SELECT 
  project_id,
  COUNT(*) as total_prompts,
  COUNT(*) FILTER (WHERE is_used) as used_prompts,
  AVG(usage_count) as avg_usage
FROM prompts 
GROUP BY project_id;
```

### Query Patterns
```typescript
// Efficient pagination
const { data } = await supabase
  .from('prompts')
  .select('*')
  .eq('project_id', projectId)
  .order('order_index')
  .range(offset, offset + limit - 1)

// Batch operations
const batchUpdate = await supabase
  .from('prompts')
  .update({ is_used: true })
  .in('id', promptIds)
```

## Monitoring & Analytics
### Performance Monitoring
```typescript
// Core Web Vitals tracking
class PerformanceMonitor {
  trackWebVitals() {
    // FCP, LCP, CLS, FID tracking
    new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        this.sendMetric(entry.name, entry.value)
      })
    }).observe({ entryTypes: ['paint', 'layout-shift'] })
  }

  trackRouteChange(route: string) {
    const startTime = performance.now()
    return () => {
      const duration = performance.now() - startTime
      this.sendMetric('route-change', duration, { route })
    }
  }
}
```

### Bundle Analysis
```bash
# Analyze bundle size
ANALYZE=true npm run build

# Performance testing
npm run test:performance
npm run test:virtualization
```

## Caching Strategies
### Browser Caching
```typescript
// Service Worker for offline support
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.register('/sw.js')
}

// Cache API responses
const cacheFirst = async (request: Request) => {
  const cache = await caches.open('api-cache')
  const cached = await cache.match(request)
  return cached || fetch(request)
}
```

### Memory Management
```typescript
// Cleanup subscriptions
useEffect(() => {
  const subscription = supabase
    .channel('prompts')
    .on('postgres_changes', handleChange)
    .subscribe()

  return () => {
    subscription.unsubscribe()
  }
}, [])

// Debounce expensive operations
const debouncedSearch = useDebounce(searchTerm, 300)
const debouncedSave = useDebounce(saveData, 1000)
```

## Performance Targets
### Metrics Goals
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Time to Interactive**: < 3.5s
- **Cumulative Layout Shift**: < 0.1
- **Bundle Size**: < 250KB gzipped

### API Performance
- **Database queries**: < 100ms
- **API responses**: < 300ms
- **Real-time updates**: < 200ms
- **File uploads**: < 2s for 1MB

## Image Optimization
```typescript
// Next.js Image optimization
import Image from 'next/image'

<Image
  src="/avatar.jpg"
  alt="User avatar"
  width={40}
  height={40}
  priority={true}
  placeholder="blur"
/>

// Dynamic image loading
const loadImage = (src: string) => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve(img)
    img.onerror = reject
    img.src = src
  })
}
```

## Update Requirements
- Update this file when implementing new optimizations
- Document performance regressions and fixes
- Maintain monitoring setup
- Keep performance targets current
- Update caching strategies when adding new features
- Document new performance monitoring tools
