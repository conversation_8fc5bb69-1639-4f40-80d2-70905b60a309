'use client'

import { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Card,
  CardBody,
  CardHeader,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Badge,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  SimpleGrid,
  Progress,
  Flex,
  Spacer,
  IconButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  FormControl,
  FormLabel,
  Select,
  Input,
} from '@chakra-ui/react'
import { 
  Crown, 
  Users, 
  TrendingUp, 
  DollarSign, 
  MoreVertical,
  Edit,
  Trash2,
  Plus,
  Download,
  Filter
} from 'lucide-react'

interface PlanStats {
  plan_name: string
  display_name: string
  active_users: number
  total_revenue: number
}

interface UserPlan {
  id: string
  user_id: string
  user_email: string
  plan_name: string
  plan_display_name: string
  status: string
  billing_cycle: string
  started_at: string
  expires_at: string | null
  auto_renew: boolean
  created_at: string
}

export default function PlansPage() {
  const [planStats, setPlanStats] = useState<PlanStats[]>([])
  const [userPlans, setUserPlans] = useState<UserPlan[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedPlan, setSelectedPlan] = useState<UserPlan | null>(null)
  const { isOpen, onOpen, onClose } = useDisclosure()
  const toast = useToast()

  useEffect(() => {
    fetchPlanData()
  }, [])

  const fetchPlanData = async () => {
    try {
      setLoading(true)
      
      // Plan istatistiklerini getir
      const statsResponse = await fetch('/api/admin/plans/stats')
      if (!statsResponse.ok) throw new Error('Plan istatistikleri alınamadı')
      const stats = await statsResponse.json()
      setPlanStats(stats)

      // Kullanıcı planlarını getir
      const plansResponse = await fetch('/api/admin/plans/users')
      if (!plansResponse.ok) throw new Error('Kullanıcı planları alınamadı')
      const plans = await plansResponse.json()
      setUserPlans(plans)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Veri yüklenirken hata oluştu')
      toast({
        title: 'Hata',
        description: err instanceof Error ? err.message : 'Veri yüklenirken hata oluştu',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    } finally {
      setLoading(false)
    }
  }

  const handleChangePlan = async (userId: string, newPlanName: string) => {
    try {
      const response = await fetch('/api/admin/plans/change', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, planName: newPlanName })
      })

      if (!response.ok) throw new Error('Plan değiştirilemedi')

      toast({
        title: 'Başarılı',
        description: 'Plan başarıyla değiştirildi',
        status: 'success',
        duration: 3000,
        isClosable: true,
      })

      fetchPlanData() // Verileri yenile
    } catch (err) {
      toast({
        title: 'Hata',
        description: err instanceof Error ? err.message : 'Plan değiştirilemedi',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    }
  }

  const getTotalRevenue = () => {
    return planStats.reduce((total, plan) => total + plan.total_revenue, 0)
  }

  const getTotalUsers = () => {
    return planStats.reduce((total, plan) => total + plan.active_users, 0)
  }

  const getPlanColor = (planName: string) => {
    switch (planName) {
      case 'free': return 'blue'
      case 'professional': return 'yellow'
      case 'enterprise': return 'purple'
      default: return 'gray'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green'
      case 'cancelled': return 'red'
      case 'expired': return 'orange'
      case 'suspended': return 'red'
      default: return 'gray'
    }
  }

  if (loading) {
    return (
      <Container maxW="7xl" py={8}>
        <VStack spacing={8} align="center" justify="center" minH="400px">
          <Spinner size="xl" color="blue.500" />
          <Text>Plan verileri yükleniyor...</Text>
        </VStack>
      </Container>
    )
  }

  if (error) {
    return (
      <Container maxW="7xl" py={8}>
        <Alert status="error">
          <AlertIcon />
          <AlertTitle>Hata!</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </Container>
    )
  }

  return (
    <Container maxW="7xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Box>
          <Heading size="lg" mb={2}>Kullanıcı Planları Yönetimi</Heading>
          <Text color="gray.600">
            Kullanıcı planlarını görüntüleyin, düzenleyin ve istatistikleri takip edin
          </Text>
        </Box>

        {/* Stats Cards */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
          <Card>
            <CardBody>
              <Stat>
                <StatLabel>Toplam Kullanıcı</StatLabel>
                <StatNumber>{getTotalUsers()}</StatNumber>
                <StatHelpText>
                  <StatArrow type="increase" />
                  Aktif planlar
                </StatHelpText>
              </Stat>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Stat>
                <StatLabel>Aylık Gelir</StatLabel>
                <StatNumber>₺{getTotalRevenue().toLocaleString()}</StatNumber>
                <StatHelpText>
                  <StatArrow type="increase" />
                  Bu ay
                </StatHelpText>
              </Stat>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Stat>
                <StatLabel>Profesyonel Plan</StatLabel>
                <StatNumber>
                  {planStats.find(p => p.plan_name === 'professional')?.active_users || 0}
                </StatNumber>
                <StatHelpText>Aktif kullanıcı</StatHelpText>
              </Stat>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Stat>
                <StatLabel>Kurumsal Plan</StatLabel>
                <StatNumber>
                  {planStats.find(p => p.plan_name === 'enterprise')?.active_users || 0}
                </StatNumber>
                <StatHelpText>Aktif kullanıcı</StatHelpText>
              </Stat>
            </CardBody>
          </Card>
        </SimpleGrid>

        {/* Tabs */}
        <Tabs>
          <TabList>
            <Tab>Plan İstatistikleri</Tab>
            <Tab>Kullanıcı Planları</Tab>
          </TabList>

          <TabPanels>
            {/* Plan Stats Tab */}
            <TabPanel>
              <Card>
                <CardHeader>
                  <Heading size="md">Plan Dağılımı</Heading>
                </CardHeader>
                <CardBody>
                  <VStack spacing={4} align="stretch">
                    {planStats.map((plan) => (
                      <Box key={plan.plan_name}>
                        <Flex align="center" mb={2}>
                          <Badge colorScheme={getPlanColor(plan.plan_name)} mr={3}>
                            {plan.display_name}
                          </Badge>
                          <Text fontWeight="medium">{plan.active_users} kullanıcı</Text>
                          <Spacer />
                          <Text color="gray.600">₺{plan.total_revenue.toLocaleString()}/ay</Text>
                        </Flex>
                        <Progress 
                          value={(plan.active_users / getTotalUsers()) * 100} 
                          colorScheme={getPlanColor(plan.plan_name)}
                          size="sm"
                        />
                      </Box>
                    ))}
                  </VStack>
                </CardBody>
              </Card>
            </TabPanel>

            {/* User Plans Tab */}
            <TabPanel>
              <Card>
                <CardHeader>
                  <Flex align="center">
                    <Heading size="md">Kullanıcı Planları</Heading>
                    <Spacer />
                    <Button leftIcon={<Download />} size="sm" variant="outline">
                      Dışa Aktar
                    </Button>
                  </Flex>
                </CardHeader>
                <CardBody>
                  <TableContainer>
                    <Table variant="simple">
                      <Thead>
                        <Tr>
                          <Th>Kullanıcı</Th>
                          <Th>Plan</Th>
                          <Th>Durum</Th>
                          <Th>Faturalama</Th>
                          <Th>Başlangıç</Th>
                          <Th>Bitiş</Th>
                          <Th>İşlemler</Th>
                        </Tr>
                      </Thead>
                      <Tbody>
                        {userPlans.map((userPlan) => (
                          <Tr key={userPlan.id}>
                            <Td>
                              <Text fontWeight="medium">{userPlan.user_email}</Text>
                            </Td>
                            <Td>
                              <Badge colorScheme={getPlanColor(userPlan.plan_name)}>
                                {userPlan.plan_display_name}
                              </Badge>
                            </Td>
                            <Td>
                              <Badge colorScheme={getStatusColor(userPlan.status)}>
                                {userPlan.status}
                              </Badge>
                            </Td>
                            <Td>{userPlan.billing_cycle}</Td>
                            <Td>{new Date(userPlan.started_at).toLocaleDateString('tr-TR')}</Td>
                            <Td>
                              {userPlan.expires_at 
                                ? new Date(userPlan.expires_at).toLocaleDateString('tr-TR')
                                : 'Sınırsız'
                              }
                            </Td>
                            <Td>
                              <Menu>
                                <MenuButton
                                  as={IconButton}
                                  icon={<MoreVertical />}
                                  variant="ghost"
                                  size="sm"
                                />
                                <MenuList>
                                  <MenuItem 
                                    icon={<Edit />}
                                    onClick={() => {
                                      setSelectedPlan(userPlan)
                                      onOpen()
                                    }}
                                  >
                                    Düzenle
                                  </MenuItem>
                                  <MenuItem 
                                    icon={<Trash2 />}
                                    color="red.500"
                                  >
                                    İptal Et
                                  </MenuItem>
                                </MenuList>
                              </Menu>
                            </Td>
                          </Tr>
                        ))}
                      </Tbody>
                    </Table>
                  </TableContainer>
                </CardBody>
              </Card>
            </TabPanel>
          </TabPanels>
        </Tabs>
      </VStack>

      {/* Edit Plan Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Plan Düzenle</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedPlan && (
              <VStack spacing={4}>
                <FormControl>
                  <FormLabel>Kullanıcı</FormLabel>
                  <Input value={selectedPlan.user_email} isReadOnly />
                </FormControl>
                <FormControl>
                  <FormLabel>Yeni Plan</FormLabel>
                  <Select placeholder="Plan seçin">
                    <option value="free">Ücretsiz</option>
                    <option value="professional">Profesyonel</option>
                    <option value="enterprise">Kurumsal</option>
                  </Select>
                </FormControl>
              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              İptal
            </Button>
            <Button colorScheme="blue">
              Kaydet
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Container>
  )
}
