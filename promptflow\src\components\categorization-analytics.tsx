'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import {
  <PERSON><PERSON>hart<PERSON>,
  <PERSON><PERSON>,
  Fold<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Eye,
  EyeOff,
  Target
} from 'lucide-react'
import { usePopularHashtags, usePopularCategories } from '@/hooks/use-hashtags'
import { usePrompts } from '@/hooks/use-prompts'

interface CategorizationAnalyticsProps {
  projectId: string | null
  className?: string
}

export function CategorizationAnalytics({ 
  projectId, 
  className 
}: CategorizationAnalyticsProps) {
  const [isVisible, setIsVisible] = useState(false)
  
  const { data: prompts = [] } = usePrompts(projectId)
  const { data: popularHashtags = [] } = usePopularHashtags(projectId, 10)
  const { data: popularCategories = [] } = usePopularCategories(projectId, 5)

  // Calculate statistics
  const totalPrompts = prompts.length
  const categorizedPrompts = prompts.filter(p => p.category || (p.tags && p.tags.length > 0)).length
  const uncategorizedPrompts = totalPrompts - categorizedPrompts
  const categorizationRate = totalPrompts > 0 ? (categorizedPrompts / totalPrompts) * 100 : 0

  const promptsWithHashtags = prompts.filter(p => p.tags && p.tags.length > 0).length
  const promptsWithCategories = prompts.filter(p => p.category).length

  if (!projectId) return null

  return (
    <div className={className}>
      {/* Toggle Button */}
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={() => setIsVisible(!isVisible)}
        className="mb-4"
      >
        {isVisible ? (
          <>
            <EyeOff className="w-4 h-4 mr-2" />
            Analitikleri Gizle
          </>
        ) : (
          <>
            <Eye className="w-4 h-4 mr-2" />
            Analitikleri Göster
          </>
        )}
      </Button>

      {isVisible && (
        <div className="space-y-4 max-w-full overflow-hidden relative z-50">
          {/* Overview Card */}
          <Card className="w-full relative z-50 bg-white shadow-lg border border-gray-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <BarChart3 className="w-4 h-4" />
                Kategorizasyon Genel Bakış
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Categorization Rate */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-600">Kategorizasyon Oranı</span>
                  <span className="text-sm font-medium">{categorizationRate.toFixed(1)}%</span>
                </div>
                <Progress value={categorizationRate} className="h-2" />
                <div className="text-xs text-gray-500 mt-1">
                  {totalPrompts} prompt&apos;tan {categorizedPrompts} tanesi kategori veya etiket içeriyor
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-lg font-semibold text-blue-700">{promptsWithHashtags}</div>
                  <div className="text-xs text-blue-600">Etiketli</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-lg font-semibold text-green-700">{promptsWithCategories}</div>
                  <div className="text-xs text-green-600">Klasörlü</div>
                </div>
              </div>

              {uncategorizedPrompts > 0 && (
                <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                  <div className="flex items-center gap-2 text-orange-700">
                    <Target className="w-4 h-4" />
                    <span className="text-sm font-medium">
                      {uncategorizedPrompts} prompts need categorization
                    </span>
                  </div>
                  <div className="text-xs text-orange-600 mt-1">
                    Add hashtags or folders to improve organization
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Popular Hashtags */}
          {popularHashtags.length > 0 && (
            <Card className="w-full relative z-50 bg-white shadow-lg border border-gray-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Hash className="w-4 h-4" />
                  En Popüler Etiketler
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {popularHashtags.slice(0, 5).map(({ hashtag, count }, index) => {
                    const percentage = totalPrompts > 0 ? (count / totalPrompts) * 100 : 0
                    return (
                      <div key={hashtag} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-gray-500 w-4">#{index + 1}</span>
                          <Badge variant="secondary" className="text-xs">
                            {hashtag.replace('#', '')}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-16 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-blue-500 rounded-full"
                              style={{ width: `${Math.max(percentage, 5)}%` }}
                            />
                          </div>
                          <span className="text-xs text-gray-600 w-8 text-right">{count}</span>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Popular Categories */}
          {popularCategories.length > 0 && (
            <Card className="w-full relative z-50 bg-white shadow-lg border border-gray-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Folder className="w-4 h-4" />
                  En Popüler Klasörler
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {popularCategories.map(({ category, count }, index) => {
                    const percentage = totalPrompts > 0 ? (count / totalPrompts) * 100 : 0
                    return (
                      <div key={category} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-gray-500 w-4">#{index + 1}</span>
                          <Badge variant="outline" className="text-xs">
                            <Folder className="w-3 h-3 mr-1" />
                            {category}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-16 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-green-500 rounded-full"
                              style={{ width: `${Math.max(percentage, 5)}%` }}
                            />
                          </div>
                          <span className="text-xs text-gray-600 w-8 text-right">{count}</span>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recommendations */}
          {totalPrompts > 0 && (
            <Card className="w-full relative z-50 bg-white shadow-lg border border-gray-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Öneriler
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-xs text-gray-600">
                  {categorizationRate < 50 && (
                    <div className="flex items-start gap-2">
                      <span className="text-orange-500">•</span>
                      <span>Aranabilirliği artırmak için etiket eklemeyi düşünün</span>
                    </div>
                  )}
                  {promptsWithCategories === 0 && (
                    <div className="flex items-start gap-2">
                      <span className="text-blue-500">•</span>
                      <span>Proje alanlarına göre organize etmek için klasörler kullanın (örn: /frontend, /backend)</span>
                    </div>
                  )}
                  {popularHashtags.length > 10 && (
                    <div className="flex items-start gap-2">
                      <span className="text-green-500">•</span>
                      <span>Harika etiket kullanımı! Benzer etiketleri birleştirmeyi düşünün</span>
                    </div>
                  )}
                  {totalPrompts > 20 && categorizationRate > 80 && (
                    <div className="flex items-start gap-2">
                      <span className="text-green-500">•</span>
                      <span>Mükemmel organizasyon! Prompt&apos;larınız iyi kategorize edilmiş</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  )
}
