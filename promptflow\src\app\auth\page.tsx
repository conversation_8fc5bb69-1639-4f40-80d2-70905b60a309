'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useSignInWithEmail, useSignUpWithEmail, useUser } from '@/hooks/use-auth'
import { AlertCircle, Loader2, ArrowLeft, Eye, EyeOff } from 'lucide-react'
import Link from 'next/link'

type AuthMode = 'signin' | 'signup' | 'forgot'

export default function AuthPage() {
  const [mode, setMode] = useState<AuthMode>('signin')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  
  const router = useRouter()
  const { data: user, isLoading: userLoading } = useUser()
  const signInMutation = useSignInWithEmail()
  const signUpMutation = useSignUpWithEmail()

  const isLoading = signInMutation.isPending || signUpMutation.isPending

  // Redirect if already authenticated
  useEffect(() => {
    if (!userLoading && user) {
      console.log(`🚀 [AUTH_PAGE] User already authenticated, redirecting to dashboard`)
      router.replace('/dashboard')
    }
  }, [user, userLoading, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setSuccess('')

    if (!email || !password) {
      setError('Lütfen tüm alanları doldurun')
      return
    }

    if (mode === 'signup' && password !== confirmPassword) {
      setError('Şifreler eşleşmiyor')
      return
    }

    if (mode === 'signup' && password.length < 6) {
      setError('Şifre en az 6 karakter olmalıdır')
      return
    }

    try {
      if (mode === 'signup') {
        await signUpMutation.mutateAsync({ email, password })
        setSuccess('Hesabınız oluşturuldu! E-posta adresinizi kontrol edin.')
      } else if (mode === 'signin') {
        await signInMutation.mutateAsync({ email, password })
        console.log(`✅ [AUTH_PAGE] Sign in successful, redirect will be handled by auth state listener`)
        // Redirect will be handled by auth state listener in use-auth.ts
      } else if (mode === 'forgot') {
        // TODO: Implement password reset
        setSuccess('Şifre sıfırlama e-postası gönderildi!')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Bir hata oluştu')
    }
  }

  const resetForm = () => {
    setEmail('')
    setPassword('')
    setConfirmPassword('')
    setError('')
    setSuccess('')
    setShowPassword(false)
  }

  const switchMode = (newMode: AuthMode) => {
    setMode(newMode)
    resetForm()
  }

  if (userLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Yükleniyor...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex flex-col">
      {/* Header */}
      <header className="p-4 sm:p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors">
            <ArrowLeft className="h-5 w-5" />
            <span>Ana Sayfaya Dön</span>
          </Link>
          <div className="flex items-center space-x-2">
            <img
              src="/logo.png"
              alt="Promptbir Logo"
              className="h-8 w-auto"
            />
            <span className="text-xl font-bold text-gray-900">Promptbir</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="space-y-1 text-center">
              <CardTitle className="text-2xl font-bold">
                {mode === 'signin' && 'Giriş Yap'}
                {mode === 'signup' && 'Hesap Oluştur'}
                {mode === 'forgot' && 'Şifre Sıfırla'}
              </CardTitle>
              <CardDescription>
                {mode === 'signin' && 'Hesabınıza giriş yapın'}
                {mode === 'signup' && 'Promptbir\'a katılın ve prompt\'larınızı yönetin'}
                {mode === 'forgot' && 'E-posta adresinizi girin, size şifre sıfırlama bağlantısı gönderelim'}
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">E-posta</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isLoading}
                    className="h-11"
                    autoComplete="email"
                    required
                  />
                </div>
                
                {mode !== 'forgot' && (
                  <div className="space-y-2">
                    <Label htmlFor="password">Şifre</Label>
                    <div className="relative">
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        placeholder="••••••••"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        disabled={isLoading}
                        className="h-11 pr-10"
                        autoComplete={mode === 'signup' ? "new-password" : "current-password"}
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                        disabled={isLoading}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-gray-400" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-400" />
                        )}
                      </Button>
                    </div>
                  </div>
                )}

                {mode === 'signup' && (
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Şifre Tekrar</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      placeholder="••••••••"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      disabled={isLoading}
                      className="h-11"
                      autoComplete="new-password"
                      required
                    />
                  </div>
                )}

                {error && (
                  <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
                    <AlertCircle className="h-4 w-4 text-red-600 flex-shrink-0" />
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                )}

                {success && (
                  <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-md">
                    <div className="h-4 w-4 bg-green-600 rounded-full flex-shrink-0"></div>
                    <p className="text-sm text-green-700">{success}</p>
                  </div>
                )}

                <Button 
                  type="submit" 
                  className="w-full h-11 bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700"
                  disabled={isLoading}
                >
                  {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {mode === 'signin' && 'Giriş Yap'}
                  {mode === 'signup' && 'Hesap Oluştur'}
                  {mode === 'forgot' && 'Şifre Sıfırlama Bağlantısı Gönder'}
                </Button>
              </form>

              <div className="space-y-2 text-center text-sm">
                {mode === 'signin' && (
                  <>
                    <button
                      type="button"
                      onClick={() => switchMode('forgot')}
                      className="text-blue-600 hover:text-blue-700 underline"
                      disabled={isLoading}
                    >
                      Şifrenizi mi unuttunuz?
                    </button>
                    <div>
                      <span className="text-gray-600">Hesabınız yok mu? </span>
                      <button
                        type="button"
                        onClick={() => switchMode('signup')}
                        className="text-blue-600 hover:text-blue-700 underline font-medium"
                        disabled={isLoading}
                      >
                        Hesap oluşturun
                      </button>
                    </div>
                  </>
                )}
                
                {mode === 'signup' && (
                  <div>
                    <span className="text-gray-600">Zaten hesabınız var mı? </span>
                    <button
                      type="button"
                      onClick={() => switchMode('signin')}
                      className="text-blue-600 hover:text-blue-700 underline font-medium"
                      disabled={isLoading}
                    >
                      Giriş yapın
                    </button>
                  </div>
                )}
                
                {mode === 'forgot' && (
                  <div>
                    <span className="text-gray-600">Şifrenizi hatırladınız mı? </span>
                    <button
                      type="button"
                      onClick={() => switchMode('signin')}
                      className="text-blue-600 hover:text-blue-700 underline font-medium"
                      disabled={isLoading}
                    >
                      Giriş yapın
                    </button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
