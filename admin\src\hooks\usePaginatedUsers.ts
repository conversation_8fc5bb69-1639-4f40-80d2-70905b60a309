'use client';

import { useInfiniteQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { queryKeys } from '@/lib/query-client';
import { useToast } from '@chakra-ui/react';

/**
 * User data structure for admin panel
 */
export interface PaginatedUser {
  id: string;
  user_id: string;
  role: 'admin' | 'super_admin';
  permissions: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  email: string;
  last_sign_in_at: string | null;
  email_confirmed_at: string | null;
  projects_count: number;
  prompts_count: number;
}

/**
 * Pagination options for user queries
 */
export interface UserPaginationOptions {
  pageSize?: number;
  search?: string;
  role?: string;
  status?: string;
  sortBy?: 'created_at' | 'email' | 'last_sign_in_at';
  sortOrder?: 'asc' | 'desc';
}

/**
 * API response structure for paginated users
 */
interface PaginatedUsersResponse {
  data: PaginatedUser[];
  count: number;
  hasMore: boolean;
  nextCursor?: number;
}

/**
 * Fetches paginated users from the admin_users_view with optimized performance
 * 
 * @param options - Pagination and filtering options
 * @returns Promise<PaginatedUsersResponse>
 */
const fetchPaginatedUsers = async (
  options: UserPaginationOptions & { pageParam?: number }
): Promise<PaginatedUsersResponse> => {
  const {
    pageParam = 0,
    pageSize = 50,
    search,
    role,
    status,
    sortBy = 'created_at',
    sortOrder = 'desc'
  } = options;

  const from = pageParam * pageSize;
  const to = from + pageSize - 1;

  // Build query with optimized view
  let query = supabase
    .from('admin_users_view')
    .select('*', { count: 'exact' });

  // Apply search filter (email search with index optimization)
  if (search && search.trim()) {
    query = query.ilike('email', `%${search.trim()}%`);
  }

  // Apply role filter
  if (role && role !== 'all') {
    query = query.eq('role', role);
  }

  // Apply status filter
  if (status && status !== 'all') {
    query = query.eq('is_active', status === 'active');
  }

  // Apply sorting
  query = query.order(sortBy, { ascending: sortOrder === 'asc' });

  // Apply pagination range
  const { data, error, count } = await query.range(from, to);

  if (error) {
    console.error('Error fetching paginated users:', error);
    throw new Error(`Failed to fetch users: ${error.message}`);
  }

  const totalCount = count || 0;
  const hasMore = to < totalCount - 1;
  const nextCursor = hasMore ? pageParam + 1 : undefined;

  return {
    data: data || [],
    count: totalCount,
    hasMore,
    nextCursor
  };
};

/**
 * Custom hook for paginated users with infinite scroll support
 * 
 * Provides optimized data fetching with:
 * - Infinite scroll pagination
 * - Search and filtering capabilities
 * - Optimistic updates
 * - Error handling and retry logic
 * - Memory-efficient data management
 * 
 * @param options - Pagination and filtering options
 * @returns Infinite query result with user data and pagination controls
 */
export function usePaginatedUsers(options: UserPaginationOptions = {}) {
  const queryClient = useQueryClient();
  const toast = useToast();

  const {
    pageSize = 50,
    search,
    role,
    status,
    sortBy = 'created_at',
    sortOrder = 'desc'
  } = options;

  return useInfiniteQuery({
    queryKey: queryKeys.users.infinite({ 
      pageSize, 
      search, 
      role, 
      status, 
      sortBy, 
      sortOrder 
    }),
    
    queryFn: ({ pageParam = 0 }) => 
      fetchPaginatedUsers({ 
        ...options, 
        pageParam 
      }),
    
    getNextPageParam: (lastPage) => lastPage.nextCursor,
    
    // Performance optimizations
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    
    // Keep previous data while loading new filters
    placeholderData: (previousData) => previousData,
    
    // Retry configuration
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (error.message.includes('4')) return false;
      return failureCount < 2;
    },
    
    // Error handling
    onError: (error) => {
      console.error('Error in usePaginatedUsers:', error);
      toast({
        title: 'Kullanıcılar Yüklenemedi',
        description: 'Kullanıcı listesi yüklenirken bir hata oluştu. Lütfen tekrar deneyin.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    },
  });
}

/**
 * Hook for updating user status with optimistic updates
 */
export function useUpdateUserStatus() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: async ({ userId, isActive }: { userId: string; isActive: boolean }) => {
      const { data, error } = await supabase
        .from('admin_users')
        .update({ is_active: isActive, updated_at: new Date().toISOString() })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },

    // Optimistic update
    onMutate: async ({ userId, isActive }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.users.infinite({}) });

      // Snapshot previous value
      const previousData = queryClient.getQueriesData({ 
        queryKey: queryKeys.users.infinite({}) 
      });

      // Optimistically update
      queryClient.setQueriesData(
        { queryKey: queryKeys.users.infinite({}) },
        (old: any) => {
          if (!old?.pages) return old;
          
          return {
            ...old,
            pages: old.pages.map((page: any) => ({
              ...page,
              data: page.data.map((user: PaginatedUser) =>
                user.user_id === userId
                  ? { ...user, is_active: isActive }
                  : user
              ),
            })),
          };
        }
      );

      return { previousData };
    },

    onError: (error, variables, context) => {
      // Revert optimistic update
      if (context?.previousData) {
        context.previousData.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }

      toast({
        title: 'Güncelleme Başarısız',
        description: 'Kullanıcı durumu güncellenirken bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    },

    onSuccess: () => {
      toast({
        title: 'Başarılı',
        description: 'Kullanıcı durumu başarıyla güncellendi.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    },

    onSettled: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: queryKeys.users.infinite({}) });
    },
  });
}

/**
 * Hook for bulk user operations
 */
export function useBulkUserOperations() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: async ({ 
      userIds, 
      operation, 
      value 
    }: { 
      userIds: string[]; 
      operation: 'activate' | 'deactivate' | 'delete';
      value?: any;
    }) => {
      if (operation === 'delete') {
        const { error } = await supabase
          .from('admin_users')
          .delete()
          .in('user_id', userIds);
        
        if (error) throw error;
      } else {
        const isActive = operation === 'activate';
        const { error } = await supabase
          .from('admin_users')
          .update({ is_active: isActive, updated_at: new Date().toISOString() })
          .in('user_id', userIds);
        
        if (error) throw error;
      }

      return { userIds, operation };
    },

    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.users.infinite({}) });
      
      toast({
        title: 'Toplu İşlem Başarılı',
        description: `${data.userIds.length} kullanıcı için ${data.operation} işlemi tamamlandı.`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    },

    onError: (error) => {
      toast({
        title: 'Toplu İşlem Başarısız',
        description: 'Toplu işlem sırasında bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    },
  });
}
