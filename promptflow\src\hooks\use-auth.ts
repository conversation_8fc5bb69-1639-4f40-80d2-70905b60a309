'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabaseBrowser as supabase } from '@/lib/supabase-browser'
import { User } from '@supabase/supabase-js'
import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

// Auth error handling
function handleAuthError(error: Error | unknown, queryClient: ReturnType<typeof useQueryClient>) {
  const errorMessage = error instanceof Error ? error.message : String(error)
  
  if (errorMessage.includes('Invalid Refresh Token') || 
      errorMessage.includes('Refresh Token Not Found') ||
      errorMessage.includes('refresh_token_not_found')) {
    // Refresh token hatası durumunda oturumu temizle
    console.warn('Refresh token hatası, oturum temizleniyor:', errorMessage)
    supabase.auth.signOut({ scope: 'local' })
    queryClient.clear()
    return true
  }
  return false
}

// Auth state listener with enhanced logout handling
export function useAuthStateListener() {
  const queryClient = useQueryClient()
  const router = useRouter()

  useEffect(() => {
    console.log(`🎧 [AUTH_LISTENER] Setting up auth state listener`)

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log(`🔄 [AUTH_LISTENER] Auth state change: ${event}`, {
          userId: session?.user?.id,
          email: session?.user?.email,
          hasSession: !!session
        })

        if (event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED') {
          console.log(`🔄 [AUTH_LISTENER] Invalidating queries for event: ${event}`)
          queryClient.invalidateQueries({ queryKey: ['user'] })
          queryClient.invalidateQueries({ queryKey: ['session'] })
        }

        if (event === 'SIGNED_OUT') {
          console.log(`🚪 [AUTH_LISTENER] User signed out - clearing cache and redirecting`)
          // Clear all cache and redirect to login
          queryClient.clear()

          // Clear any persisted state
          localStorage.removeItem('promptflow-app-store')

          // Force redirect to auth page
          setTimeout(() => {
            console.log(`🚪 [AUTH_LISTENER] Redirecting to /auth`)
            router.push('/auth')
            router.refresh()
          }, 100)
        }

        if (event === 'SIGNED_IN') {
          console.log(`🔑 [AUTH_LISTENER] User signed in - refreshing queries and redirecting`)
          // Refresh queries when user signs in
          queryClient.invalidateQueries({ queryKey: ['user'] })
          queryClient.invalidateQueries({ queryKey: ['session'] })

          // Redirect to dashboard after successful login
          setTimeout(() => {
            const currentPath = window.location.pathname
            if (currentPath === '/auth' || currentPath === '/') {
              console.log(`🚀 [AUTH_LISTENER] Redirecting from ${currentPath} to /dashboard`)
              router.push('/dashboard')
            }
          }, 100)
        }
      }
    )

    return () => {
      console.log(`🎧 [AUTH_LISTENER] Cleaning up auth state listener`)
      subscription.unsubscribe()
    }
  }, [queryClient, router])
}

// Kullanıcı bilgilerini getir
export function useUser() {
  const queryClient = useQueryClient()

  return useQuery({
    queryKey: ['user'],
    queryFn: async (): Promise<User | null> => {
      console.log(`🔐 [USE_USER] Getting user...`)
      try {
        // Önce session'ı kontrol et
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          console.error(`❌ [USE_USER] Session error:`, sessionError)
          return null
        }

        if (!session) {
          console.log(`🔐 [USE_USER] No session found`)
          return null
        }

        console.log(`🔐 [USE_USER] Session found, getting user...`)
        const { data: { user }, error } = await supabase.auth.getUser()

        if (error) {
          console.error(`❌ [USE_USER] Error getting user:`, error)
          // Auth session missing error'ını handle et
          if (error.message.includes('Auth session missing')) {
            console.log(`🔄 [USE_USER] Auth session missing, returning null`)
            return null
          }
          // Refresh token hatası kontrolü
          if (handleAuthError(error, queryClient)) {
            console.log(`🔄 [USE_USER] Handled auth error, returning null`)
            return null
          }
          throw new Error(error.message)
        }

        console.log(`✅ [USE_USER] User retrieved:`, user?.email || 'null')
        return user
      } catch (error: unknown) {
        console.error(`💥 [USE_USER] Exception:`, error)
        const errorMessage = error instanceof Error ? error.message : String(error)

        // Auth session missing error'ını handle et
        if (errorMessage.includes('Auth session missing')) {
          console.log(`🔄 [USE_USER] Auth session missing exception, returning null`)
          return null
        }

        // Refresh token hatası kontrolü
        if (handleAuthError(error, queryClient)) {
          console.log(`🔄 [USE_USER] Handled auth error exception, returning null`)
          return null
        }
        throw error
      }
    },
    staleTime: 5 * 60 * 1000, // 5 dakika
    retry: (failureCount, error: unknown) => {
      // Refresh token hatalarında ve session missing'de retry yapma
      const errorMessage = error instanceof Error ? error.message : String(error)
      console.log(`🔄 [USE_USER] Retry attempt ${failureCount}, error: ${errorMessage}`)
      if (errorMessage.includes('Invalid Refresh Token') ||
          errorMessage.includes('Refresh Token Not Found') ||
          errorMessage.includes('Auth session missing')) {
        console.log(`🚫 [USE_USER] Not retrying auth error: ${errorMessage}`)
        return false
      }
      return failureCount < 3
    },
  })
}

// Oturum durumunu kontrol et
export function useSession() {
  const queryClient = useQueryClient()
  
  return useQuery({
    queryKey: ['session'],
    queryFn: async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          // Refresh token hatası kontrolü
          if (handleAuthError(error, queryClient)) {
            return null
          }
          throw new Error(error.message)
        }

        return session
      } catch (error: unknown) {
        // Refresh token hatası kontrolü
        if (handleAuthError(error, queryClient)) {
          return null
        }
        throw error
      }
    },
    staleTime: 5 * 60 * 1000, // 5 dakika
    retry: (failureCount, error: unknown) => {
      // Refresh token hatalarında retry yapma
      const errorMessage = error instanceof Error ? error.message : String(error)
      if (errorMessage.includes('Invalid Refresh Token') || 
          errorMessage.includes('Refresh Token Not Found')) {
        return false
      }
      return failureCount < 3
    },
  })
}

// Email ile giriş yap
export function useSignInWithEmail() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ email, password }: { email: string; password: string }) => {
      console.log(`🔑 [SIGN_IN] Attempting login for: ${email}`)

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        console.error(`❌ [SIGN_IN] Login failed:`, error)
        throw new Error(error.message)
      }

      console.log(`✅ [SIGN_IN] Login successful:`, {
        userId: data.user?.id,
        email: data.user?.email,
        hasSession: !!data.session
      })

      return data
    },
    onSuccess: () => {
      console.log(`🎉 [SIGN_IN] onSuccess triggered, invalidating queries`)
      queryClient.invalidateQueries({ queryKey: ['user'] })
      queryClient.invalidateQueries({ queryKey: ['session'] })
    },
    onError: (error) => {
      console.error(`💥 [SIGN_IN] onError triggered:`, error)
    }
  })
}

// Email ile kayıt ol
export function useSignUpWithEmail() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ email, password }: { email: string; password: string }) => {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      })

      if (error) {
        throw new Error(error.message)
      }

      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user'] })
      queryClient.invalidateQueries({ queryKey: ['session'] })
    },
  })
}

// Enhanced logout with proper redirect
export function useSignOut() {
  const queryClient = useQueryClient()
  const router = useRouter()

  return useMutation({
    mutationFn: async () => {
      console.log('Starting logout process...')

      // Sign out from Supabase
      const { error } = await supabase.auth.signOut({ scope: 'global' })

      if (error) {
        console.error('Logout error:', error)
        throw new Error(error.message)
      }

      console.log('Logout successful')
    },
    onSuccess: () => {
      console.log('Logout onSuccess triggered')

      // Show success toast
      toast.success('Başarıyla çıkış yapıldı', {
        description: 'Giriş sayfasına yönlendiriliyorsunuz...'
      })

      // Clear all cache
      queryClient.clear()

      // Clear persisted state
      localStorage.removeItem('promptflow-app-store')
      sessionStorage.clear()

      // Force redirect to auth page
      setTimeout(() => {
        console.log('Redirecting to auth page...')
        router.push('/auth')
        router.refresh()
        window.location.href = '/auth' // Fallback for complete page reload
      }, 1000) // Increased delay to show toast
    },
    onError: (error) => {
      console.error('Logout failed:', error)

      // Show error toast
      toast.error('Çıkış yapılırken hata oluştu', {
        description: 'Yine de oturum temizleniyor...'
      })

      // Even if logout fails, clear local state and redirect
      queryClient.clear()
      localStorage.removeItem('promptflow-app-store')
      sessionStorage.clear()

      setTimeout(() => {
        router.push('/auth')
        router.refresh()
      }, 1000)
    }
  })
}