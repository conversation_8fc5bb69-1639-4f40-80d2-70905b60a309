'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Plus, 
  X, 
  Loader2, 
  AlertCircle, 
  Globe, 
  Lock, 
  Tag,
  Edit3
} from 'lucide-react'
import { useContextCategories, useUpdateContext } from '@/hooks/use-contexts'
import { Context } from './context-gallery'
import { toast } from 'sonner'

interface ContextEditModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  context: Context | null
  onSuccess?: () => void
}

export function ContextEditModal({ 
  open, 
  onOpenChange, 
  context,
  onSuccess 
}: ContextEditModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    category_id: '',
    is_public: false,
    is_template: false,
    tags: [] as string[]
  })
  const [newTag, setNewTag] = useState('')
  const [errors, setErrors] = useState<Record<string, string>>({})

  const { data: categories = [], isLoading: categoriesLoading } = useContextCategories()
  const updateContextMutation = useUpdateContext()

  // Initialize form data when context changes
  useEffect(() => {
    if (context) {
      setFormData({
        title: context.title,
        description: context.description || '',
        content: context.content,
        category_id: context.category.id,
        is_public: context.is_public,
        is_template: context.is_template,
        tags: context.tags || []
      })
    }
  }, [context])

  // Reset form when modal closes
  useEffect(() => {
    if (!open) {
      setErrors({})
      setNewTag('')
    }
  }, [open])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = 'Başlık gereklidir'
    }

    if (!formData.content.trim()) {
      newErrors.content = 'İçerik gereklidir'
    }

    if (!formData.category_id) {
      newErrors.category_id = 'Kategori seçimi gereklidir'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!context) return
    
    if (!validateForm()) {
      toast.error('Lütfen tüm gerekli alanları doldurun')
      return
    }

    try {
      await updateContextMutation.mutateAsync({
        id: context.id,
        updates: {
          title: formData.title.trim(),
          description: formData.description.trim() || undefined,
          content: formData.content.trim(),
          category_id: formData.category_id,
          is_public: formData.is_public,
          is_template: formData.is_template,
          tags: formData.tags
        }
      })

      toast.success('Context başarıyla güncellendi!')
      
      onSuccess?.()
      onOpenChange(false)
    } catch (error) {
      console.error('Context update error:', error)
      toast.error('Context güncellenirken bir hata oluştu')
    }
  }

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }))
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addTag()
    }
  }

  if (!context) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <Edit3 className="h-5 w-5" />
            Context Düzenle
          </DialogTitle>
          <DialogDescription>
            Context bilgilerini düzenleyin. Herkese açık contextler admin onayı gerektirir.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <form onSubmit={handleSubmit} className="space-y-6 p-1">
            {/* Title */}
            <div className="space-y-2">
              <Label htmlFor="title">
                Başlık <span className="text-red-500">*</span>
              </Label>
              <Input
                id="title"
                placeholder="Context başlığını girin..."
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                className={errors.title ? 'border-red-500' : ''}
              />
              {errors.title && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.title}
                </p>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Açıklama</Label>
              <Input
                id="description"
                placeholder="Context açıklaması (isteğe bağlı)..."
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>

            {/* Category */}
            <div className="space-y-2">
              <Label htmlFor="category">
                Kategori <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formData.category_id}
                onValueChange={(value) => setFormData(prev => ({ ...prev, category_id: value }))}
              >
                <SelectTrigger className={errors.category_id ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Kategori seçin..." />
                </SelectTrigger>
                <SelectContent>
                  {categoriesLoading ? (
                    <SelectItem value="loading" disabled>
                      Kategoriler yükleniyor...
                    </SelectItem>
                  ) : (
                    categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        <span className="flex items-center gap-2">
                          <span>{category.icon}</span>
                          {category.name}
                        </span>
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {errors.category_id && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.category_id}
                </p>
              )}
            </div>

            {/* Content */}
            <div className="space-y-2">
              <Label htmlFor="content">
                İçerik <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="content"
                placeholder="Context içeriğini girin..."
                value={formData.content}
                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                className={`min-h-[120px] resize-none ${errors.content ? 'border-red-500' : ''}`}
              />
              {errors.content && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.content}
                </p>
              )}
            </div>

            {/* Tags */}
            <div className="space-y-2">
              <Label htmlFor="tags">Etiketler</Label>
              <div className="flex gap-2">
                <Input
                  id="tags"
                  placeholder="Etiket ekle..."
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addTag}
                  disabled={!newTag.trim()}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {formData.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      <Tag className="h-3 w-3" />
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="ml-1 hover:text-red-500"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Visibility Options */}
            <div className="space-y-3">
              <Label>Görünürlük Ayarları</Label>
              <div className="space-y-2">
                <label className="flex items-center gap-3 cursor-pointer">
                  <input
                    type="radio"
                    name="visibility"
                    checked={!formData.is_public}
                    onChange={() => setFormData(prev => ({ ...prev, is_public: false }))}
                    className="w-4 h-4"
                  />
                  <div className="flex items-center gap-2">
                    <Lock className="h-4 w-4 text-gray-500" />
                    <div className="flex flex-col">
                      <span className="text-sm">Özel</span>
                      <span className="text-xs text-gray-500">Sadece ben görebilirim</span>
                    </div>
                  </div>
                </label>
                <label className="flex items-center gap-3 cursor-pointer">
                  <input
                    type="radio"
                    name="visibility"
                    checked={formData.is_public}
                    onChange={() => setFormData(prev => ({ ...prev, is_public: true }))}
                    className="w-4 h-4"
                  />
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-blue-500" />
                    <div className="flex flex-col">
                      <span className="text-sm">Herkese Açık</span>
                      <span className="text-xs text-gray-500">Tüm kullanıcılar görebilir</span>
                    </div>
                  </div>
                </label>
              </div>
            </div>

            {/* Template Option */}
            <div className="space-y-2">
              <label className="flex items-center gap-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.is_template}
                  onChange={(e) => setFormData(prev => ({ ...prev, is_template: e.target.checked }))}
                  className="w-4 h-4"
                />
                <span className="text-sm">Bu contexti şablon olarak işaretle</span>
              </label>
            </div>

            {/* Submit Buttons */}
            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="flex-1"
              >
                İptal
              </Button>
              <Button
                type="submit"
                disabled={updateContextMutation.isPending}
                className="flex-1"
              >
                {updateContextMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Güncelle
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  )
}
