'use client'

import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useCreatePrompt } from './use-prompts'
import { useProjects } from './use-projects'
import { Context } from '@/components/context-gallery'
import { toast } from 'sonner'

interface ContextToPromptOptions {
  /** Target project ID. If not provided, uses active project */
  projectId?: string
  /** Custom title for the prompt. If not provided, uses context title */
  customTitle?: string
  /** Additional tags to add to the prompt */
  additionalTags?: string[]
  /** Whether to mark as favorite */
  markAsFavorite?: boolean
  /** Custom category for the prompt */
  customCategory?: string
}

interface ContextToPromptResult {
  success: boolean
  promptId?: string
  error?: string
}

/**
 * Hook for converting Context Gallery contexts to prompts in current project
 * Handles validation, limits checking, and proper data transformation
 */
export function useContextToPrompt() {
  const queryClient = useQueryClient()
  const createPromptMutation = useCreatePrompt()
  const { data: projects } = useProjects()

  return useMutation({
    mutationFn: async ({
      context,
      options = {}
    }: {
      context: Context
      options?: ContextToPromptOptions
    }): Promise<ContextToPromptResult> => {
      try {
        // 1. Determine target project
        const targetProjectId = options.projectId || getActiveProjectId(projects || [])
        
        if (!targetProjectId) {
          throw new Error('Lütfen önce bir proje seçin')
        }

        // 2. Prepare prompt data
        const promptData = {
          project_id: targetProjectId,
          prompt_text: context.content,
          title: options.customTitle || context.title || 'Context Gallery Prompt',
          category: options.customCategory || extractCategoryFromContext(context),
          tags: combineTagsFromContext(context, options.additionalTags),
          order_index: await getNextOrderIndex(targetProjectId),
          is_used: false,
          is_favorite: options.markAsFavorite || false,
          source: 'context_gallery' as const,
          source_context_id: context.id
        }

        // 3. Create prompt using existing hook
        const newPrompt = await createPromptMutation.mutateAsync(promptData)

        // 4. Track context usage
        await trackContextUsage(context.id, targetProjectId)

        // 5. Update UI state
        queryClient.invalidateQueries({ queryKey: ['prompts', targetProjectId] })
        queryClient.invalidateQueries({ queryKey: ['projects'] })

        return {
          success: true,
          promptId: newPrompt.id
        }

      } catch (error) {
        console.error('Context to prompt conversion error:', error)
        
        const errorMessage = error instanceof Error 
          ? error.message 
          : 'Context prompt olarak eklenirken hata oluştu'

        return {
          success: false,
          error: errorMessage
        }
      }
    },
    onSuccess: (result) => {
      if (result.success) {
        toast.success('Context başarıyla projeye eklendi!')
      } else {
        toast.error(result.error || 'Bir hata oluştu')
      }
    },
    onError: (error) => {
      console.error('Context to prompt mutation error:', error)
      toast.error('Context eklenirken beklenmeyen bir hata oluştu')
    }
  })
}

/**
 * Simplified hook for quick context addition to active project
 */
export function useAddContextToProject() {
  const contextToPromptMutation = useContextToPrompt()

  return {
    addContext: async (context: Context, customTitle?: string) => {
      return contextToPromptMutation.mutateAsync({
        context,
        options: { customTitle }
      })
    },
    isLoading: contextToPromptMutation.isPending,
    error: contextToPromptMutation.error
  }
}

/**
 * Hook for batch adding multiple contexts to project
 */
export function useBatchAddContexts() {
  const contextToPromptMutation = useContextToPrompt()
  // const queryClient = useQueryClient() // Unused for now

  return useMutation({
    mutationFn: async ({
      contexts,
      projectId,
      options = {}
    }: {
      contexts: Context[]
      projectId?: string
      options?: Omit<ContextToPromptOptions, 'projectId'>
    }) => {
      const results = []
      
      for (const context of contexts) {
        try {
          const result = await contextToPromptMutation.mutateAsync({
            context,
            options: { ...options, projectId }
          })
          results.push({ context: context.id, result })
        } catch (error) {
          results.push({ 
            context: context.id, 
            result: { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
          })
        }
      }

      return results
    },
    onSuccess: (results) => {
      const successCount = results.filter(r => r.result.success).length
      const totalCount = results.length
      
      if (successCount === totalCount) {
        toast.success(`${successCount} context başarıyla eklendi!`)
      } else {
        toast.warning(`${successCount}/${totalCount} context eklendi. Bazı hatalar oluştu.`)
      }
    }
  })
}

// Helper functions
interface ProjectLike {
  id: string
}

function getActiveProjectId(projects: unknown[]): string | null {
  if (!projects || projects.length === 0) return null

  // Try to get from localStorage or URL params
  const stored = localStorage.getItem('activeProjectId')
  if (stored && projects.find((p: unknown) => (p as ProjectLike)?.id === stored)) {
    return stored
  }

  // Fallback to first project
  return (projects[0] as ProjectLike)?.id || null
}

function extractCategoryFromContext(context: Context): string | undefined {
  // Extract category from context metadata or tags
  if (context.category) return context.category.name || context.category.toString()
  if (context.tags && context.tags.length > 0) {
    // Use first tag as category if it looks like a category
    const firstTag = context.tags[0]
    if (firstTag.startsWith('/')) return firstTag
  }
  return undefined
}

function combineTagsFromContext(context: Context, additionalTags?: string[]): string[] {
  const contextTags = context.tags || []
  const additional = additionalTags || []
  
  // Combine and deduplicate tags
  const allTags = [...contextTags, ...additional]
  return Array.from(new Set(allTags)).filter(tag => tag.trim().length > 0)
}

async function getNextOrderIndex(_projectId: string): Promise<number> {
  // This would typically query the database to get the next order index
  // For now, return a timestamp-based index
  return Date.now()
}

async function trackContextUsage(contextId: string, projectId: string): Promise<void> {
  try {
    // Track context usage in analytics
    // This could be implemented with Supabase or analytics service
    console.log(`Context ${contextId} used in project ${projectId}`)
  } catch (error) {
    console.warn('Failed to track context usage:', error)
    // Don't throw error for analytics failure
  }
}
