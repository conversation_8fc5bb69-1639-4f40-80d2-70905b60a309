import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

// Server-side Supabase client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Verify admin authentication using SSR client
async function verifyAdminAuth(request: NextRequest) {
  try {
    const cookieStore = cookies();

    // Create SSR client for proper cookie handling
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set() {
            // No-op for API routes
          },
          remove() {
            // No-op for API routes
          },
        },
      }
    );

    // Verify the session
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('Auth verification failed:', userError);
      return { error: 'Invalid authentication', status: 401 };
    }

    // Check admin status using admin client
    const { data: adminData, error: adminError } = await supabaseAdmin
      .from('admin_users')
      .select('user_id, is_active, role')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .single();

    if (adminError || !adminData) {
      console.error('Admin check failed for user:', user.id, 'Error:', adminError);
      return { error: 'Admin access required', status: 403 };
    }

    return { user, adminData, error: null };
  } catch (error) {
    console.error('Admin auth verification error:', error);
    return { error: 'Authentication failed', status: 500 };
  }
}

// Handle dashboard stats
async function handleDashboardStats() {
  try {
    const { data, error } = await supabaseAdmin.rpc('get_admin_dashboard_stats');
    
    if (error) {
      console.error('Dashboard stats error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch dashboard stats' },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle user stats
async function handleUserStats() {
  try {
    const { data, error } = await supabaseAdmin.rpc('get_user_stats');
    
    if (error) {
      console.error('User stats error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch user stats' },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('User stats error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle users list with pagination
async function handleUsersList(searchParams: URLSearchParams) {
  try {
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search');
    const role = searchParams.get('role');
    const status = searchParams.get('status');
    
    const offset = (page - 1) * limit;

    let query = supabaseAdmin
      .from('admin_users_view')
      .select('*', { count: 'exact' });

    // Apply filters
    if (search) {
      query = query.ilike('email', `%${search}%`);
    }
    if (role) {
      query = query.eq('role', role);
    }
    if (status) {
      query = query.eq('is_active', status === 'active');
    }

    const { data, error, count } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Users list error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch users' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      users: data || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit),
    });
  } catch (error) {
    console.error('Users list error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Plan istatistikleri
async function handlePlanStats() {
  try {
    const { data, error } = await supabaseAdmin.rpc('get_plan_statistics');

    if (error) {
      console.error('Plan stats error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch plan statistics' },
        { status: 500 }
      );
    }

    return NextResponse.json(data || []);
  } catch (error) {
    console.error('Plan stats error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Kullanıcı planları listesi
async function handleUserPlans(searchParams: URLSearchParams) {
  try {
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = (page - 1) * limit;

    const { data, error, count } = await supabaseAdmin
      .from('user_plans')
      .select(`
        *,
        plan_types (
          name,
          display_name
        ),
        auth.users (
          email
        )
      `, { count: 'exact' })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('User plans error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch user plans' },
        { status: 500 }
      );
    }

    // Transform data to include user email
    const transformedData = data?.map(plan => ({
      ...plan,
      user_email: plan.auth?.users?.email || 'Unknown',
      plan_name: plan.plan_types?.name || 'Unknown',
      plan_display_name: plan.plan_types?.display_name || 'Unknown'
    })) || [];

    return NextResponse.json({
      plans: transformedData,
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit),
    });
  } catch (error) {
    console.error('User plans error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Plan değiştirme
async function handleChangePlan(request: NextRequest) {
  try {
    const { userId, planName } = await request.json();

    if (!userId || !planName) {
      return NextResponse.json(
        { error: 'User ID and plan name are required' },
        { status: 400 }
      );
    }

    const { data, error } = await supabaseAdmin.rpc('change_user_plan', {
      user_uuid: userId,
      new_plan_name: planName
    });

    if (error) {
      console.error('Change plan error:', error);
      return NextResponse.json(
        { error: 'Failed to change plan' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true, planId: data });
  } catch (error) {
    console.error('Change plan error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Main API handler
export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  // Verify admin authentication
  const authResult = await verifyAdminAuth(request);
  if (authResult.error) {
    return NextResponse.json(
      { error: authResult.error },
      { status: authResult.status }
    );
  }

  const path = params.path.join('/');
  const { searchParams } = new URL(request.url);

  try {
    switch (path) {
      case 'dashboard/stats':
        return handleDashboardStats();
      
      case 'users/stats':
        return handleUserStats();
      
      case 'users':
        return handleUsersList(searchParams);

      case 'plans/stats':
        return handlePlanStats();

      case 'plans/users':
        return handleUserPlans(searchParams);

      default:
        return NextResponse.json(
          { error: 'Endpoint not found' },
          { status: 404 }
        );
    }
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle POST requests
export async function POST(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  // Verify admin authentication
  const authResult = await verifyAdminAuth(request);
  if (authResult.error) {
    return NextResponse.json(
      { error: authResult.error },
      { status: authResult.status }
    );
  }

  const path = params.path.join('/');

  try {
    switch (path) {
      case 'plans/change':
        return handleChangePlan(request);

      default:
        return NextResponse.json(
          { error: 'Endpoint not found' },
          { status: 404 }
        );
    }
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
