'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabaseBrowser as supabase } from '@/lib/supabase-browser'

// Type definitions
export type PromptCategory = {
  id: string
  name: string
  description: string | null
  icon: string | null
  color: string
  created_at: string
}

export type PromptTemplate = {
  id: string
  category_id: string | null
  name: string
  description: string | null
  prompt_text: string
  tags: string[] | null
  is_system: boolean
  usage_count: number
  created_at: string
  updated_at: string
}

export type PromptTemplateWithCategory = PromptTemplate & {
  category: PromptCategory | null
}

// Kategorileri getir
export function usePromptCategories() {
  return useQuery({
    queryKey: ['prompt-categories'],
    queryFn: async (): Promise<PromptCategory[]> => {
      const { data, error } = await supabase
        .from('prompt_categories')
        .select('*')
        .order('name')

      if (error) {
        throw new Error(error.message)
      }

      return data || []
    },
    staleTime: 5 * 60 * 1000, // 5 dakika
  })
}

// Şablonları getir
export function usePromptTemplates(categoryId?: string) {
  return useQuery({
    queryKey: ['prompt-templates', categoryId],
    queryFn: async (): Promise<PromptTemplateWithCategory[]> => {
      let query = supabase
        .from('prompt_templates')
        .select(`
          *,
          category:prompt_categories(*)
        `)
        .order('usage_count', { ascending: false })
        .order('name')

      if (categoryId) {
        query = query.eq('category_id', categoryId)
      }

      const { data, error } = await query

      if (error) {
        throw new Error(error.message)
      }

      return data || []
    },
    staleTime: 5 * 60 * 1000, // 5 dakika
  })
}

// Popüler şablonları getir
export function usePopularTemplates(limit = 6) {
  return useQuery({
    queryKey: ['popular-templates', limit],
    queryFn: async (): Promise<PromptTemplateWithCategory[]> => {
      const { data, error } = await supabase
        .from('prompt_templates')
        .select(`
          *,
          category:prompt_categories(*)
        `)
        .order('usage_count', { ascending: false })
        .limit(limit)

      if (error) {
        throw new Error(error.message)
      }

      return data || []
    },
    staleTime: 5 * 60 * 1000, // 5 dakika
  })
}

// Şablon arama
export function useSearchTemplates(searchTerm: string) {
  return useQuery({
    queryKey: ['search-templates', searchTerm],
    queryFn: async (): Promise<PromptTemplateWithCategory[]> => {
      if (!searchTerm.trim()) return []

      const { data, error } = await supabase
        .from('prompt_templates')
        .select(`
          *,
          category:prompt_categories(*)
        `)
        .or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,prompt_text.ilike.%${searchTerm}%`)
        .order('usage_count', { ascending: false })

      if (error) {
        throw new Error(error.message)
      }

      return data || []
    },
    enabled: !!searchTerm.trim(),
    staleTime: 30 * 1000, // 30 saniye
  })
}

// Şablon kullanım sayısını artır
export function useIncrementTemplateUsage() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (templateId: string): Promise<void> => {
      const { error } = await supabase
        .rpc('increment_template_usage', { template_id: templateId })

      if (error) {
        throw new Error(error.message)
      }
    },
    onSuccess: () => {
      // Cache'i güncelle
      queryClient.invalidateQueries({ queryKey: ['prompt-templates'] })
      queryClient.invalidateQueries({ queryKey: ['popular-templates'] })
    },
  })
}

// Şablon detayını getir
export function usePromptTemplate(templateId: string) {
  return useQuery({
    queryKey: ['prompt-template', templateId],
    queryFn: async (): Promise<PromptTemplateWithCategory> => {
      const { data, error } = await supabase
        .from('prompt_templates')
        .select(`
          *,
          category:prompt_categories(*)
        `)
        .eq('id', templateId)
        .single()

      if (error) {
        throw new Error(error.message)
      }

      return data
    },
    enabled: !!templateId,
  })
} 