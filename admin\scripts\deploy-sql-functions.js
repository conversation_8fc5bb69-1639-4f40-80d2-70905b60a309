#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to deploy SQL functions to Supabase
 * This script reads the SQL file and executes the functions via Supabase client
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env.local') });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function deployFunction() {
  try {
    console.log('🚀 Deploying get_admin_dashboard_stats function...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../sql/performance-optimizations.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');
    
    // Extract just the get_admin_dashboard_stats function
    const functionMatch = sqlContent.match(
      /CREATE OR REPLACE FUNCTION get_admin_dashboard_stats\(\)[\s\S]*?\$\$;/
    );
    
    if (!functionMatch) {
      throw new Error('Could not find get_admin_dashboard_stats function in SQL file');
    }
    
    const functionSQL = functionMatch[0];
    console.log('📝 Function SQL extracted successfully');
    
    // Execute the function creation
    const { data, error } = await supabase.rpc('exec_sql', { 
      sql: functionSQL 
    });
    
    if (error) {
      // Try alternative method - direct SQL execution
      console.log('⚠️  RPC method failed, trying direct execution...');
      
      // For direct execution, we'll test the function instead
      const { data: testData, error: testError } = await supabase.rpc('get_admin_dashboard_stats');
      
      if (testError) {
        console.error('❌ Function test failed:', testError.message);
        console.log('\n📋 Manual deployment required:');
        console.log('1. Go to Supabase Dashboard > SQL Editor');
        console.log('2. Execute the following SQL:');
        console.log('\n' + functionSQL);
        return;
      }
      
      console.log('✅ Function already exists and working!');
      console.log('📊 Test result:', JSON.stringify(testData, null, 2));
      return;
    }
    
    console.log('✅ Function deployed successfully!');
    
    // Test the function
    console.log('🧪 Testing function...');
    const { data: testData, error: testError } = await supabase.rpc('get_admin_dashboard_stats');
    
    if (testError) {
      console.error('❌ Function test failed:', testError.message);
    } else {
      console.log('✅ Function test successful!');
      console.log('📊 Result:', JSON.stringify(testData, null, 2));
    }
    
  } catch (error) {
    console.error('❌ Deployment failed:', error.message);
    console.log('\n📋 Manual deployment instructions:');
    console.log('1. Go to Supabase Dashboard > SQL Editor');
    console.log('2. Copy and execute the SQL from: admin/sql/performance-optimizations.sql');
    console.log('3. Look for the get_admin_dashboard_stats function');
  }
}

async function testFunction() {
  try {
    console.log('🧪 Testing get_admin_dashboard_stats function...');
    
    const { data, error } = await supabase.rpc('get_admin_dashboard_stats');
    
    if (error) {
      console.error('❌ Function test failed:', error.message);
      console.log('\n💡 This likely means the function needs to be deployed manually.');
      console.log('📋 Manual deployment instructions:');
      console.log('1. Go to Supabase Dashboard > SQL Editor');
      console.log('2. Copy and execute the SQL from: admin/sql/performance-optimizations.sql');
      return false;
    }
    
    console.log('✅ Function test successful!');
    console.log('📊 Dashboard stats:', JSON.stringify(data, null, 2));
    return true;
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

// Main execution
async function main() {
  const command = process.argv[2];
  
  if (command === 'test') {
    await testFunction();
  } else {
    await deployFunction();
  }
}

main().catch(console.error);
