// Optimized icon imports - only import the icons we actually use
// This reduces the bundle size significantly compared to importing all of lucide-react

// Navigation icons
export {
  LayoutDashboard,
  Users,
  FolderOpen,
  FileText,
  Settings,
  LogOut,
  Menu,
  X,
} from 'lucide-react';

// Action icons
export {
  Plus,
  Edit,
  Trash2,
  Search,
  Filter,
  Download,
  Upload,
  Save,
  Copy,
  ExternalLink,
} from 'lucide-react';

// Status icons
export {
  Check,
  AlertCircle,
  Info,
  XCircle,
  Clock,
  Eye,
  EyeOff,
  Lock,
  Unlock,
} from 'lucide-react';

// Data visualization icons
export {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Activity,
  PieChart,
  LineChart,
} from 'lucide-react';

// UI icons
export {
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  MoreVertical,
  MoreHorizontal,
  Refresh,
  Loader,
} from 'lucide-react';

// Admin specific icons
export {
  Shield,
  UserCheck,
  UserX,
  Database,
  Server,
  Globe,
  Mail,
  Phone,
} from 'lucide-react';

// File and document icons
export {
  File,
  Folder,
  Image,
  Video,
  Music,
  Archive,
  Code,
  BookOpen,
} from 'lucide-react';

// Social and communication icons
export {
  MessageSquare,
  Bell,
  Heart,
  Star,
  Share,
  Link,
  Hash,
  AtSign,
} from 'lucide-react';

// System icons
export {
  Power,
  Wifi,
  Battery,
  Volume2,
  VolumeX,
  Bluetooth,
  Cpu,
  HardDrive,
} from 'lucide-react';

// Icon mapping for dynamic usage
export const iconMap = {
  // Navigation
  dashboard: LayoutDashboard,
  users: Users,
  projects: FolderOpen,
  prompts: FileText,
  settings: Settings,
  logout: LogOut,
  menu: Menu,
  close: X,
  
  // Actions
  add: Plus,
  edit: Edit,
  delete: Trash2,
  search: Search,
  filter: Filter,
  download: Download,
  upload: Upload,
  save: Save,
  copy: Copy,
  external: ExternalLink,
  
  // Status
  success: Check,
  warning: AlertCircle,
  info: Info,
  error: XCircle,
  pending: Clock,
  visible: Eye,
  hidden: EyeOff,
  locked: Lock,
  unlocked: Unlock,
  
  // Charts
  bar: BarChart3,
  up: TrendingUp,
  down: TrendingDown,
  activity: Activity,
  pie: PieChart,
  line: LineChart,
  
  // UI
  chevronDown: ChevronDown,
  chevronUp: ChevronUp,
  chevronLeft: ChevronLeft,
  chevronRight: ChevronRight,
  moreVertical: MoreVertical,
  moreHorizontal: MoreHorizontal,
  refresh: Refresh,
  loading: Loader,
  
  // Admin
  admin: Shield,
  userCheck: UserCheck,
  userX: UserX,
  database: Database,
  server: Server,
  globe: Globe,
  mail: Mail,
  phone: Phone,
} as const;

export type IconName = keyof typeof iconMap;

// Helper function to get icon by name
export function getIcon(name: IconName) {
  return iconMap[name];
}
