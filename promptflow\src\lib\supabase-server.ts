import { createServer<PERSON>lient } from '@supabase/ssr'
import { NextRequest, NextResponse } from 'next/server'

export function createSupabaseServerClient(request: NextRequest, response: NextResponse) {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          const value = request.cookies.get(name)?.value
          console.log(`🍪 [SERVER_CLIENT] Cookie GET: ${name} = ${value ? 'EXISTS' : 'NOT_FOUND'}`)

          // If we have the auth token cookie, try to parse it
          if (name === 'sb-iqehopwgrczylqliajww-auth-token' && value) {
            try {
              const session = JSON.parse(value)
              console.log(`🔐 [SERVER_CLIENT] Parsed session from cookie:`, {
                hasAccessToken: !!session.access_token,
                expiresAt: session.expires_at ? new Date(session.expires_at * 1000).toISOString() : null
              })
              return value
            } catch (e) {
              console.warn(`⚠️ [SERVER_CLIENT] Failed to parse session cookie:`, e)
            }
          }

          return value
        },
        set(name: string, value: string, options: Record<string, unknown>) {
          console.log(`🍪 [SERVER_CLIENT] Cookie SET: ${name}`)
          request.cookies.set({
            name,
            value,
            ...options,
          })
          response.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name: string, options: Record<string, unknown>) {
          console.log(`🍪 [SERVER_CLIENT] Cookie REMOVE: ${name}`)
          request.cookies.set({
            name,
            value: '',
            ...options,
          })
          response.cookies.set({
            name,
            value: '',
            ...options,
          })
        },
      },
    }
  )
}
