/**
 * Project Validation Security Tests
 * Comprehensive security and validation testing
 */

import {
  validateProjectName,
  validateProjectNameUnique,
  sanitizeProjectName,
  escapeHtml,
  checkClientRateLimit,
  resetClientRateLimit,
  formatValidationError,
  isSameProjectName,
  PROJECT_NAME_RULES
} from '../project-validation'

// Mock localStorage for rate limiting tests
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn()
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
})

describe('Project Validation Security Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    resetClientRateLimit()
  })

  describe('Input Sanitization', () => {
    it('sanitizes basic input correctly', () => {
      expect(sanitizeProjectName('  Test Project  ')).toBe('Test Project')
      expect(sanitizeProjectName('Test    Multiple    Spaces')).toBe('Test Multiple Spaces')
      expect(sanitizeProjectName('\t\nTest\t\n')).toBe('Test')
    })

    it('handles empty and whitespace-only input', () => {
      expect(sanitizeProjectName('')).toBe('')
      expect(sanitizeProjectName('   ')).toBe('')
      expect(sanitizeProjectName('\t\n\r')).toBe('')
    })

    it('preserves valid characters', () => {
      expect(sanitizeProjectName('Test-Project_123.v2')).toBe('Test-Project_123.v2')
      expect(sanitizeProjectName('Türkçe Proje Adı')).toBe('Türkçe Proje Adı')
    })
  })

  describe('XSS Protection', () => {
    it('escapes HTML entities', () => {
      expect(escapeHtml('<script>alert("xss")</script>')).toBe('&lt;script&gt;alert("xss")&lt;/script&gt;')
      expect(escapeHtml('Test & Company')).toBe('Test &amp; Company')
      expect(escapeHtml('"quoted" text')).toBe('"quoted" text')
    })

    it('handles special characters', () => {
      expect(escapeHtml('<img src="x" onerror="alert(1)">')).toBe('&lt;img src="x" onerror="alert(1)"&gt;')
      expect(escapeHtml('javascript:alert(1)')).toBe('javascript:alert(1)')
    })
  })

  describe('Length Validation', () => {
    it('rejects names that are too short', () => {
      const result = validateProjectName('AB')
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('en az 3 karakter')
    })

    it('rejects names that are too long', () => {
      const longName = 'A'.repeat(51)
      const result = validateProjectName(longName)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('en fazla 50 karakter')
    })

    it('accepts valid length names', () => {
      const result = validateProjectName('Valid Project Name')
      expect(result.isValid).toBe(true)
      expect(result.sanitizedValue).toBe('Valid Project Name')
    })
  })

  describe('Character Validation', () => {
    it('accepts valid characters', () => {
      const validNames = [
        'Test Project',
        'Project-123',
        'Project_v2',
        'Project.final',
        'Test-Project_123.v2'
      ]

      validNames.forEach(name => {
        const result = validateProjectName(name)
        expect(result.isValid).toBe(true)
      })
    })

    it('rejects invalid characters', () => {
      const invalidNames = [
        'Test@Project',
        'Project#123',
        'Project$',
        'Project%',
        'Project&',
        'Project*',
        'Project+',
        'Project=',
        'Project[',
        'Project]',
        'Project{',
        'Project}',
        'Project|',
        'Project\\',
        'Project/',
        'Project?',
        'Project<',
        'Project>',
        'Project,',
        'Project;',
        'Project:',
        'Project"',
        "Project'",
        'Project`',
        'Project~',
        'Project!',
        'Project^'
      ]

      invalidNames.forEach(name => {
        const result = validateProjectName(name)
        expect(result.isValid).toBe(false)
        expect(result.error).toContain('sadece harf, rakam, boşluk ve -_. karakterlerini içerebilir')
      })
    })

    it('rejects whitespace-only names', () => {
      const result = validateProjectName('   ')
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('boş olamaz')
    })

    it('rejects names with only spaces and allowed chars', () => {
      const result = validateProjectName('  -  _  .  ')
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('sadece boşluk karakterlerinden oluşamaz')
    })
  })

  describe('Duplicate Name Validation', () => {
    const mockProjects = [
      { id: 'project-1', name: 'Existing Project' },
      { id: 'project-2', name: 'Another Project' },
      { id: 'project-3', name: 'CASE SENSITIVE' }
    ]

    it('detects exact duplicates', async () => {
      const result = await validateProjectNameUnique('Existing Project', 'new-project', mockProjects)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('bu isimde bir proje zaten mevcut')
    })

    it('detects case-insensitive duplicates', async () => {
      const result = await validateProjectNameUnique('existing project', 'new-project', mockProjects)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('bu isimde bir proje zaten mevcut')
    })

    it('allows same name for same project', async () => {
      const result = await validateProjectNameUnique('Existing Project', 'project-1', mockProjects)
      expect(result.isValid).toBe(true)
    })

    it('allows unique names', async () => {
      const result = await validateProjectNameUnique('Unique Project Name', 'new-project', mockProjects)
      expect(result.isValid).toBe(true)
    })

    it('handles whitespace differences', async () => {
      const result = await validateProjectNameUnique('  Existing Project  ', 'new-project', mockProjects)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('bu isimde bir proje zaten mevcut')
    })
  })

  describe('Rate Limiting', () => {
    beforeEach(() => {
      mockLocalStorage.getItem.mockReturnValue(null)
    })

    it('allows first request', () => {
      expect(checkClientRateLimit()).toBe(true)
      expect(mockLocalStorage.setItem).toHaveBeenCalled()
    })

    it('tracks request count', () => {
      const now = Date.now()
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        count: 5,
        windowStart: now
      }))

      expect(checkClientRateLimit()).toBe(true)
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'project_name_update_rate_limit',
        JSON.stringify({
          count: 6,
          windowStart: now
        })
      )
    })

    it('blocks when limit exceeded', () => {
      const now = Date.now()
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        count: PROJECT_NAME_RULES.rateLimit.maxRequests,
        windowStart: now
      }))

      expect(checkClientRateLimit()).toBe(false)
    })

    it('resets after time window', () => {
      const oldTime = Date.now() - (PROJECT_NAME_RULES.rateLimit.windowMinutes * 60 * 1000 + 1000)
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        count: PROJECT_NAME_RULES.rateLimit.maxRequests,
        windowStart: oldTime
      }))

      expect(checkClientRateLimit()).toBe(true)
    })

    it('handles localStorage errors gracefully', () => {
      mockLocalStorage.getItem.mockImplementation(() => {
        throw new Error('localStorage error')
      })

      expect(checkClientRateLimit()).toBe(true) // Should allow on error
    })
  })

  describe('Error Message Formatting', () => {
    it('formats database errors correctly', () => {
      expect(formatValidationError('unique_violation')).toBe('Bu isimde bir proje zaten mevcut')
      expect(formatValidationError('check_violation')).toBe('Proje adı geçersiz karakterler içeriyor')
      expect(formatValidationError('not_null_violation')).toBe('Proje adı boş olamaz')
      expect(formatValidationError('RateLimitExceeded')).toBe('Çok fazla güncelleme isteği. Lütfen bekleyin.')
    })

    it('passes through unknown errors', () => {
      expect(formatValidationError('unknown_error')).toBe('unknown_error')
      expect(formatValidationError('Custom error message')).toBe('Custom error message')
    })
  })

  describe('Name Comparison', () => {
    it('compares names case-insensitively', () => {
      expect(isSameProjectName('Test Project', 'test project')).toBe(true)
      expect(isSameProjectName('TEST PROJECT', 'test project')).toBe(true)
      expect(isSameProjectName('Test Project', 'Different Project')).toBe(false)
    })

    it('handles whitespace differences', () => {
      expect(isSameProjectName('  Test Project  ', 'Test Project')).toBe(true)
      expect(isSameProjectName('Test    Project', 'Test Project')).toBe(true)
    })

    it('handles empty strings', () => {
      expect(isSameProjectName('', '')).toBe(true)
      expect(isSameProjectName('', '   ')).toBe(true)
      expect(isSameProjectName('Test', '')).toBe(false)
    })
  })

  describe('Security Edge Cases', () => {
    it('handles null and undefined input', () => {
      expect(sanitizeProjectName(null as unknown as string)).toBe('')
      expect(sanitizeProjectName(undefined as unknown as string)).toBe('')
    })

    it('handles very long input gracefully', () => {
      const veryLongString = 'A'.repeat(10000)
      const result = validateProjectName(veryLongString)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('en fazla 50 karakter')
    })

    it('handles unicode characters', () => {
      const unicodeNames = [
        'Proje 🚀',
        'Proje ñ',
        'Proje ü',
        'Proje ğ',
        'Proje ş',
        'Proje ı',
        'Proje ç',
        'Proje ö'
      ]

      unicodeNames.forEach(name => {
        const result = validateProjectName(name)
        // Unicode characters should be rejected by our regex
        expect(result.isValid).toBe(false)
      })
    })

    it('prevents injection attempts', () => {
      const injectionAttempts = [
        "'; DROP TABLE projects; --",
        '"; DELETE FROM projects; --',
        '<script>alert("xss")</script>',
        'javascript:alert(1)',
        '${alert(1)}',
        '{{alert(1)}}',
        '#{alert(1)}'
      ]

      injectionAttempts.forEach(attempt => {
        const result = validateProjectName(attempt)
        expect(result.isValid).toBe(false)
      })
    })
  })
})
