---
type: "fixes"
role: "dependency_troubleshooting_guide"
dependencies:
  - "core/PROJECT_CORE.md"
  - "frontend/MAIN_APP.md"
  - "frontend/ADMIN_PANEL.md"
related_fixes:
  - "fixes/BUILD_ERROR_RESOLUTION.md"
auto_update_from:
  - "frontend/MAIN_APP.md"
  - "frontend/ADMIN_PANEL.md"
solution_history:
  - date: "2025-01-29"
    issue: "Missing critters dependency"
    solution: "npm install critters for CSS optimization"
    status: "resolved"
  - date: "2025-01-29"
    issue: "Security vulnerabilities"
    solution: "npm audit fix applied"
    status: "resolved"
last_updated: "2025-01-29"
---

# PromptFlow Dependency Management & Security

## Dependency Management Strategy

### Package Manager Standards
```bash
# Use npm for consistency across both applications
# Avoid mixing npm, yarn, and pnpm in same project

# Lock file management
# - Commit package-lock.json to version control
# - Use npm ci in production/CI environments
# - Use npm install for development
```

### Version Management
```json
// package.json version strategy
{
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=9.0.0"
  },
  "dependencies": {
    // Pin major versions for stability
    "next": "15.4.1",
    "react": "19.1.0",
    "react-dom": "19.1.0",
    
    // Allow minor updates for patches
    "@supabase/supabase-js": "^2.51.0",
    "@tanstack/react-query": "^5.83.0",
    
    // Pin exact versions for critical dependencies
    "typescript": "5.7.2"
  }
}
```

## Critical Dependencies for PromptFlow

### Build-Time Dependencies
```bash
# Essential for build process
npm install critters              # CSS optimization
npm install @types/node          # Node.js types
npm install @types/react         # React types
npm install @types/react-dom     # React DOM types
npm install typescript          # TypeScript compiler
npm install eslint              # Code linting
npm install tailwindcss         # CSS framework
```

### Runtime Dependencies
```bash
# Core application dependencies
npm install next                 # Framework
npm install react               # UI library
npm install react-dom           # DOM rendering
npm install @supabase/supabase-js # Database client
npm install @tanstack/react-query # Data fetching
npm install zustand             # State management
npm install lucide-react        # Icons
npm install sonner              # Notifications
```

### UI Component Dependencies
```bash
# shadcn/ui ecosystem
npm install @radix-ui/react-dialog
npm install @radix-ui/react-label
npm install @radix-ui/react-progress
npm install @radix-ui/react-select
npm install @radix-ui/react-separator
npm install @radix-ui/react-slot
npm install class-variance-authority
npm install clsx
npm install tailwind-merge
```

## Security Management

### Vulnerability Scanning
```bash
# Regular security audits
npm audit                        # Check for vulnerabilities
npm audit fix                    # Auto-fix low-risk issues
npm audit fix --force           # Force fix (use cautiously)

# Manual review for high-risk vulnerabilities
npm audit --audit-level=high
```

### Security Automation
```json
// package.json scripts
{
  "scripts": {
    "security-check": "npm audit --audit-level=moderate",
    "security-fix": "npm audit fix",
    "deps-check": "npm outdated",
    "deps-update": "npm update"
  }
}
```

### Dependency Update Strategy
```bash
# Check for outdated packages
npm outdated

# Update patch versions (safe)
npm update

# Update minor versions (review changes)
npm install package@^1.2.0

# Update major versions (test thoroughly)
npm install package@2.0.0
```

## Common Dependency Issues

### Issue 1: Missing Build Dependencies
**Symptoms**: `Cannot find module 'critters'` during build
**Solution**:
```bash
# Install missing build-time dependencies
npm install critters
npm install @types/node
npm install typescript

# Verify build works
npm run build
```

### Issue 2: Version Conflicts
**Symptoms**: Peer dependency warnings or build failures
**Solution**:
```bash
# Check dependency tree
npm ls --depth=0

# Resolve conflicts
npm install --legacy-peer-deps  # Temporary fix
# Or update conflicting packages to compatible versions

# Clean install if needed
rm -rf node_modules package-lock.json
npm install
```

### Issue 3: Security Vulnerabilities
**Symptoms**: `npm audit` reports vulnerabilities
**Solution**:
```bash
# Low/moderate vulnerabilities
npm audit fix

# High/critical vulnerabilities
npm audit fix --force  # Use with caution
# Or manually update specific packages:
npm install vulnerable-package@latest
```

### Issue 4: Outdated Dependencies
**Symptoms**: Build warnings or deprecated feature usage
**Solution**:
```bash
# Check what's outdated
npm outdated

# Update specific packages
npm install package@latest

# Update all patch/minor versions
npm update

# For major version updates, test thoroughly
npm install package@^2.0.0
npm run build
npm run test
```

## Project-Specific Dependency Patterns

### PromptFlow Main App Dependencies
```json
{
  "dependencies": {
    "next": "15.4.1",
    "react": "19.1.0",
    "react-dom": "19.1.0",
    "@supabase/ssr": "^0.5.2",
    "@supabase/supabase-js": "^2.51.0",
    "@tanstack/react-query": "^5.83.0",
    "zustand": "^5.0.6"
  },
  "devDependencies": {
    "@types/node": "^20",
    "@types/react": "^19",
    "@types/react-dom": "^19",
    "typescript": "^5",
    "eslint": "^9",
    "tailwindcss": "^4"
  }
}
```

### Admin Panel Dependencies
```json
{
  "dependencies": {
    "next": "14.2.18",
    "react": "^18",
    "react-dom": "^18",
    "@supabase/ssr": "^0.5.2",
    "@supabase/supabase-js": "^2.51.0"
  },
  "devDependencies": {
    "@types/node": "^20",
    "@types/react": "^18",
    "@types/react-dom": "^18",
    "typescript": "^5",
    "eslint": "^8"
  }
}
```

## Automation Scripts

### Dependency Health Check
```bash
#!/bin/bash
# dependency-health-check.sh

echo "🔍 Checking dependency health..."

# Check for vulnerabilities
echo "📊 Security audit:"
npm audit --audit-level=moderate

# Check for outdated packages
echo "📦 Outdated packages:"
npm outdated

# Check for unused dependencies
echo "🧹 Checking for unused dependencies:"
npx depcheck

# Verify build still works
echo "🏗️ Testing build:"
npm run build

echo "✅ Dependency health check complete"
```

### Automated Update Script
```bash
#!/bin/bash
# update-dependencies.sh

echo "🔄 Updating dependencies..."

# Backup current package-lock.json
cp package-lock.json package-lock.json.backup

# Update patch and minor versions
npm update

# Fix security vulnerabilities
npm audit fix

# Test build
if npm run build; then
  echo "✅ Dependencies updated successfully"
  rm package-lock.json.backup
else
  echo "❌ Build failed, reverting changes"
  mv package-lock.json.backup package-lock.json
  npm install
fi
```

## CI/CD Integration

### GitHub Actions Dependency Check
```yaml
# .github/workflows/dependency-check.yml
name: Dependency Security Check

on:
  schedule:
    - cron: '0 0 * * 1'  # Weekly on Monday
  pull_request:
    paths:
      - 'package.json'
      - 'package-lock.json'

jobs:
  security-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Security audit
        run: npm audit --audit-level=moderate
      
      - name: Check for outdated packages
        run: npm outdated
```

### Pre-commit Hooks
```json
// package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "npm audit --audit-level=high"
    }
  }
}
```

## Update Requirements
- Run security audits weekly
- Update dependencies monthly
- Test thoroughly after major version updates
- Maintain separate dependency versions for main app vs admin panel
- Document any custom dependency requirements
- Keep automation scripts current with project needs
- Monitor for breaking changes in major dependencies
