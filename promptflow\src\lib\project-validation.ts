/**
 * Proje adı validation ve güvenlik utilities
 * Güvenlik odaklı input validation ve sanitization
 */

// Proje adı limitleri ve kuralları
export const PROJECT_NAME_RULES = {
  minLength: 3,
  maxLength: 50,
  allowedCharsRegex: /^[a-zA-Z0-9\s\-_.]+$/,
  debounceMs: 300,
  rateLimit: {
    maxRequests: 10,
    windowMinutes: 1
  }
} as const;

// Validation sonuç tipi
export interface ValidationResult {
  isValid: boolean;
  error?: string;
  sanitizedValue?: string;
}

// Rate limiting için local storage key
const RATE_LIMIT_KEY = 'project_name_update_rate_limit';

/**
 * Proje adını sanitize eder
 */
export function sanitizeProjectName(name: string): string {
  if (!name) return '';
  
  // Trim ve normalize
  let sanitized = name.trim();
  
  // Çoklu boşlukları tek boşluğa çevir
  sanitized = sanitized.replace(/\s+/g, ' ');
  
  // Başlangıç ve bitiş boşluklarını kaldır
  sanitized = sanitized.trim();
  
  return sanitized;
}

/**
 * Proje adını validate eder
 */
export function validateProjectName(name: string): ValidationResult {
  const sanitized = sanitizeProjectName(name);
  
  // Boş kontrol
  if (!sanitized) {
    return {
      isValid: false,
      error: 'Proje adı boş olamaz'
    };
  }
  
  // Uzunluk kontrol
  if (sanitized.length < PROJECT_NAME_RULES.minLength) {
    return {
      isValid: false,
      error: `Proje adı en az ${PROJECT_NAME_RULES.minLength} karakter olmalıdır`
    };
  }
  
  if (sanitized.length > PROJECT_NAME_RULES.maxLength) {
    return {
      isValid: false,
      error: `Proje adı en fazla ${PROJECT_NAME_RULES.maxLength} karakter olabilir`
    };
  }
  
  // Karakter kontrol
  if (!PROJECT_NAME_RULES.allowedCharsRegex.test(sanitized)) {
    return {
      isValid: false,
      error: 'Proje adı sadece harf, rakam, boşluk ve -_. karakterlerini içerebilir'
    };
  }
  
  // Sadece boşluk kontrolü
  if (sanitized.replace(/\s/g, '').length === 0) {
    return {
      isValid: false,
      error: 'Proje adı sadece boşluk karakterlerinden oluşamaz'
    };
  }
  
  return {
    isValid: true,
    sanitizedValue: sanitized
  };
}

/**
 * XSS koruması için HTML encode
 */
export function escapeHtml(text: string): string {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * Client-side rate limiting kontrolü
 */
export function checkClientRateLimit(): boolean {
  try {
    const stored = localStorage.getItem(RATE_LIMIT_KEY);
    const now = Date.now();
    
    if (!stored) {
      // İlk istek
      localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({
        count: 1,
        windowStart: now
      }));
      return true;
    }
    
    const data = JSON.parse(stored);
    const windowDuration = PROJECT_NAME_RULES.rateLimit.windowMinutes * 60 * 1000; // ms
    
    // Pencere süresi dolmuş mu?
    if (now - data.windowStart > windowDuration) {
      // Yeni pencere başlat
      localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({
        count: 1,
        windowStart: now
      }));
      return true;
    }
    
    // Limit kontrolü
    if (data.count >= PROJECT_NAME_RULES.rateLimit.maxRequests) {
      return false;
    }
    
    // Sayacı artır
    localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({
      count: data.count + 1,
      windowStart: data.windowStart
    }));
    
    return true;
  } catch (error) {
    console.warn('Rate limit check failed:', error);
    return true; // Hata durumunda izin ver
  }
}

/**
 * Rate limit reset
 */
export function resetClientRateLimit(): void {
  try {
    localStorage.removeItem(RATE_LIMIT_KEY);
  } catch (error) {
    console.warn('Rate limit reset failed:', error);
  }
}

/**
 * Debounced validation hook için utility
 */
export function createDebouncedValidator(
  validator: (value: string) => ValidationResult,
  delay: number = PROJECT_NAME_RULES.debounceMs
) {
  let timeoutId: NodeJS.Timeout;
  
  return (value: string, callback: (result: ValidationResult) => void) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      const result = validator(value);
      callback(result);
    }, delay);
  };
}

/**
 * Güvenli proje adı karşılaştırması
 */
export function isSameProjectName(name1: string, name2: string): boolean {
  const sanitized1 = sanitizeProjectName(name1).toLowerCase();
  const sanitized2 = sanitizeProjectName(name2).toLowerCase();
  return sanitized1 === sanitized2;
}

/**
 * Duplicate name kontrolü için async validation
 */
export async function validateProjectNameUnique(
  name: string,
  currentProjectId: string,
  projects: Array<{ id: string; name: string }>
): Promise<ValidationResult> {
  const sanitized = sanitizeProjectName(name);

  // Önce temel validation
  const basicValidation = validateProjectName(name);
  if (!basicValidation.isValid) {
    return basicValidation;
  }

  // Duplicate kontrolü (case-insensitive)
  const isDuplicate = projects.some(project =>
    project.id !== currentProjectId &&
    isSameProjectName(project.name, sanitized)
  );

  if (isDuplicate) {
    return {
      isValid: false,
      error: 'Bu isimde bir proje zaten mevcut'
    };
  }

  return {
    isValid: true,
    sanitizedValue: sanitized
  };
}

/**
 * Error mesajlarını kullanıcı dostu hale getir
 */
export function formatValidationError(error: string): string {
  // Teknik hataları kullanıcı dostu mesajlara çevir
  const errorMap: Record<string, string> = {
    'unique_violation': 'Bu isimde bir proje zaten mevcut',
    'check_violation': 'Proje adı geçersiz karakterler içeriyor',
    'not_null_violation': 'Proje adı boş olamaz',
    'foreign_key_violation': 'Geçersiz proje referansı',
    'RateLimitExceeded': 'Çok fazla güncelleme isteği. Lütfen bekleyin.',
    'InvalidInput': 'Geçersiz proje adı',
    'DuplicateName': 'Bu isimde bir proje zaten mevcut',
    'NotFound': 'Proje bulunamadı',
    'Unauthorized': 'Bu işlem için yetkiniz yok'
  };

  return errorMap[error] || error;
}

/**
 * Advanced debounced validator with duplicate check
 */
export function createAdvancedDebouncedValidator(
  projects: Array<{ id: string; name: string }>,
  currentProjectId: string,
  delay: number = PROJECT_NAME_RULES.debounceMs
) {
  let timeoutId: NodeJS.Timeout;

  return (value: string, callback: (result: ValidationResult) => void) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(async () => {
      const result = await validateProjectNameUnique(value, currentProjectId, projects);
      callback(result);
    }, delay);
  };
}
